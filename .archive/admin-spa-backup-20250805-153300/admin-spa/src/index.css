/* MasteryOS 管理后台全局样式 */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* React Admin 自定义样式 */
.RaLayout-content {
  padding: 24px !important;
}

.RaTopToolbar-root {
  padding: 16px 24px !important;
}

/* 卡片样式优化 */
.MuiCard-root {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}

/* 按钮样式优化 */
.RaButton-root {
  border-radius: 6px !important;
  text-transform: none !important;
  font-weight: 500 !important;
}

/* 表格样式优化 */
.RaDatagrid-table {
  border-radius: 8px !important;
  overflow: hidden !important;
}

.MuiTableHead-root {
  background-color: #fafafa !important;
}

/* 表单样式优化 */
.RaSimpleForm-root {
  padding: 24px !important;
}

.MuiTextField-root {
  margin-bottom: 16px !important;
}

/* 仪表盘样式 */
.dashboard-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 24px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1976d2;
  margin: 0;
}

.stat-label {
  font-size: 1rem;
  color: #666;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    grid-template-columns: 1fr;
    padding: 16px;
  }
  
  .dashboard-card {
    padding: 16px;
  }
  
  .RaLayout-content {
    padding: 16px !important;
  }
}