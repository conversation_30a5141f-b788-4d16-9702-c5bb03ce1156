import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  DataProvider,
  List,
  Datagrid,
  TextField,
  EmailField,
  DateField,
  Edit,
  SimpleForm,
  TextInput,
  Create,
  Show,
  SimpleShowLayout,
  DeleteButton,
  EditButton,
  ShowButton,
  BooleanField,
  SelectInput,
} from 'react-admin';
import { Dashboard } from './components/Dashboard';
import chineseMessages from 'ra-language-chinese';
import polyglotI18nProvider from 'ra-i18n-polyglot';
import { customTheme } from './theme/customTheme';
import { CustomLayout } from './components/CustomLayout';

// 创建丰富的 mock dataProvider
const dataProvider: DataProvider = {
  getList: (resource: string) => {
    // eslint-disable-next-line no-console
    console.log('getList called for:', resource);

    const mockData: Record<string, unknown[]> = {
      users: [
        {
          id: 1,
          name: '张三',
          email: 'zhang<PERSON>@example.com',
          role: 'admin',
          department: '技术部',
          active: true,
          created_at: '2024-01-15',
        },
        {
          id: 2,
          name: '李四',
          email: '<EMAIL>',
          role: 'user',
          department: '市场部',
          active: true,
          created_at: '2024-02-20',
        },
        {
          id: 3,
          name: '王五',
          email: '<EMAIL>',
          role: 'user',
          department: '产品部',
          active: false,
          created_at: '2024-03-10',
        },
        {
          id: 4,
          name: '赵六',
          email: '<EMAIL>',
          role: 'manager',
          department: '设计部',
          active: true,
          created_at: '2024-04-05',
        },
      ],
      organizations: [
        { id: 1, name: '阿里巴巴', type: '互联网公司', employees: 120000, founded: '1999-04-04' },
        { id: 2, name: '腾讯', type: '互联网公司', employees: 86000, founded: '1998-11-11' },
        { id: 3, name: '字节跳动', type: '科技公司', employees: 150000, founded: '2012-03-09' },
      ],
      skills: [
        { id: 1, name: 'React', category: '前端开发', difficulty: 'intermediate', popularity: 95 },
        {
          id: 2,
          name: 'Node.js',
          category: '后端开发',
          difficulty: 'intermediate',
          popularity: 88,
        },
        { id: 3, name: 'TypeScript', category: '编程语言', difficulty: 'advanced', popularity: 92 },
        { id: 4, name: 'Docker', category: '运维部署', difficulty: 'intermediate', popularity: 85 },
      ],
      documents: [
        {
          id: 1,
          title: 'React 入门指南',
          type: '教程',
          author: '张三',
          size: '2.5MB',
          created_at: '2024-01-20',
        },
        {
          id: 2,
          title: 'TypeScript 最佳实践',
          type: '文档',
          author: '李四',
          size: '1.8MB',
          created_at: '2024-02-15',
        },
        {
          id: 3,
          title: 'Node.js 性能优化',
          type: '技术文档',
          author: '王五',
          size: '3.2MB',
          created_at: '2024-03-05',
        },
      ],
    };

    return Promise.resolve({
      data: mockData[resource] || [],
      total: mockData[resource]?.length || 0,
    });
  },
  getOne: (resource: string, params: { id: string | number }) => {
    const mockData: Record<string, unknown> = {
      users: { id: params.id, name: '用户详情', email: '<EMAIL>', role: 'user' },
      organizations: { id: params.id, name: '组织详情', type: '公司' },
      skills: { id: params.id, name: '技能详情', category: '开发' },
      documents: { id: params.id, title: '文档详情', type: '文档' },
    };
    return Promise.resolve({ data: mockData[resource] || {} });
  },
  getMany: () => Promise.resolve({ data: [] }),
  getManyReference: () => Promise.resolve({ data: [], total: 0 }),
  create: (_resource: string, params: { data: Record<string, unknown> }) =>
    Promise.resolve({ data: { ...params.data, id: Date.now() } }),
  update: (_resource: string, params: { data: Record<string, unknown> }) =>
    Promise.resolve({ data: params.data }),
  updateMany: () => Promise.resolve({ data: [] }),
  delete: () => Promise.resolve({ data: { id: 1 } }),
  deleteMany: () => Promise.resolve({ data: [] }),
} as DataProvider;

// 中文本地化 - 扩展缺失的翻译
const customChineseMessages = {
  ...chineseMessages,
  ra: {
    ...chineseMessages.ra,
    configurable: {
      customize: '自定义',
      configureMode: '配置模式',
      inspector: {
        title: '检查器',
        content: '内容',
        reset: '重置',
        hideAll: '隐藏全部',
        showAll: '显示全部',
      },
      Datagrid: {
        title: '数据表格',
        unlabeled: '未标记',
      },
      SimpleForm: {
        title: '简单表单',
        unlabeled: '未标记',
      },
      SimpleList: {
        title: '简单列表',
        unlabeled: '未标记',
        primaryText: '主要文本',
        secondaryText: '次要文本',
        tertiaryText: '第三文本',
      },
    },
  },
};

const i18nProvider = polyglotI18nProvider(() => customChineseMessages, 'zh');

// 用户管理组件
export const UserList = () => (
  <List>
    <Datagrid>
      <TextField source="id" label="ID" />
      <TextField source="name" label="姓名" />
      <EmailField source="email" label="邮箱" />
      <TextField source="role" label="角色" />
      <TextField source="department" label="部门" />
      <BooleanField source="active" label="状态" />
      <DateField source="created_at" label="创建时间" />
      <EditButton />
      <ShowButton />
      <DeleteButton />
    </Datagrid>
  </List>
);

export const UserEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput source="name" label="姓名" required />
      <TextInput source="email" label="邮箱" required />
      <SelectInput
        source="role"
        label="角色"
        choices={[
          { id: 'admin', name: '管理员' },
          { id: 'manager', name: '经理' },
          { id: 'user', name: '用户' },
        ]}
      />
      <TextInput source="department" label="部门" />
    </SimpleForm>
  </Edit>
);

export const UserCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="name" label="姓名" required />
      <TextInput source="email" label="邮箱" required />
      <SelectInput
        source="role"
        label="角色"
        choices={[
          { id: 'admin', name: '管理员' },
          { id: 'manager', name: '经理' },
          { id: 'user', name: '用户' },
        ]}
        defaultValue="user"
      />
      <TextInput source="department" label="部门" />
    </SimpleForm>
  </Create>
);

export const UserShow = () => (
  <Show>
    <SimpleShowLayout>
      <TextField source="id" label="ID" />
      <TextField source="name" label="姓名" />
      <EmailField source="email" label="邮箱" />
      <TextField source="role" label="角色" />
      <TextField source="department" label="部门" />
      <BooleanField source="active" label="状态" />
      <DateField source="created_at" label="创建时间" />
    </SimpleShowLayout>
  </Show>
);

function App() {
  return (
    <Admin
      dataProvider={dataProvider}
      i18nProvider={i18nProvider}
      dashboard={Dashboard}
      title="MasteryOS 管理后台"
      theme={customTheme}
      layout={CustomLayout}
      requireAuth={false}
    >
      {/* 用户管理 */}
      <Resource
        name="users"
        list={UserList}
        edit={UserEdit}
        create={UserCreate}
        show={UserShow}
        options={{ label: '用户管理' }}
      />

      {/* 组织管理 */}
      <Resource
        name="organizations"
        list={ListGuesser}
        edit={EditGuesser}
        show={ShowGuesser}
        options={{ label: '组织管理' }}
      />

      {/* 技能管理 */}
      <Resource
        name="skills"
        list={ListGuesser}
        edit={EditGuesser}
        show={ShowGuesser}
        options={{ label: '技能管理' }}
      />

      {/* 文档管理 */}
      <Resource
        name="documents"
        list={ListGuesser}
        edit={EditGuesser}
        show={ShowGuesser}
        options={{ label: '文档管理' }}
      />
    </Admin>
  );
}

export default App;
