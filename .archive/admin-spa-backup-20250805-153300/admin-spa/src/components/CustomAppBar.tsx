import { AppBar, TitlePortal } from 'react-admin';
import { Box, Typography, IconButton, Avatar, Tooltip } from '@mui/material';
import {
  Settings as SettingsIcon,
  NotificationsNone as NotificationsIcon,
  Person as PersonIcon,
} from '@mui/icons-material';

export const CustomAppBar = () => {
  return (
    <AppBar
      sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        '& .RaAppBar-toolbar': {
          padding: '0 24px',
          minHeight: '64px',
        },
      }}
    >
      <TitlePortal />
      <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto', gap: 1 }}>
        <Typography
          variant="body2"
          sx={{
            color: 'rgba(255, 255, 255, 0.8)',
            mr: 2,
            display: { xs: 'none', sm: 'block' },
          }}
        >
          欢迎使用 MasteryOS 管理后台
        </Typography>

        <Tooltip title="通知">
          <IconButton sx={{ color: 'white' }}>
            <NotificationsIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="设置">
          <IconButton sx={{ color: 'white' }}>
            <SettingsIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="用户配置文件">
          <IconButton sx={{ color: 'white' }}>
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'rgba(255,255,255,0.2)' }}>
              <PersonIcon fontSize="small" />
            </Avatar>
          </IconButton>
        </Tooltip>
      </Box>
    </AppBar>
  );
};
