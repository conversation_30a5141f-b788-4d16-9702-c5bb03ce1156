import React from 'react';
import { Card, CardContent, Typography, Grid, Box } from '@mui/material';
import {
  People as PeopleIcon,
  School as SkillIcon,
  Description as DocumentIcon,
  TrendingUp as ProgressIcon,
} from '@mui/icons-material';

// 模拟数据 - 实际应该从API获取
const stats = {
  totalUsers: 156,
  totalSkills: 24,
  totalDocuments: 89,
  activeSessions: 12,
};

const StatCard = ({
  title,
  value,
  icon,
  color,
}: {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}) => (
  <Card className="dashboard-card" sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 56,
            height: 56,
            borderRadius: '50%',
            backgroundColor: `${color}20`,
            color,
            mr: 2,
          }}
        >
          {icon}
        </Box>
        <Box>
          <Typography variant="h3" className="stat-number" sx={{ color }}>
            {value}
          </Typography>
          <Typography variant="body2" className="stat-label">
            {title}
          </Typography>
        </Box>
      </Box>
    </CardContent>
  </Card>
);

export const Dashboard = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 4, fontWeight: 600 }}>
        📊 概览仪表盘
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="用户总数"
            value={stats.totalUsers}
            icon={<PeopleIcon fontSize="large" />}
            color="#1976d2"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="技能总数"
            value={stats.totalSkills}
            icon={<SkillIcon fontSize="large" />}
            color="#2e7d32"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="文档总数"
            value={stats.totalDocuments}
            icon={<DocumentIcon fontSize="large" />}
            color="#ed6c02"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="活跃会话"
            value={stats.activeSessions}
            icon={<ProgressIcon fontSize="large" />}
            color="#9c27b0"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card className="dashboard-card">
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                📈 学习趋势
              </Typography>
              <Box
                sx={{
                  height: 300,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#666',
                }}
              >
                图表组件待实现 (可使用 Recharts)
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card className="dashboard-card">
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                🚀 快速操作
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2" sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                  • 添加新用户
                </Typography>
                <Typography variant="body2" sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                  • 上传培训文档
                </Typography>
                <Typography variant="body2" sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                  • 创建学习路径
                </Typography>
                <Typography variant="body2" sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                  • 查看学习报告
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};
