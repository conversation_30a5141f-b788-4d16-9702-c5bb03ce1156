import { Menu } from 'react-admin';
import { Box, Typography, Divider } from '@mui/material';

export const CustomMenu = (props: Record<string, unknown>) => {
  return (
    <Box
      sx={{
        width: 240,
        backgroundColor: 'white',
        borderRight: '1px solid #e0e0e0',
        minHeight: '100vh',
      }}
    >
      {/* Logo 区域 */}
      <Box
        sx={{
          p: 3,
          textAlign: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <Typography variant="h6" fontWeight="bold">
          MasteryOS
        </Typography>
        <Typography variant="caption" sx={{ opacity: 0.8 }}>
          管理后台
        </Typography>
      </Box>

      <Menu {...props} />

      {/* 底部信息 */}
      <Box sx={{ position: 'absolute', bottom: 16, left: 16, right: 16 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="caption" color="text.secondary" align="center">
          © 2024 MasteryOS
        </Typography>
      </Box>
    </Box>
  );
};
