import {
  List,
  Datagrid,
  TextField,
  Date<PERSON>ield,
  EditButton,
  Edit,
  SimpleForm,
  TextInput,
  Create,
  NumberField,
  SelectInput,
} from 'react-admin';

export const DocumentList = () => (
  <List>
    <Datagrid>
      <TextField source="id" label="ID" />
      <TextField source="title" label="文档标题" />
      <TextField source="mime_type" label="文件类型" />
      <NumberField source="file_size" label="文件大小" />
      <NumberField source="page_count" label="页数" />
      <TextField source="processing_status" label="处理状态" />
      <DateField source="created_at" label="上传时间" />
      <EditButton />
    </Datagrid>
  </List>
);

export const DocumentEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput source="title" label="文档标题" required />
      <SelectInput
        source="processing_status"
        label="处理状态"
        choices={[
          { id: 'pending', name: '待处理' },
          { id: 'processing', name: '处理中' },
          { id: 'completed', name: '已完成' },
          { id: 'failed', name: '处理失败' },
        ]}
      />
    </SimpleForm>
  </Edit>
);

export const DocumentCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="title" label="文档标题" required />
      <TextInput source="file_url" label="文件URL" required />
      <TextInput source="mime_type" label="文件类型" />
    </SimpleForm>
  </Create>
);
