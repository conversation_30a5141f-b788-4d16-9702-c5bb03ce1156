import {
  List,
  Datagrid,
  TextField,
  EmailField,
  DateField,
  EditButton,
  Edit,
  SimpleForm,
  TextInput,
  Create,
  SelectInput,
  BooleanField,
  BooleanInput,
} from 'react-admin';

export const UserList = () => (
  <List>
    <Datagrid>
      <TextField source="id" label="ID" />
      <TextField source="name" label="姓名" />
      <EmailField source="email" label="邮箱" />
      <TextField source="role" label="角色" />
      <TextField source="department" label="部门" />
      <BooleanField source="status" label="状态" />
      <DateField source="created_at" label="创建时间" />
      <EditButton />
    </Datagrid>
  </List>
);

export const UserEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput source="name" label="姓名" required />
      <TextInput source="email" label="邮箱" required />
      <SelectInput
        source="role"
        label="角色"
        choices={[
          { id: 'learner', name: '学习者' },
          { id: 'trainer', name: '培训师' },
          { id: 'admin', name: '管理员' },
        ]}
      />
      <TextInput source="department" label="部门" />
      <BooleanInput source="status" label="激活状态" />
    </SimpleForm>
  </Edit>
);

export const UserCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="name" label="姓名" required />
      <TextInput source="email" label="邮箱" required />
      <TextInput source="password" label="密码" type="password" required />
      <SelectInput
        source="role"
        label="角色"
        choices={[
          { id: 'learner', name: '学习者' },
          { id: 'trainer', name: '培训师' },
          { id: 'admin', name: '管理员' },
        ]}
        defaultValue="learner"
      />
      <TextInput source="department" label="部门" />
    </SimpleForm>
  </Create>
);
