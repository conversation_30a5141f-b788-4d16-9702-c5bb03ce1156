version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: masteryos-postgres
    environment:
      POSTGRES_DB: masteryos
      POSTGRES_USER: masteryos
      POSTGRES_PASSWORD: ${DB_PASSWORD:-masteryos_prod_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U masteryos"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - masteryos-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: masteryos-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - masteryos-network
    restart: unless-stopped

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: masteryos-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY:-minioadmin123}
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - masteryos-network
    restart: unless-stopped

  # ChromaDB向量数据库
  chromadb:
    image: chromadb/chroma:latest
    container_name: masteryos-chromadb
    environment:
      CHROMA_SERVER_HOST: 0.0.0.0
      CHROMA_SERVER_HTTP_PORT: 8000
    ports:
      - "8000:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - masteryos-network
    restart: unless-stopped

  # 统一BFF API
  unified-bff:
    build:
      context: ./apps/unified-bff
      dockerfile: Dockerfile
    container_name: masteryos-unified-bff
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://masteryos:${DB_PASSWORD:-masteryos_prod_password}@postgres:5432/masteryos
      REDIS_URL: redis://redis:6379
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY:-minioadmin}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY:-minioadmin123}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      OPENAI_MODEL: ${OPENAI_MODEL:-gpt-3.5-turbo}
      OPENAI_EMBEDDING_MODEL: ${OPENAI_EMBEDDING_MODEL:-text-embedding-3-small}
      CHROMADB_URL: http://chromadb:8000
      AI_ENABLED: ${AI_ENABLED:-true}
    ports:
      - "3102:3102"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - masteryos-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Flutter Web Admin
  admin-web:
    build:
      context: ./apps/admin-web
      dockerfile: Dockerfile
    container_name: masteryos-admin-web
    ports:
      - "3200:80"
    depends_on:
      - admin-bff
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - masteryos-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: masteryos-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - admin-web
      - unified-bff
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - masteryos-network
    restart: unless-stopped

networks:
  masteryos-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  chromadb_data:
    driver: local