import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from '../src/users/users.module';
import { User, UserRole, UserStatus } from '../src/users/entities/user.entity';

describe('Users (e2e)', () => {
  let app: INestApplication;
  let userRepository;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        UsersModule,
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [User],
          synchronize: true,
        }),
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get('UserRepository');
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/users (POST)', () => {
    it('should create a new user', () => {
      const createUserDto = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123',
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
      };

      return request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.email).toBe(createUserDto.email);
          expect(res.body.username).toBe(createUserDto.username);
          expect(res.body.role).toBe(createUserDto.role);
          expect(res.body.status).toBe(createUserDto.status);
          expect(res.body.password).toBeUndefined(); // 密码不应该返回
        });
    });

    it('should fail with invalid email', () => {
      const createUserDto = {
        email: 'invalid-email',
        username: 'testuser',
        password: 'password123',
      };

      return request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(400);
    });

    it('should fail with duplicate email', async () => {
      // 先创建一个用户
      const user = userRepository.create({
        email: '<EMAIL>',
        username: 'existing',
        password: 'hashedpassword',
      });
      await userRepository.save(user);

      const createUserDto = {
        email: '<EMAIL>',
        username: 'newuser',
        password: 'password123',
      };

      return request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(409); // Conflict
    });
  });

  describe('/users (GET)', () => {
    beforeEach(async () => {
      // 创建测试数据
      const users = [
        {
          email: '<EMAIL>',
          username: 'user1',
          password: 'hashedpassword',
          role: UserRole.USER,
          status: UserStatus.ACTIVE,
        },
        {
          email: '<EMAIL>',
          username: 'admin',
          password: 'hashedpassword',
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
        },
        {
          email: '<EMAIL>',
          username: 'inactive',
          password: 'hashedpassword',
          role: UserRole.USER,
          status: UserStatus.INACTIVE,
        },
      ];

      for (const userData of users) {
        const user = userRepository.create(userData);
        await userRepository.save(user);
      }
    });

    it('should return paginated users', () => {
      return request(app.getHttpServer())
        .get('/users')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(3);
          expect(res.body.pagination).toBeDefined();
          expect(res.body.pagination.total).toBe(3);
        });
    });

    it('should filter users by role', () => {
      return request(app.getHttpServer())
        .get('/users?role=admin')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].role).toBe(UserRole.ADMIN);
        });
    });

    it('should search users by username', () => {
      return request(app.getHttpServer())
        .get('/users?search=admin')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].username).toBe('admin');
        });
    });
  });

  describe('/users/stats (GET)', () => {
    beforeEach(async () => {
      const users = [
        { email: '<EMAIL>', username: 'u1', password: 'pass', role: UserRole.USER, status: UserStatus.ACTIVE },
        { email: '<EMAIL>', username: 'u2', password: 'pass', role: UserRole.ADMIN, status: UserStatus.ACTIVE },
        { email: '<EMAIL>', username: 'u3', password: 'pass', role: UserRole.USER, status: UserStatus.INACTIVE },
      ];

      for (const userData of users) {
        const user = userRepository.create(userData);
        await userRepository.save(user);
      }
    });

    it('should return user statistics', () => {
      return request(app.getHttpServer())
        .get('/users/stats')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('totalUsers', 3);
          expect(res.body).toHaveProperty('activeUsers', 2);
          expect(res.body).toHaveProperty('adminUsers', 1);
        });
    });
  });
});