import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentsModule } from '../src/documents/documents.module';
import { Document, DocumentType, DocumentStatus, AccessLevel } from '../src/documents/entities/document.entity';

describe('Documents (e2e)', () => {
  let app: INestApplication;
  let documentRepository;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        DocumentsModule,
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Document],
          synchronize: true,
        }),
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    documentRepository = moduleFixture.get('DocumentRepository');
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/documents (POST)', () => {
    it('should create a new document', () => {
      const createDocumentDto = {
        title: 'Test Document',
        description: 'A test document',
        fileName: 'test.pdf',
        filePath: '/uploads/test.pdf',
        fileSize: 1024,
        type: DocumentType.PDF,
        status: DocumentStatus.PUBLISHED,
        accessLevel: AccessLevel.PUBLIC,
      };

      return request(app.getHttpServer())
        .post('/documents')
        .send(createDocumentDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.title).toBe(createDocumentDto.title);
          expect(res.body.fileName).toBe(createDocumentDto.fileName);
          expect(res.body.type).toBe(createDocumentDto.type);
          expect(res.body.status).toBe(createDocumentDto.status);
        });
    });

    it('should fail with missing required fields', () => {
      const incompleteDto = {
        title: 'Test Document',
        // 缺少必需字段
      };

      return request(app.getHttpServer())
        .post('/documents')
        .send(incompleteDto)
        .expect(400);
    });
  });

  describe('/documents (GET)', () => {
    beforeEach(async () => {
      const documents = [
        {
          title: 'PDF Document',
          fileName: 'doc1.pdf',
          filePath: '/uploads/doc1.pdf',
          fileSize: 1024,
          type: DocumentType.PDF,
          status: DocumentStatus.PUBLISHED,
          accessLevel: AccessLevel.PUBLIC,
          createdBy: 'user1',
          viewCount: 10,
          downloadCount: 5,
          tags: ['test', 'pdf'],
          isSearchable: true,
        },
        {
          title: 'Word Document',
          fileName: 'doc2.docx',
          filePath: '/uploads/doc2.docx',
          fileSize: 2048,
          type: DocumentType.WORD,
          status: DocumentStatus.DRAFT,
          accessLevel: AccessLevel.INTERNAL,
          createdBy: 'user2',
          viewCount: 0,
          downloadCount: 0,
          tags: ['draft', 'word'],
          isSearchable: true,
        },
      ];

      for (const docData of documents) {
        const doc = documentRepository.create(docData);
        await documentRepository.save(doc);
      }
    });

    it('should return paginated documents', () => {
      return request(app.getHttpServer())
        .get('/documents')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(2);
          expect(res.body.pagination).toBeDefined();
          expect(res.body.pagination.total).toBe(2);
        });
    });

    it('should filter documents by type', () => {
      return request(app.getHttpServer())
        .get('/documents?type=pdf')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].type).toBe(DocumentType.PDF);
        });
    });

    it('should filter documents by status', () => {
      return request(app.getHttpServer())
        .get('/documents?status=published')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].status).toBe(DocumentStatus.PUBLISHED);
        });
    });

    it('should search documents by title', () => {
      return request(app.getHttpServer())
        .get('/documents?search=PDF')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].title).toContain('PDF');
        });
    });
  });

  describe('/documents/stats (GET)', () => {
    beforeEach(async () => {
      const documents = [
        {
          title: 'Doc 1', fileName: 'doc1.pdf', filePath: '/doc1.pdf', fileSize: 1024,
          type: DocumentType.PDF, status: DocumentStatus.PUBLISHED, accessLevel: AccessLevel.PUBLIC,
          createdBy: 'user1', viewCount: 10, downloadCount: 5, tags: [], isSearchable: true,
        },
        {
          title: 'Doc 2', fileName: 'doc2.docx', filePath: '/doc2.docx', fileSize: 2048,
          type: DocumentType.WORD, status: DocumentStatus.DRAFT, accessLevel: AccessLevel.INTERNAL,
          createdBy: 'user1', viewCount: 3, downloadCount: 1, tags: [], isSearchable: true,
        },
        {
          title: 'Doc 3', fileName: 'doc3.xlsx', filePath: '/doc3.xlsx', fileSize: 512,
          type: DocumentType.EXCEL, status: DocumentStatus.PUBLISHED, accessLevel: AccessLevel.PUBLIC,
          createdBy: 'user2', viewCount: 7, downloadCount: 2, tags: [], isSearchable: true,
        },
      ];

      for (const docData of documents) {
        const doc = documentRepository.create(docData);
        await documentRepository.save(doc);
      }
    });

    it('should return document statistics', () => {
      return request(app.getHttpServer())
        .get('/documents/stats')
        .expect(200)
        .expect((res) => {
          expect(res.body.overview).toHaveProperty('totalDocuments', 3);
          expect(res.body.overview).toHaveProperty('publishedDocuments', 2);
          expect(res.body.overview).toHaveProperty('draftDocuments', 1);
          expect(res.body.activity).toHaveProperty('totalViews', 20);
          expect(res.body.activity).toHaveProperty('totalDownloads', 8);
          expect(res.body.distribution.byType).toHaveLength(3);
        });
    });
  });

  describe('/documents/search (GET)', () => {
    beforeEach(async () => {
      const documents = [
        {
          title: 'React Development Guide',
          content: 'Learn React hooks and components',
          fileName: 'react-guide.pdf', filePath: '/react-guide.pdf', fileSize: 1024,
          type: DocumentType.PDF, status: DocumentStatus.PUBLISHED, accessLevel: AccessLevel.PUBLIC,
          createdBy: 'user1', viewCount: 0, downloadCount: 0, tags: [], isSearchable: true,
        },
        {
          title: 'Flutter Mobile App',
          content: 'Build mobile apps with Flutter',
          fileName: 'flutter-app.docx', filePath: '/flutter-app.docx', fileSize: 2048,
          type: DocumentType.WORD, status: DocumentStatus.PUBLISHED, accessLevel: AccessLevel.PUBLIC,
          createdBy: 'user2', viewCount: 0, downloadCount: 0, tags: [], isSearchable: true,
        },
      ];

      for (const docData of documents) {
        const doc = documentRepository.create(docData);
        await documentRepository.save(doc);
      }
    });

    it('should search documents by content', () => {
      return request(app.getHttpServer())
        .get('/documents/search?q=React')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(1);
          expect(res.body[0].title).toContain('React');
        });
    });

    it('should return empty array for no matches', () => {
      return request(app.getHttpServer())
        .get('/documents/search?q=nonexistent')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(0);
        });
    });
  });
});