import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum AnalyticsEventType {
  LOGIN = 'login',
  LOGOUT = 'logout',
  DOCUMENT_VIEW = 'document_view',
  DOCUMENT_DOWNLOAD = 'document_download',
  SEARCH = 'search',
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  SESSION_START = 'session_start',
  SESSION_END = 'session_end',
}

@Entity('analytics_events')
@Index(['eventType'])
@Index(['userId'])
@Index(['createdAt'])
@Index(['organizationId'])
export class AnalyticsEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: AnalyticsEventType,
  })
  eventType: AnalyticsEventType;

  @Column({ nullable: true })
  userId?: string;

  @Column({ nullable: true })
  organizationId?: string;

  @Column({ nullable: true })
  sessionId?: string;

  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @Column({ nullable: true })
  ipAddress?: string;

  @Column({ nullable: true })
  userAgent?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('daily_stats')
@Index(['date'])
@Index(['organizationId'])
export class DailyStats {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({ nullable: true })
  organizationId?: string;

  @Column({ default: 0 })
  totalUsers: number;

  @Column({ default: 0 })
  activeUsers: number;

  @Column({ default: 0 })
  newUsers: number;

  @Column({ default: 0 })
  totalSessions: number;

  @Column({ default: 0 })
  totalDocuments: number;

  @Column({ default: 0 })
  documentsViewed: number;

  @Column({ default: 0 })
  documentsDownloaded: number;

  @Column({ default: 0 })
  totalSearches: number;

  @Column('jsonb', { nullable: true })
  additionalMetrics?: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}