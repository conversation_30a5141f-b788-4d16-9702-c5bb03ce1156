import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Req,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AnalyticsService } from './analytics.service';
import { AnalyticsQueryDto, TimeRangeDto, CreateAnalyticsEventDto } from './dto/analytics-query.dto';

@ApiTags('Analytics')
@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Post('events')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建分析事件' })
  @ApiResponse({ status: 201, description: '事件创建成功' })
  async createEvent(
    @Body() createEventDto: CreateAnalyticsEventDto,
    @Req() request: any,
  ) {
    return await this.analyticsService.createEvent(createEventDto, request);
  }

  @Get('dashboard')
  @ApiOperation({ summary: '获取仪表盘统计数据' })
  @ApiResponse({ status: 200, description: '仪表盘数据获取成功' })
  async getDashboardStats(@Query() timeRangeDto: TimeRangeDto) {
    return await this.analyticsService.getDashboardStats(timeRangeDto);
  }

  @Get('user-activity-trend')
  @ApiOperation({ summary: '获取用户活动趋势' })
  @ApiResponse({ status: 200, description: '用户活动趋势数据获取成功' })
  async getUserActivityTrend(@Query() timeRangeDto: TimeRangeDto) {
    return await this.analyticsService.getUserActivityTrend(timeRangeDto);
  }

  @Get('event-distribution')
  @ApiOperation({ summary: '获取事件类型分布' })
  @ApiResponse({ status: 200, description: '事件分布数据获取成功' })
  async getEventTypeDistribution(@Query() timeRangeDto: TimeRangeDto) {
    return await this.analyticsService.getEventTypeDistribution(timeRangeDto);
  }

  @Get('top-active-users')
  @ApiOperation({ summary: '获取最活跃用户' })
  @ApiResponse({ status: 200, description: '活跃用户数据获取成功' })
  async getTopActiveUsers(@Query() timeRangeDto: TimeRangeDto) {
    return await this.analyticsService.getTopActiveUsers(timeRangeDto);
  }

  @Get('hourly-pattern')
  @ApiOperation({ summary: '获取每小时活动模式' })
  @ApiResponse({ status: 200, description: '活动模式数据获取成功' })
  async getHourlyActivityPattern(@Query() timeRangeDto: TimeRangeDto) {
    return await this.analyticsService.getHourlyActivityPattern(timeRangeDto);
  }

  @Get('events')
  @ApiOperation({ summary: '获取分析事件列表' })
  @ApiResponse({ status: 200, description: '事件列表获取成功' })
  async getEvents(@Query() queryDto: AnalyticsQueryDto) {
    return await this.analyticsService.getEvents(queryDto);
  }

  @Post('daily-stats/:date')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '生成指定日期的每日统计' })
  @ApiResponse({ status: 201, description: '每日统计生成成功' })
  async generateDailyStats(
    @Query('date') date: string,
    @Query('organizationId') organizationId?: string,
  ) {
    const targetDate = new Date(date);
    return await this.analyticsService.generateDailyStats(targetDate, organizationId);
  }
}