import { IsOptional, IsDateString, Is<PERSON>num, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { AnalyticsEventType } from '../entities/analytics.entity';

export class AnalyticsQueryDto {
  @ApiProperty({ required: false, description: '开始日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ required: false, description: '结束日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ required: false, description: '组织ID' })
  @IsOptional()
  @IsString()
  organizationId?: string;

  @ApiProperty({ required: false, description: '用户ID' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({ required: false, enum: AnalyticsEventType, description: '事件类型' })
  @IsOptional()
  @IsEnum(AnalyticsEventType)
  eventType?: AnalyticsEventType;
}

export class TimeRangeDto {
  @ApiProperty({ description: '时间范围（天数）', example: 30 })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(365)
  days: number = 30;

  @ApiProperty({ required: false, description: '组织ID' })
  @IsOptional()
  @IsString()
  organizationId?: string;
}

export class CreateAnalyticsEventDto {
  @ApiProperty({ enum: AnalyticsEventType, description: '事件类型' })
  @IsEnum(AnalyticsEventType)
  eventType: AnalyticsEventType;

  @ApiProperty({ required: false, description: '用户ID' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({ required: false, description: '组织ID' })
  @IsOptional()
  @IsString()
  organizationId?: string;

  @ApiProperty({ required: false, description: '会话ID' })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiProperty({ required: false, description: '事件元数据' })
  @IsOptional()
  metadata?: Record<string, any>;
}