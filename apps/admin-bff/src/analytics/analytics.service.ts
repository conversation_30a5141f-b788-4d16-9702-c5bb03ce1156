import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual } from 'typeorm';
import { AnalyticsEvent, DailyStats, AnalyticsEventType } from './entities/analytics.entity';
import { User, UserStatus, UserRole } from '../users/entities/user.entity';
import { AnalyticsQueryDto, TimeRangeDto, CreateAnalyticsEventDto } from './dto/analytics-query.dto';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(AnalyticsEvent)
    private analyticsEventRepository: Repository<AnalyticsEvent>,
    @InjectRepository(DailyStats)
    private dailyStatsRepository: Repository<DailyStats>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  async createEvent(createEventDto: CreateAnalyticsEventDto, request?: any) {
    const event = this.analyticsEventRepository.create({
      ...createEventDto,
      ipAddress: request?.ip,
      userAgent: request?.get('User-Agent'),
    });

    return await this.analyticsEventRepository.save(event);
  }

  async getDashboardStats(timeRangeDto: TimeRangeDto) {
    const { days, organizationId } = timeRangeDto;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 基础用户统计
    const [totalUsers, activeUsers, adminUsers, newUsers] = await Promise.all([
      this.usersRepository.count({
        where: organizationId ? { organizationId } : {},
      }),
      this.usersRepository.count({
        where: {
          status: UserStatus.ACTIVE,
          ...(organizationId && { organizationId }),
        },
      }),
      this.usersRepository.count({
        where: {
          role: UserRole.ADMIN,
          ...(organizationId && { organizationId }),
        },
      }),
      this.usersRepository.count({
        where: {
          createdAt: MoreThanOrEqual(startDate),
          ...(organizationId && { organizationId }),
        },
      }),
    ]);

    // 活动统计
    const [totalSessions, totalEvents, documentViews, searches] = await Promise.all([
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.SESSION_START,
          createdAt: MoreThanOrEqual(startDate),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          createdAt: MoreThanOrEqual(startDate),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.DOCUMENT_VIEW,
          createdAt: MoreThanOrEqual(startDate),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.SEARCH,
          createdAt: MoreThanOrEqual(startDate),
          ...(organizationId && { organizationId }),
        },
      }),
    ]);

    // 计算增长率
    const prevStartDate = new Date(startDate);
    prevStartDate.setDate(prevStartDate.getDate() - days);

    const [prevNewUsers, prevSessions, prevDocumentViews] = await Promise.all([
      this.usersRepository.count({
        where: {
          createdAt: Between(prevStartDate, startDate),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.SESSION_START,
          createdAt: Between(prevStartDate, startDate),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.DOCUMENT_VIEW,
          createdAt: Between(prevStartDate, startDate),
          ...(organizationId && { organizationId }),
        },
      }),
    ]);

    const calculateGrowthRate = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      overview: {
        totalUsers,
        activeUsers,
        adminUsers,
        newUsers,
        newUsersGrowth: calculateGrowthRate(newUsers, prevNewUsers),
      },
      activity: {
        totalSessions,
        totalEvents,
        documentViews,
        searches,
        sessionsGrowth: calculateGrowthRate(totalSessions, prevSessions),
        documentViewsGrowth: calculateGrowthRate(documentViews, prevDocumentViews),
      },
      timeRange: {
        days,
        startDate,
        endDate: new Date(),
      },
    };
  }

  async getUserActivityTrend(timeRangeDto: TimeRangeDto) {
    const { days, organizationId } = timeRangeDto;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const query = `
      SELECT 
        DATE(created_at) as date,
        COUNT(DISTINCT user_id) as active_users,
        COUNT(*) as total_events
      FROM analytics_events 
      WHERE created_at >= $1 AND created_at <= $2
      ${organizationId ? 'AND organization_id = $3' : ''}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;

    const params = organizationId 
      ? [startDate, endDate, organizationId]
      : [startDate, endDate];

    const result = await this.analyticsEventRepository.query(query, params);
    
    return result.map(row => ({
      date: row.date,
      activeUsers: parseInt(row.active_users),
      totalEvents: parseInt(row.total_events),
    }));
  }

  async getEventTypeDistribution(timeRangeDto: TimeRangeDto) {
    const { days, organizationId } = timeRangeDto;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const query = `
      SELECT 
        event_type,
        COUNT(*) as count
      FROM analytics_events 
      WHERE created_at >= $1
      ${organizationId ? 'AND organization_id = $2' : ''}
      GROUP BY event_type
      ORDER BY count DESC
    `;

    const params = organizationId ? [startDate, organizationId] : [startDate];
    const result = await this.analyticsEventRepository.query(query, params);
    
    return result.map(row => ({
      eventType: row.event_type,
      count: parseInt(row.count),
    }));
  }

  async getTopActiveUsers(timeRangeDto: TimeRangeDto) {
    const { days, organizationId } = timeRangeDto;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const query = `
      SELECT 
        ae.user_id,
        u.username,
        u.email,
        COUNT(*) as activity_count,
        COUNT(DISTINCT DATE(ae.created_at)) as active_days
      FROM analytics_events ae
      LEFT JOIN users u ON ae.user_id = u.id
      WHERE ae.created_at >= $1 AND ae.user_id IS NOT NULL
      ${organizationId ? 'AND ae.organization_id = $2' : ''}
      GROUP BY ae.user_id, u.username, u.email
      ORDER BY activity_count DESC
      LIMIT 10
    `;

    const params = organizationId ? [startDate, organizationId] : [startDate];
    const result = await this.analyticsEventRepository.query(query, params);
    
    return result.map(row => ({
      userId: row.user_id,
      username: row.username,
      email: row.email,
      activityCount: parseInt(row.activity_count),
      activeDays: parseInt(row.active_days),
    }));
  }

  async getHourlyActivityPattern(timeRangeDto: TimeRangeDto) {
    const { days, organizationId } = timeRangeDto;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const query = `
      SELECT 
        EXTRACT(HOUR FROM created_at) as hour,
        COUNT(*) as event_count,
        COUNT(DISTINCT user_id) as unique_users
      FROM analytics_events 
      WHERE created_at >= $1
      ${organizationId ? 'AND organization_id = $2' : ''}
      GROUP BY EXTRACT(HOUR FROM created_at)
      ORDER BY hour ASC
    `;

    const params = organizationId ? [startDate, organizationId] : [startDate];
    const result = await this.analyticsEventRepository.query(query, params);
    
    // 确保所有24小时都有数据
    const hourlyData = Array.from({ length: 24 }, (_, hour) => {
      const found = result.find(row => parseInt(row.hour) === hour);
      return {
        hour,
        eventCount: found ? parseInt(found.event_count) : 0,
        uniqueUsers: found ? parseInt(found.unique_users) : 0,
      };
    });

    return hourlyData;
  }

  async getEvents(queryDto: AnalyticsQueryDto) {
    const { startDate, endDate, organizationId, userId, eventType } = queryDto;
    const whereConditions: any = {};

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) whereConditions.createdAt = { ...whereConditions.createdAt, ...MoreThanOrEqual(new Date(startDate)) };
      if (endDate) whereConditions.createdAt = { ...whereConditions.createdAt, ...{ lessThanOrEqual: new Date(endDate) } };
    }
    
    if (organizationId) whereConditions.organizationId = organizationId;
    if (userId) whereConditions.userId = userId;
    if (eventType) whereConditions.eventType = eventType;

    return await this.analyticsEventRepository.find({
      where: whereConditions,
      order: { createdAt: 'DESC' },
      take: 100,
    });
  }

  async generateDailyStats(date: Date, organizationId?: string) {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const [
      totalUsers,
      activeUsers,
      newUsers,
      totalSessions,
      documentsViewed,
      documentsDownloaded,
      totalSearches,
    ] = await Promise.all([
      this.usersRepository.count({
        where: organizationId ? { organizationId } : {},
      }),
      this.usersRepository.count({
        where: {
          status: UserStatus.ACTIVE,
          ...(organizationId && { organizationId }),
        },
      }),
      this.usersRepository.count({
        where: {
          createdAt: Between(startOfDay, endOfDay),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.SESSION_START,
          createdAt: Between(startOfDay, endOfDay),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.DOCUMENT_VIEW,
          createdAt: Between(startOfDay, endOfDay),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.DOCUMENT_DOWNLOAD,
          createdAt: Between(startOfDay, endOfDay),
          ...(organizationId && { organizationId }),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: AnalyticsEventType.SEARCH,
          createdAt: Between(startOfDay, endOfDay),
          ...(organizationId && { organizationId }),
        },
      }),
    ]);

    const dailyStats = this.dailyStatsRepository.create({
      date: startOfDay,
      organizationId,
      totalUsers,
      activeUsers,
      newUsers,
      totalSessions,
      documentsViewed,
      documentsDownloaded,
      totalSearches,
    });

    return await this.dailyStatsRepository.save(dailyStats);
  }
}