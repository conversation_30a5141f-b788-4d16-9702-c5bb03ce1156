import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { DocumentsModule } from './documents/documents.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      url: process.env['DATABASE_URL'] || 'postgresql://masteryos:masteryos@localhost:8182/masteryos',
      autoLoadEntities: true,
      synchronize: process.env['NODE_ENV'] === 'development',
      logging: process.env['NODE_ENV'] === 'development',
    }),
    UsersModule,
    AnalyticsModule,
    DocumentsModule,
    HealthModule,
    // TODO: 添加其他模块
    // AuthModule,
    // OrganizationsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
