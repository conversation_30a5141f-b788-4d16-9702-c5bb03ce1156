import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class HealthService {
  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  async getHealthStatus() {
    const startTime = Date.now();
    
    try {
      const checks = await Promise.allSettled([
        this.checkDatabase(),
        this.checkMemory(),
        this.checkDisk(),
      ]);

      const database = checks[0].status === 'fulfilled' ? checks[0].value : { status: 'error', error: checks[0].reason?.message };
      const memory = checks[1].status === 'fulfilled' ? checks[1].value : { status: 'error', error: checks[1].reason?.message };
      const disk = checks[2].status === 'fulfilled' ? checks[2].value : { status: 'error', error: checks[2].reason?.message };

      const overallStatus = [database, memory, disk].every(check => check.status === 'healthy') ? 'healthy' : 'unhealthy';

      return {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: Date.now() - startTime,
        version: process.env.npm_package_version || '1.0.0',
        checks: {
          database,
          memory,
          disk,
        },
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
        responseTime: Date.now() - startTime,
      };
    }
  }

  async getReadinessStatus() {
    try {
      // 检查关键依赖是否就绪
      const dbCheck = await this.checkDatabase();
      
      if (dbCheck.status !== 'healthy') {
        return {
          status: 'not ready',
          reason: 'Database not ready',
          timestamp: new Date().toISOString(),
        };
      }

      return {
        status: 'ready',
        timestamp: new Date().toISOString(),
        checks: {
          database: dbCheck,
        },
      };
    } catch (error) {
      return {
        status: 'not ready',
        reason: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async getLivenessStatus() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid,
      memory: process.memoryUsage(),
    };
  }

  private async checkDatabase() {
    try {
      const startTime = Date.now();
      await this.dataSource.query('SELECT 1');
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        responseTime,
        message: 'Database connection successful',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        message: 'Database connection failed',
      };
    }
  }

  private async checkMemory() {
    try {
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      const memoryUsagePercent = (usedMemory / totalMemory) * 100;

      // 如果内存使用超过90%，标记为不健康
      const status = memoryUsagePercent > 90 ? 'unhealthy' : 'healthy';

      return {
        status,
        usage: {
          used: Math.round(usedMemory / 1024 / 1024), // MB
          total: Math.round(totalMemory / 1024 / 1024), // MB
          percentage: Math.round(memoryUsagePercent),
        },
        message: `Memory usage: ${Math.round(memoryUsagePercent)}%`,
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        message: 'Memory check failed',
      };
    }
  }

  private async checkDisk() {
    try {
      // 简单的磁盘检查 - 在生产环境中应该检查实际的磁盘空间
      return {
        status: 'healthy',
        message: 'Disk space sufficient',
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        message: 'Disk check failed',
      };
    }
  }
}