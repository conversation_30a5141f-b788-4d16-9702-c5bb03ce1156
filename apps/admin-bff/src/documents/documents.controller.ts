import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  Res,
  StreamableFile,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { createReadStream } from 'fs';
import { join } from 'path';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { DocumentQueryDto } from './dto/document-query.dto';

@ApiTags('Documents')
@Controller('documents')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建文档' })
  @ApiResponse({ status: 201, description: '文档创建成功' })
  async create(
    @Body() createDocumentDto: CreateDocumentDto,
    // TODO: 从JWT token中获取userId
    // @CurrentUser() user: User,
  ) {
    const userId = 'mock-user-id'; // 临时Mock用户ID
    return await this.documentsService.create(createDocumentDto, userId);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '上传文档文件' })
  @ApiResponse({ status: 201, description: '文件上传成功' })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() metadata: any,
  ) {
    if (!file) {
      throw new Error('请选择要上传的文件');
    }

    // TODO: 实现文件上传逻辑，保存到MinIO或本地存储
    // 这里返回模拟的文件信息
    return {
      fileName: file.originalname,
      filePath: `/uploads/${Date.now()}-${file.originalname}`,
      fileSize: file.size,
      mimeType: file.mimetype,
      message: '文件上传成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取文档列表' })
  @ApiResponse({ status: 200, description: '文档列表获取成功' })
  async findAll(@Query() queryDto: DocumentQueryDto) {
    return await this.documentsService.findAll(queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取文档统计信息' })
  @ApiResponse({ status: 200, description: '统计信息获取成功' })
  async getStats(@Query('organizationId') organizationId?: string) {
    return await this.documentsService.getStats(organizationId);
  }

  @Get('search')
  @ApiOperation({ summary: '全文搜索文档' })
  @ApiResponse({ status: 200, description: '搜索结果获取成功' })
  async searchContent(
    @Query('q') query: string,
    @Query('organizationId') organizationId?: string,
  ) {
    return await this.documentsService.searchContent(query, organizationId);
  }

  @Get('popular')
  @ApiOperation({ summary: '获取热门文档' })
  @ApiResponse({ status: 200, description: '热门文档获取成功' })
  async getPopularDocuments(
    @Query('organizationId') organizationId?: string,
    @Query('limit') limit?: number,
  ) {
    return await this.documentsService.getPopularDocuments(
      organizationId,
      limit ? parseInt(limit.toString()) : 10,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: '获取文档详情' })
  @ApiResponse({ status: 200, description: '文档详情获取成功' })
  async findOne(
    @Param('id') id: string,
    // TODO: 从JWT token中获取userId（可选）
  ) {
    const userId = 'mock-user-id'; // 临时Mock用户ID
    return await this.documentsService.findOne(id, userId);
  }

  @Get(':id/download')
  @ApiOperation({ summary: '下载文档' })
  @ApiResponse({ status: 200, description: '文档下载成功' })
  async downloadDocument(
    @Param('id') id: string,
    @Res({ passthrough: true }) res: Response,
  ) {
    const userId = 'mock-user-id'; // 临时Mock用户ID
    const fileInfo = await this.documentsService.download(id, userId);

    // TODO: 实现实际的文件下载逻辑
    // 这里返回模拟的文件流
    res.set({
      'Content-Type': fileInfo.mimeType || 'application/octet-stream',
      'Content-Disposition': `attachment; filename="${fileInfo.fileName}"`,
    });

    // 模拟文件内容
    const mockContent = `Mock file content for ${fileInfo.fileName}`;
    return new StreamableFile(Buffer.from(mockContent));
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新文档' })
  @ApiResponse({ status: 200, description: '文档更新成功' })
  async update(
    @Param('id') id: string,
    @Body() updateDocumentDto: UpdateDocumentDto,
  ) {
    const userId = 'mock-user-id'; // 临时Mock用户ID
    return await this.documentsService.update(id, updateDocumentDto, userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除文档' })
  @ApiResponse({ status: 204, description: '文档删除成功' })
  async remove(@Param('id') id: string) {
    const userId = 'mock-user-id'; // 临时Mock用户ID
    return await this.documentsService.remove(id, userId);
  }
}