import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between, MoreThanOrEqual, LessThanOrEqual, In } from 'typeorm';
import { Document, DocumentStatus, AccessLevel } from './entities/document.entity';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { DocumentQueryDto } from './dto/document-query.dto';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Document)
    private documentsRepository: Repository<Document>,
  ) {}

  async create(createDocumentDto: CreateDocumentDto, userId: string) {
    const document = this.documentsRepository.create({
      ...createDocumentDto,
      createdBy: userId,
    });

    return await this.documentsRepository.save(document);
  }

  async findAll(queryDto: DocumentQueryDto) {
    const {
      page = 1,
      pageSize = 20,
      search,
      type,
      status,
      accessLevel,
      organizationId,
      createdBy,
      tags,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      startDate,
      endDate,
      minSize,
      maxSize,
    } = queryDto;

    const queryBuilder = this.documentsRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.creator', 'creator')
      .leftJoinAndSelect('document.updater', 'updater');

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(document.title ILIKE :search OR document.description ILIKE :search OR document.content ILIKE :search OR document.fileName ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // 过滤条件
    if (type) {
      queryBuilder.andWhere('document.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('document.status = :status', { status });
    }

    if (accessLevel) {
      queryBuilder.andWhere('document.accessLevel = :accessLevel', { accessLevel });
    }

    if (organizationId) {
      queryBuilder.andWhere('document.organizationId = :organizationId', { organizationId });
    }

    if (createdBy) {
      queryBuilder.andWhere('document.createdBy = :createdBy', { createdBy });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere('document.tags && :tags', { tags });
    }

    // 日期范围
    if (startDate) {
      queryBuilder.andWhere('document.createdAt >= :startDate', { startDate: new Date(startDate) });
    }

    if (endDate) {
      queryBuilder.andWhere('document.createdAt <= :endDate', { endDate: new Date(endDate) });
    }

    // 文件大小范围
    if (minSize !== undefined) {
      queryBuilder.andWhere('document.fileSize >= :minSize', { minSize });
    }

    if (maxSize !== undefined) {
      queryBuilder.andWhere('document.fileSize <= :maxSize', { maxSize });
    }

    // 排序
    const allowedSortFields = [
      'title', 'fileName', 'fileSize', 'type', 'status', 'accessLevel',
      'viewCount', 'downloadCount', 'createdAt', 'updatedAt'
    ];

    if (allowedSortFields.includes(sortBy)) {
      queryBuilder.orderBy(`document.${sortBy}`, sortOrder);
    } else {
      queryBuilder.orderBy('document.createdAt', 'DESC');
    }

    // 分页
    const total = await queryBuilder.getCount();
    const totalPages = Math.ceil(total / pageSize);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const documents = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getMany();

    return {
      data: documents,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };
  }

  async findOne(id: string, userId?: string) {
    const document = await this.documentsRepository.findOne({
      where: { id },
      relations: ['creator', 'updater'],
    });

    if (!document) {
      throw new NotFoundException('文档不存在');
    }

    // 检查访问权限
    this.checkReadPermission(document, userId);

    // 增加查看次数
    if (userId) {
      await this.incrementViewCount(id);
    }

    return document;
  }

  async update(id: string, updateDocumentDto: UpdateDocumentDto, userId: string) {
    const document = await this.findOne(id);

    // 检查更新权限
    this.checkWritePermission(document, userId);

    const updatedDocument = await this.documentsRepository.save({
      ...document,
      ...updateDocumentDto,
      updatedBy: userId,
    });

    return updatedDocument;
  }

  async remove(id: string, userId: string) {
    const document = await this.findOne(id);

    // 检查删除权限
    this.checkWritePermission(document, userId);

    // 软删除：标记为已删除状态
    await this.documentsRepository.update(id, {
      status: DocumentStatus.DELETED,
      updatedBy: userId,
    });

    return { message: '文档删除成功' };
  }

  async download(id: string, userId?: string) {
    const document = await this.findOne(id, userId);

    // 增加下载次数
    await this.incrementDownloadCount(id);

    return {
      fileName: document.fileName,
      filePath: document.filePath,
      mimeType: document.mimeType,
    };
  }

  async getStats(organizationId?: string) {
    const whereCondition = organizationId ? { organizationId } : {};

    const [
      totalDocuments,
      publishedDocuments,
      draftDocuments,
      archivedDocuments,
      totalViews,
      totalDownloads,
      recentDocuments,
    ] = await Promise.all([
      this.documentsRepository.count({ where: whereCondition }),
      this.documentsRepository.count({
        where: { ...whereCondition, status: DocumentStatus.PUBLISHED },
      }),
      this.documentsRepository.count({
        where: { ...whereCondition, status: DocumentStatus.DRAFT },
      }),
      this.documentsRepository.count({
        where: { ...whereCondition, status: DocumentStatus.ARCHIVED },
      }),
      this.documentsRepository
        .createQueryBuilder('document')
        .select('SUM(document.viewCount)', 'sum')
        .where(organizationId ? 'document.organizationId = :organizationId' : '1=1', { organizationId })
        .getRawOne()
        .then(result => parseInt(result.sum) || 0),
      this.documentsRepository
        .createQueryBuilder('document')
        .select('SUM(document.downloadCount)', 'sum')
        .where(organizationId ? 'document.organizationId = :organizationId' : '1=1', { organizationId })
        .getRawOne()
        .then(result => parseInt(result.sum) || 0),
      this.documentsRepository.count({
        where: {
          ...whereCondition,
          createdAt: MoreThanOrEqual(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
        },
      }),
    ]);

    // 按类型统计
    const typeStats = await this.documentsRepository
      .createQueryBuilder('document')
      .select('document.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .where(organizationId ? 'document.organizationId = :organizationId' : '1=1', { organizationId })
      .groupBy('document.type')
      .getRawMany();

    return {
      overview: {
        totalDocuments,
        publishedDocuments,
        draftDocuments,
        archivedDocuments,
        recentDocuments,
      },
      activity: {
        totalViews,
        totalDownloads,
      },
      distribution: {
        byType: typeStats.map(stat => ({
          type: stat.type,
          count: parseInt(stat.count),
        })),
      },
    };
  }

  async searchContent(query: string, organizationId?: string) {
    const queryBuilder = this.documentsRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.creator', 'creator')
      .where('document.isSearchable = true')
      .andWhere('document.status = :status', { status: DocumentStatus.PUBLISHED });

    if (organizationId) {
      queryBuilder.andWhere('document.organizationId = :organizationId', { organizationId });
    }

    if (query) {
      queryBuilder.andWhere(
        '(document.title ILIKE :query OR document.description ILIKE :query OR document.content ILIKE :query)',
        { query: `%${query}%` }
      );
    }

    return await queryBuilder
      .orderBy('document.createdAt', 'DESC')
      .take(50)
      .getMany();
  }

  async getPopularDocuments(organizationId?: string, limit: number = 10) {
    const queryBuilder = this.documentsRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.creator', 'creator')
      .where('document.status = :status', { status: DocumentStatus.PUBLISHED });

    if (organizationId) {
      queryBuilder.andWhere('document.organizationId = :organizationId', { organizationId });
    }

    return await queryBuilder
      .orderBy('document.viewCount', 'DESC')
      .addOrderBy('document.downloadCount', 'DESC')
      .take(limit)
      .getMany();
  }

  private async incrementViewCount(id: string) {
    await this.documentsRepository.increment({ id }, 'viewCount', 1);
  }

  private async incrementDownloadCount(id: string) {
    await this.documentsRepository.increment({ id }, 'downloadCount', 1);
  }

  private checkReadPermission(document: Document, userId?: string) {
    switch (document.accessLevel) {
      case AccessLevel.PUBLIC:
        return true;
      case AccessLevel.INTERNAL:
        if (!userId) {
          throw new ForbiddenException('需要登录才能访问此文档');
        }
        return true;
      case AccessLevel.PRIVATE:
        if (!userId || document.createdBy !== userId) {
          throw new ForbiddenException('无权访问此私有文档');
        }
        return true;
      case AccessLevel.RESTRICTED:
        // 这里可以添加更复杂的权限检查逻辑
        if (!userId) {
          throw new ForbiddenException('无权访问此受限文档');
        }
        return true;
      default:
        throw new ForbiddenException('无权访问此文档');
    }
  }

  private checkWritePermission(document: Document, userId: string) {
    if (!userId) {
      throw new ForbiddenException('需要登录才能操作');
    }

    // 只有文档创建者可以修改/删除
    if (document.createdBy !== userId) {
      throw new ForbiddenException('只有文档创建者可以进行此操作');
    }
  }
}