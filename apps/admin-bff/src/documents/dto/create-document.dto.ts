import {
  IsString,
  <PERSON><PERSON>ptional,
  Is<PERSON>num,
  IsArray,
  IsBoolean,
  MaxLength,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { DocumentType, DocumentStatus, AccessLevel } from '../entities/document.entity';

export class CreateDocumentDto {
  @ApiProperty({ description: '文档标题' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  @ApiProperty({ required: false, description: '文档描述' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({ description: '文件名' })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({ description: '文件路径' })
  @IsString()
  @IsNotEmpty()
  filePath: string;

  @ApiProperty({ description: '文件大小（字节）' })
  @IsOptional()
  fileSize?: number;

  @ApiProperty({ required: false, description: 'MIME类型' })
  @IsOptional()
  @IsString()
  mimeType?: string;

  @ApiProperty({ enum: DocumentType, description: '文档类型' })
  @IsEnum(DocumentType)
  type: DocumentType;

  @ApiProperty({ enum: DocumentStatus, required: false, description: '文档状态' })
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @ApiProperty({ enum: AccessLevel, required: false, description: '访问级别' })
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @ApiProperty({ required: false, description: '组织ID' })
  @IsOptional()
  @IsString()
  organizationId?: string;

  @ApiProperty({ required: false, description: '标签列表' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ required: false, description: '元数据' })
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiProperty({ required: false, description: '缩略图路径' })
  @IsOptional()
  @IsString()
  thumbnailPath?: string;

  @ApiProperty({ required: false, description: '是否可搜索' })
  @IsOptional()
  @IsBoolean()
  isSearchable?: boolean;

  @ApiProperty({ required: false, description: '文档内容（用于搜索）' })
  @IsOptional()
  @IsString()
  content?: string;
}