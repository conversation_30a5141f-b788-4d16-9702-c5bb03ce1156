import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum DocumentType {
  PDF = 'pdf',
  WORD = 'word',
  EXCEL = 'excel',
  POWERPOINT = 'powerpoint',
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  OTHER = 'other',
}

export enum DocumentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

export enum AccessLevel {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  PRIVATE = 'private',
  RESTRICTED = 'restricted',
}

@Entity('documents')
@Index(['title'])
@Index(['type'])
@Index(['status'])
@Index(['accessLevel'])
@Index(['organizationId'])
@Index(['createdBy'])
@Index(['createdAt'])
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column()
  fileName: string;

  @Column()
  filePath: string;

  @Column()
  fileSize: number;

  @Column({ nullable: true })
  mimeType?: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.OTHER,
  })
  type: DocumentType;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.DRAFT,
  })
  status: DocumentStatus;

  @Column({
    type: 'enum',
    enum: AccessLevel,
    default: AccessLevel.INTERNAL,
  })
  accessLevel: AccessLevel;

  @Column({ nullable: true })
  organizationId?: string;

  @Column()
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator: User;

  @Column({ nullable: true })
  updatedBy?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updatedBy' })
  updater?: User;

  @Column('text', { array: true, default: [] })
  tags: string[];

  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @Column({ default: 0 })
  viewCount: number;

  @Column({ default: 0 })
  downloadCount: number;

  @Column({ nullable: true })
  thumbnailPath?: string;

  @Column({ default: true })
  isSearchable: boolean;

  @Column({ type: 'text', nullable: true })
  content?: string; // 提取的文本内容用于搜索

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 虚拟属性
  get fileExtension(): string {
    return this.fileName.split('.').pop()?.toLowerCase() || '';
  }

  get sizeFormatted(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}