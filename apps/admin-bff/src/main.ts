import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启用CORS
  app.enableCors({
    origin: process.env['CORS_ORIGINS']?.split(',') || ['http://localhost:3100'],
    credentials: true,
  });

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // API文档
  const config = new DocumentBuilder()
    .setTitle('MasteryOS Admin API')
    .setDescription('MasteryOS 管理端 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  // 健康检查
  app.getHttpAdapter().get('/health', (_req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  const port = process.env['PORT'] || 3000;
  await app.listen(port);

  console.info(`🚀 Admin BFF running on http://localhost:${port}`);
  console.info(`📚 API docs available at http://localhost:${port}/docs`);
}

void bootstrap().catch((error) => {
  console.error('应用启动失败:', error);
  process.exit(1);
});
