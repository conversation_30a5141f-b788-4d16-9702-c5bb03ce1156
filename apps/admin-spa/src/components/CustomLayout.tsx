import * as React from 'react';
import { Layout, CheckForApplicationUpdate } from 'react-admin';
import { Box } from '@mui/material';
import { CustomAppBar } from './CustomAppBar';
import { CustomMenu } from './CustomMenu';

export const CustomLayout = (props: { children: React.ReactNode }) => (
  <Layout
    {...props}
    appBar={CustomAppBar}
    menu={CustomMenu}
    sx={{
      '& .RaLayout-content': {
        backgroundColor: '#f8fafc',
        minHeight: '100vh',
      },
    }}
  >
    <Box sx={{ p: 2 }}>{props.children}</Box>
    <CheckForApplicationUpdate />
  </Layout>
);
