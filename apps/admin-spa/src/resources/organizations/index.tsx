import {
  List,
  Datagrid,
  TextField,
  DateField,
  EditButton,
  Edit,
  SimpleForm,
  TextInput,
  Create,
} from 'react-admin';

export const OrganizationList = () => (
  <List>
    <Datagrid>
      <TextField source="id" label="ID" />
      <TextField source="name" label="组织名称" />
      <TextField source="slug" label="标识符" />
      <DateField source="created_at" label="创建时间" />
      <EditButton />
    </Datagrid>
  </List>
);

export const OrganizationEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput source="name" label="组织名称" required />
      <TextInput source="slug" label="标识符" required />
    </SimpleForm>
  </Edit>
);

export const OrganizationCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="name" label="组织名称" required />
      <TextInput source="slug" label="标识符" required />
    </SimpleForm>
  </Create>
);
