import {
  List,
  Datagrid,
  Text<PERSON>ield,
  Date<PERSON>ield,
  Edit<PERSON>utton,
  Edit,
  SimpleForm,
  TextInput,
  Create,
  NumberField,
  NumberInput,
} from 'react-admin';

export const SkillList = () => (
  <List>
    <Datagrid>
      <TextField source="id" label="ID" />
      <TextField source="name" label="技能名称" />
      <TextField source="description" label="描述" />
      <TextField source="category" label="分类" />
      <NumberField source="target_hours" label="目标小时数" />
      <DateField source="created_at" label="创建时间" />
      <EditButton />
    </Datagrid>
  </List>
);

export const SkillEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput source="name" label="技能名称" required />
      <TextInput source="description" label="描述" multiline rows={3} />
      <TextInput source="category" label="分类" />
      <NumberInput source="target_hours" label="目标小时数" defaultValue={10000} />
    </SimpleForm>
  </Edit>
);

export const SkillCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="name" label="技能名称" required />
      <TextInput source="description" label="描述" multiline rows={3} />
      <TextInput source="category" label="分类" />
      <NumberInput source="target_hours" label="目标小时数" defaultValue={10000} />
    </SimpleForm>
  </Create>
);
