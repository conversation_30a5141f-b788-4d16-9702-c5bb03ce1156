# Flutter Web Dockerfile
FROM ghcr.io/cirruslabs/flutter:3.24.5 AS build

# 设置工作目录
WORKDIR /app

# 复制pubspec文件
COPY pubspec.yaml pubspec.lock ./

# 安装依赖
RUN flutter pub get

# 复制源代码
COPY . .

# 生成代码
RUN flutter pub run build_runner build --delete-conflicting-outputs

# 构建Web应用
RUN flutter build web --release --web-renderer html

# Nginx部署阶段
FROM nginx:alpine AS production

# 复制构建结果到nginx
COPY --from=build /app/build/web /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]