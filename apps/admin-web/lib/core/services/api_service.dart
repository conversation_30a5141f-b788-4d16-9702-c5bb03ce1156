import 'package:dio/dio.dart';
import 'package:admin_web/core/config/app_config.dart';
import 'package:admin_web/core/services/storage_service.dart';

class ApiService {
  late final Dio _dio;
  
  Dio get dio => _dio;
  
  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          final token = StorageService.getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Token 过期，尝试刷新
            final refreshToken = StorageService.getRefreshToken();
            if (refreshToken != null) {
              try {
                final response = await _dio.post('${AppConfig.commonApiPrefix}/auth/refresh', data: {
                  'refreshToken': refreshToken,
                });
                
                final newToken = response.data['accessToken'];
                await StorageService.saveToken(newToken);
                
                // 重试原请求
                error.requestOptions.headers['Authorization'] = 'Bearer $newToken';
                final retryResponse = await _dio.fetch(error.requestOptions);
                handler.resolve(retryResponse);
                return;
              } catch (e) {
                // 刷新失败，清除认证信息
                await StorageService.clearAuth();
              }
            }
          }
          handler.next(error);
        },
      ),
    );
  }
  
  // 认证相关 - 使用统一认证API
  Future<Map<String, dynamic>> login(String usernameOrEmail, String password) async {
    final response = await _dio.post('${AppConfig.commonApiPrefix}/auth/login', data: {
      'usernameOrEmail': usernameOrEmail,
      'password': password,
    });
    return response.data;
  }
  
  Future<void> logout() async {
    await _dio.post('${AppConfig.commonApiPrefix}/auth/logout');
    await StorageService.clearAuth();
  }
  
  Future<Map<String, dynamic>> getProfile() async {
    final response = await _dio.get('${AppConfig.commonApiPrefix}/auth/profile');
    return response.data;
  }
  
  // 用户管理 - 使用管理员API
  Future<Map<String, dynamic>> getUsers({
    int page = 1,
    int pageSize = AppConfig.defaultPageSize,
    String? search,
  }) async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/users', queryParameters: {
      'page': page,
      'pageSize': pageSize,
      if (search != null) 'search': search,
    });
    return response.data;
  }
  
  Future<Map<String, dynamic>> getUser(String id) async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/users/$id');
    return response.data;
  }
  
  Future<Map<String, dynamic>> createUser(Map<String, dynamic> data) async {
    final response = await _dio.post('${AppConfig.adminApiPrefix}/users', data: data);
    return response.data;
  }
  
  Future<Map<String, dynamic>> updateUser(String id, Map<String, dynamic> data) async {
    final response = await _dio.patch('${AppConfig.adminApiPrefix}/users/$id', data: data);
    return response.data;
  }
  
  Future<void> deleteUser(String id) async {
    await _dio.delete('${AppConfig.adminApiPrefix}/users/$id');
  }

  Future<Map<String, dynamic>> getUserStats() async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/users/stats');
    return response.data;
  }
  
  // 文档管理 - 使用管理员API
  Future<Map<String, dynamic>> getDocuments({
    int page = 1,
    int pageSize = AppConfig.defaultPageSize,
    String? search,
  }) async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/documents', queryParameters: {
      'page': page,
      'pageSize': pageSize,
      if (search != null) 'search': search,
    });
    return response.data;
  }
  
  Future<Map<String, dynamic>> getDocument(String id) async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/documents/$id');
    return response.data;
  }
  
  Future<Map<String, dynamic>> createDocument(Map<String, dynamic> data) async {
    final response = await _dio.post('${AppConfig.adminApiPrefix}/documents', data: data);
    return response.data;
  }
  
  Future<Map<String, dynamic>> updateDocument(String id, Map<String, dynamic> data) async {
    final response = await _dio.patch('${AppConfig.adminApiPrefix}/documents/$id', data: data);
    return response.data;
  }
  
  Future<void> deleteDocument(String id) async {
    await _dio.delete('${AppConfig.adminApiPrefix}/documents/$id');
  }
  
  // 分析统计 - 使用管理员分析API
  Future<Map<String, dynamic>> getDashboardStats() async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/analytics/dashboard');
    return response.data;
  }
  
  Future<Map<String, dynamic>> getEventStats() async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/analytics/events/stats');
    return response.data;
  }
  
  Future<Map<String, dynamic>> getUserActivityTrend() async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/analytics/user-activity-trend');
    return response.data;
  }
  
  Future<Map<String, dynamic>> getContentEngagement() async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/analytics/content-engagement');
    return response.data;
  }
  
  Future<Map<String, dynamic>> getSystemUsage() async {
    final response = await _dio.get('${AppConfig.adminApiPrefix}/analytics/system-usage');
    return response.data;
  }
}