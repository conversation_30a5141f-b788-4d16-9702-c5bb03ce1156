import 'package:dio/dio.dart';
import 'package:admin_web/core/config/app_config.dart';
import 'package:admin_web/core/services/storage_service.dart';

class ApiService {
  late final Dio _dio;
  
  Dio get dio => _dio;
  
  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          final token = StorageService.getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Token 过期，尝试刷新
            final refreshToken = StorageService.getRefreshToken();
            if (refreshToken != null) {
              try {
                final response = await _dio.post('/auth/refresh', data: {
                  'refreshToken': refreshToken,
                });
                
                final newToken = response.data['accessToken'];
                await StorageService.saveToken(newToken);
                
                // 重试原请求
                error.requestOptions.headers['Authorization'] = 'Bearer $newToken';
                final retryResponse = await _dio.fetch(error.requestOptions);
                handler.resolve(retryResponse);
                return;
              } catch (e) {
                // 刷新失败，清除认证信息
                await StorageService.clearAuth();
              }
            }
          }
          handler.next(error);
        },
      ),
    );
  }
  
  // 认证相关
  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await _dio.post('/auth/login', data: {
      'email': email,
      'password': password,
    });
    return response.data;
  }
  
  Future<void> logout() async {
    await _dio.post('/auth/logout');
    await StorageService.clearAuth();
  }
  
  // 用户管理
  Future<Map<String, dynamic>> getUsers({
    int page = 1,
    int pageSize = AppConfig.defaultPageSize,
    String? search,
  }) async {
    final response = await _dio.get('/users', queryParameters: {
      'page': page,
      'pageSize': pageSize,
      if (search != null) 'search': search,
    });
    return response.data;
  }
  
  Future<Map<String, dynamic>> getUser(String id) async {
    final response = await _dio.get('/users/$id');
    return response.data;
  }
  
  Future<Map<String, dynamic>> createUser(Map<String, dynamic> data) async {
    final response = await _dio.post('/users', data: data);
    return response.data;
  }
  
  Future<Map<String, dynamic>> updateUser(String id, Map<String, dynamic> data) async {
    final response = await _dio.patch('/users/$id', data: data);
    return response.data;
  }
  
  Future<void> deleteUser(String id) async {
    await _dio.delete('/users/$id');
  }
  
  // 文档管理
  Future<Map<String, dynamic>> getDocuments({
    int page = 1,
    int pageSize = AppConfig.defaultPageSize,
    String? search,
  }) async {
    final response = await _dio.get('/documents', queryParameters: {
      'page': page,
      'pageSize': pageSize,
      if (search != null) 'search': search,
    });
    return response.data;
  }
  
  // 统计数据
  Future<Map<String, dynamic>> getDashboardStats() async {
    final response = await _dio.get('/analytics/dashboard');
    return response.data;
  }
  
  Future<Map<String, dynamic>> getLearningStats() async {
    final response = await _dio.get('/analytics/learning');
    return response.data;
  }
}