import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static late SharedPreferences _prefs;
  
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  // 基础存储方法
  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }
  
  static String? getString(String key) {
    return _prefs.getString(key);
  }
  
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }
  
  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }
  
  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }
  
  static Future<bool> clear() async {
    return await _prefs.clear();
  }
  
  // 认证相关
  static Future<void> saveToken(String token) async {
    await setString('auth_token', token);
  }
  
  static String? getToken() {
    return getString('auth_token');
  }
  
  static Future<void> saveRefreshToken(String token) async {
    await setString('refresh_token', token);
  }
  
  static String? getRefreshToken() {
    return getString('refresh_token');
  }
  
  static Future<void> clearAuth() async {
    await remove('auth_token');
    await remove('refresh_token');
  }
}