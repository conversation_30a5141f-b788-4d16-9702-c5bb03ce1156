import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:admin_web/core/router/app_router.dart';
import 'package:admin_web/core/theme/app_theme.dart';
import 'package:admin_web/features/auth/bloc/auth_bloc.dart';
import 'package:admin_web/core/services/api_service.dart';

class AdminApp extends StatelessWidget {
  const AdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider(create: (_) => ApiService()),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => AuthBloc(
              apiService: context.read<ApiService>(),
            ),
          ),
        ],
        child: MaterialApp.router(
          title: 'MasteryOS Admin',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          routerConfig: AppRouter.router,
          debugShowCheckedModeBanner: false,
        ),
      ),
    );
  }
}