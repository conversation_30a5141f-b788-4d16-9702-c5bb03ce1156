import 'package:flutter/material.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('系统设置'),
      ),
      body: Row(
        children: [
          // 设置分类
          SizedBox(
            width: 250,
            child: Card(
              margin: const EdgeInsets.all(16),
              child: ListView(
                padding: const EdgeInsets.all(8),
                children: [
                  ListTile(
                    leading: const Icon(Icons.person),
                    title: const Text('个人资料'),
                    selected: true,
                    onTap: () {},
                  ),
                  ListTile(
                    leading: const Icon(Icons.security),
                    title: const Text('安全设置'),
                    onTap: () {},
                  ),
                  ListTile(
                    leading: const Icon(Icons.notifications),
                    title: const Text('通知设置'),
                    onTap: () {},
                  ),
                  ListTile(
                    leading: const Icon(Icons.palette),
                    title: const Text('主题设置'),
                    onTap: () {},
                  ),
                  ListTile(
                    leading: const Icon(Icons.storage),
                    title: const Text('数据管理'),
                    onTap: () {},
                  ),
                  ListTile(
                    leading: const Icon(Icons.info),
                    title: const Text('关于系统'),
                    onTap: () {},
                  ),
                ],
              ),
            ),
          ),
          // 设置内容
          Expanded(
            child: Card(
              margin: const EdgeInsets.fromLTRB(0, 16, 16, 16),
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '个人资料',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: 32),
                    // 头像
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: Theme.of(context).primaryColor,
                          child: const Text(
                            'A',
                            style: TextStyle(fontSize: 40, color: Colors.white),
                          ),
                        ),
                        const SizedBox(width: 24),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ElevatedButton(
                              onPressed: () {},
                              child: const Text('更换头像'),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '支持 JPG、PNG 格式，最大 2MB',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                    // 基本信息
                    SizedBox(
                      width: 600,
                      child: Column(
                        children: [
                          TextFormField(
                            initialValue: 'Admin',
                            decoration: const InputDecoration(
                              labelText: '用户名',
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            initialValue: '<EMAIL>',
                            decoration: const InputDecoration(
                              labelText: '邮箱',
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            decoration: const InputDecoration(
                              labelText: '手机号',
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            maxLines: 3,
                            decoration: const InputDecoration(
                              labelText: '个人简介',
                              alignLabelWithHint: true,
                            ),
                          ),
                          const SizedBox(height: 32),
                          Row(
                            children: [
                              ElevatedButton(
                                onPressed: () {},
                                child: const Text('保存更改'),
                              ),
                              const SizedBox(width: 16),
                              TextButton(
                                onPressed: () {},
                                child: const Text('取消'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}