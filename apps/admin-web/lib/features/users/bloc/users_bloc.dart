import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:admin_web/shared/models/user.dart';
import 'package:admin_web/shared/models/pagination.dart';
import 'package:admin_web/features/users/services/users_api_service.dart';

// Events
abstract class UsersEvent extends Equatable {
  const UsersEvent();
  
  @override
  List<Object?> get props => [];
}

class LoadUsers extends UsersEvent {
  final int page;
  final int pageSize;
  final String? search;
  final UserRole? role;
  final UserStatus? status;
  final String sortBy;
  final String sortOrder;

  const LoadUsers({
    this.page = 1,
    this.pageSize = 20,
    this.search,
    this.role,
    this.status,
    this.sortBy = 'createdAt',
    this.sortOrder = 'DESC',
  });

  @override
  List<Object?> get props => [
        page,
        pageSize,
        search,
        role,
        status,
        sortBy,
        sortOrder,
      ];
}

class LoadUserDetails extends UsersEvent {
  final String userId;

  const LoadUserDetails(this.userId);

  @override
  List<Object?> get props => [userId];
}

class <PERSON>reateUser extends UsersEvent {
  final String email;
  final String username;
  final String password;
  final String? firstName;
  final String? lastName;
  final UserRole? role;
  final UserStatus? status;
  final String? organizationId;
  final String? phone;
  final String? bio;

  const CreateUser({
    required this.email,
    required this.username,
    required this.password,
    this.firstName,
    this.lastName,
    this.role,
    this.status,
    this.organizationId,
    this.phone,
    this.bio,
  });

  @override
  List<Object?> get props => [
        email,
        username,
        password,
        firstName,
        lastName,
        role,
        status,
        organizationId,
        phone,
        bio,
      ];
}

class UpdateUser extends UsersEvent {
  final String userId;
  final String? email;
  final String? username;
  final String? firstName;
  final String? lastName;
  final UserRole? role;
  final UserStatus? status;
  final String? organizationId;
  final String? phone;
  final String? bio;

  const UpdateUser({
    required this.userId,
    this.email,
    this.username,
    this.firstName,
    this.lastName,
    this.role,
    this.status,
    this.organizationId,
    this.phone,
    this.bio,
  });

  @override
  List<Object?> get props => [
        userId,
        email,
        username,
        firstName,
        lastName,
        role,
        status,
        organizationId,
        phone,
        bio,
      ];
}

class DeleteUser extends UsersEvent {
  final String userId;

  const DeleteUser(this.userId);

  @override
  List<Object?> get props => [userId];
}

class LoadUserStats extends UsersEvent {}

// States
abstract class UsersState extends Equatable {
  const UsersState();
  
  @override
  List<Object?> get props => [];
}

class UsersInitial extends UsersState {}

class UsersLoading extends UsersState {}

class UsersLoaded extends UsersState {
  final List<User> users;
  final Pagination pagination;
  final Map<String, dynamic>? stats;

  const UsersLoaded({
    required this.users,
    required this.pagination,
    this.stats,
  });

  @override
  List<Object?> get props => [users, pagination, stats];
}

class UserDetailsLoaded extends UsersState {
  final User user;

  const UserDetailsLoaded(this.user);

  @override
  List<Object?> get props => [user];
}

class UsersError extends UsersState {
  final String message;

  const UsersError(this.message);

  @override
  List<Object?> get props => [message];
}

class UserOperationSuccess extends UsersState {
  final String message;

  const UserOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

// Bloc
class UsersBloc extends Bloc<UsersEvent, UsersState> {
  final UsersApiService _usersApiService;

  UsersBloc(this._usersApiService) : super(UsersInitial()) {
    on<LoadUsers>(_onLoadUsers);
    on<LoadUserDetails>(_onLoadUserDetails);
    on<CreateUser>(_onCreateUser);
    on<UpdateUser>(_onUpdateUser);
    on<DeleteUser>(_onDeleteUser);
    on<LoadUserStats>(_onLoadUserStats);
  }

  Future<void> _onLoadUsers(
    LoadUsers event,
    Emitter<UsersState> emit,
  ) async {
    emit(UsersLoading());

    try {
      final [usersResponse, statsResponse] = await Future.wait([
        _usersApiService.getUsers(
          page: event.page,
          pageSize: event.pageSize,
          search: event.search,
          role: event.role,
          status: event.status,
          sortBy: event.sortBy,
          sortOrder: event.sortOrder,
        ),
        _usersApiService.getUserStats(),
      ]);

      emit(UsersLoaded(
        users: (usersResponse as PaginatedResponse<User>).data,
        pagination: usersResponse.pagination,
        stats: statsResponse as Map<String, dynamic>,
      ));
    } catch (e) {
      emit(UsersError('加载用户列表失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadUserDetails(
    LoadUserDetails event,
    Emitter<UsersState> emit,
  ) async {
    emit(UsersLoading());

    try {
      final user = await _usersApiService.getUser(event.userId);
      emit(UserDetailsLoaded(user));
    } catch (e) {
      emit(UsersError('加载用户详情失败: ${e.toString()}'));
    }
  }

  Future<void> _onCreateUser(
    CreateUser event,
    Emitter<UsersState> emit,
  ) async {
    try {
      await _usersApiService.createUser(
        email: event.email,
        username: event.username,
        password: event.password,
        firstName: event.firstName,
        lastName: event.lastName,
        role: event.role,
        status: event.status,
        organizationId: event.organizationId,
        phone: event.phone,
        bio: event.bio,
      );

      emit(const UserOperationSuccess('用户创建成功'));
      
      // 重新加载用户列表
      add(const LoadUsers());
    } catch (e) {
      emit(UsersError('创建用户失败: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateUser(
    UpdateUser event,
    Emitter<UsersState> emit,
  ) async {
    try {
      await _usersApiService.updateUser(
        event.userId,
        email: event.email,
        username: event.username,
        firstName: event.firstName,
        lastName: event.lastName,
        role: event.role,
        status: event.status,
        organizationId: event.organizationId,
        phone: event.phone,
        bio: event.bio,
      );

      emit(const UserOperationSuccess('用户更新成功'));
      
      // 重新加载用户列表
      add(const LoadUsers());
    } catch (e) {
      emit(UsersError('更新用户失败: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteUser(
    DeleteUser event,
    Emitter<UsersState> emit,
  ) async {
    try {
      await _usersApiService.deleteUser(event.userId);
      emit(const UserOperationSuccess('用户删除成功'));
      
      // 重新加载用户列表
      add(const LoadUsers());
    } catch (e) {
      emit(UsersError('删除用户失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadUserStats(
    LoadUserStats event,
    Emitter<UsersState> emit,
  ) async {
    try {
      final stats = await _usersApiService.getUserStats();
      
      if (state is UsersLoaded) {
        final currentState = state as UsersLoaded;
        emit(UsersLoaded(
          users: currentState.users,
          pagination: currentState.pagination,
          stats: stats,
        ));
      }
    } catch (e) {
      // 静默处理统计数据加载失败
    }
  }
}