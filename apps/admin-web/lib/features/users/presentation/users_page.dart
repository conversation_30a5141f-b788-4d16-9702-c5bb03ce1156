import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:intl/intl.dart';
import 'package:admin_web/shared/models/user.dart';
import 'package:admin_web/features/users/bloc/users_bloc.dart';
import 'package:admin_web/features/users/services/users_api_service.dart';
import 'package:admin_web/core/services/api_service.dart';

class UsersPage extends StatelessWidget {
  const UsersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => UsersBloc(
        UsersApiService(context.read<ApiService>().dio),
      )..add(const LoadUsers()),
      child: const _UsersPageContent(),
    );
  }
}

class _UsersPageContent extends StatefulWidget {
  const _UsersPageContent();

  @override
  State<_UsersPageContent> createState() => _UsersPageContentState();
}

class _UsersPageContentState extends State<_UsersPageContent> {
  final TextEditingController _searchController = TextEditingController();
  UserRole? _selectedRole;
  UserStatus? _selectedStatus;
  int _currentPage = 1;
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  void _loadUsers() {
    context.read<UsersBloc>().add(LoadUsers(
      page: _currentPage,
      search: _searchController.text.isEmpty ? null : _searchController.text,
      role: _selectedRole,
      status: _selectedStatus,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用户管理'),
        actions: [
          ElevatedButton.icon(
            onPressed: () => _showCreateUserDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('添加用户'),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: BlocListener<UsersBloc, UsersState>(
        listener: (context, state) {
          if (state is UsersError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is UserOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildSearchBar(),
              const SizedBox(height: 16),
              Expanded(child: _buildUsersTable()),
              _buildPagination(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索用户...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  _loadUsers();
                },
              ),
            ),
            onSubmitted: (_) => _loadUsers(),
          ),
        ),
        const SizedBox(width: 16),
        DropdownButton<UserRole?>(
          value: _selectedRole,
          hint: const Text('全部角色'),
          items: [
            const DropdownMenuItem(value: null, child: Text('全部角色')),
            ...UserRole.values.map((role) => DropdownMenuItem(
              value: role,
              child: Text(role == UserRole.admin ? '管理员' : '普通用户'),
            )),
          ],
          onChanged: (value) {
            setState(() => _selectedRole = value);
            _loadUsers();
          },
        ),
        const SizedBox(width: 16),
        DropdownButton<UserStatus?>(
          value: _selectedStatus,
          hint: const Text('全部状态'),
          items: [
            const DropdownMenuItem(value: null, child: Text('全部状态')),
            ...UserStatus.values.map((status) => DropdownMenuItem(
              value: status,
              child: Text(_getStatusDisplayName(status)),
            )),
          ],
          onChanged: (value) {
            setState(() => _selectedStatus = value);
            _loadUsers();
          },
        ),
      ],
    );
  }

  Widget _buildUsersTable() {
    return BlocBuilder<UsersBloc, UsersState>(
      builder: (context, state) {
        if (state is UsersLoading) {
          return const Card(
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (state is UsersLoaded) {
          return Card(
            child: DataTable2(
              columnSpacing: 12,
              horizontalMargin: 12,
              minWidth: 600,
              columns: const [
                DataColumn2(label: Text('用户名'), size: ColumnSize.L),
                DataColumn2(label: Text('邮箱'), size: ColumnSize.L),
                DataColumn2(label: Text('姓名'), size: ColumnSize.M),
                DataColumn2(label: Text('角色'), size: ColumnSize.S),
                DataColumn2(label: Text('状态'), size: ColumnSize.S),
                DataColumn2(label: Text('创建时间'), size: ColumnSize.M),
                DataColumn2(label: Text('操作'), size: ColumnSize.M, fixedWidth: 120),
              ],
              rows: state.users.map((user) => _buildUserRow(user)).toList(),
              empty: const Center(child: Text('暂无数据')),
            ),
          );
        }

        return const Card(
          child: Center(child: Text('加载失败')),
        );
      },
    );
  }

  DataRow _buildUserRow(User user) {
    return DataRow(
      cells: [
        DataCell(Text(user.username)),
        DataCell(Text(user.email)),
        DataCell(Text(user.displayName)),
        DataCell(Chip(
          label: Text(user.roleDisplayName),
          backgroundColor: user.role == UserRole.admin ? Colors.orange : Colors.blue,
        )),
        DataCell(Chip(
          label: Text(user.statusDisplayName),
          backgroundColor: _getStatusColor(user.status),
        )),
        DataCell(Text(DateFormat('yyyy-MM-dd').format(user.createdAt))),
        DataCell(Row(
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _showEditUserDialog(context, user),
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: () => _showDeleteConfirmDialog(context, user),
            ),
          ],
        )),
      ],
    );
  }

  Widget _buildPagination() {
    return BlocBuilder<UsersBloc, UsersState>(
      builder: (context, state) {
        if (state is! UsersLoaded) return const SizedBox.shrink();

        final pagination = state.pagination;
        final start = (pagination.page - 1) * pagination.pageSize + 1;
        final end = (start + state.users.length - 1).clamp(0, pagination.total);

        return Card(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('显示 $start-$end 条，共 ${pagination.total} 条'),
                Row(
                  children: [
                    IconButton(
                      onPressed: pagination.hasPrev ? () {
                        setState(() => _currentPage--);
                        _loadUsers();
                      } : null,
                      icon: const Icon(Icons.chevron_left),
                    ),
                    ...List.generate(
                      (pagination.totalPages).clamp(0, 5),
                      (index) {
                        final pageNum = index + 1;
                        return TextButton(
                          onPressed: pageNum == pagination.page ? null : () {
                            setState(() => _currentPage = pageNum);
                            _loadUsers();
                          },
                          child: Text('$pageNum'),
                        );
                      },
                    ),
                    IconButton(
                      onPressed: pagination.hasNext ? () {
                        setState(() => _currentPage++);
                        _loadUsers();
                      } : null,
                      icon: const Icon(Icons.chevron_right),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getStatusDisplayName(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return '活跃';
      case UserStatus.inactive:
        return '未激活';
      case UserStatus.suspended:
        return '已暂停';
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Colors.green.withValues(alpha: 0.2);
      case UserStatus.inactive:
        return Colors.orange.withValues(alpha: 0.2);
      case UserStatus.suspended:
        return Colors.red.withValues(alpha: 0.2);
    }
  }

  void _showCreateUserDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => UserFormDialog(
        title: '添加用户',
        onSubmit: (data) {
          context.read<UsersBloc>().add(CreateUser(
            email: data['email']!,
            username: data['username']!,
            password: data['password']!,
            firstName: data['firstName'],
            lastName: data['lastName'],
            role: data['role'],
            status: data['status'],
            phone: data['phone'],
            bio: data['bio'],
          ));
        },
      ),
    );
  }

  void _showEditUserDialog(BuildContext context, User user) {
    showDialog(
      context: context,
      builder: (context) => UserFormDialog(
        title: '编辑用户',
        user: user,
        onSubmit: (data) {
          context.read<UsersBloc>().add(UpdateUser(
            userId: user.id,
            email: data['email'],
            username: data['username'],
            firstName: data['firstName'],
            lastName: data['lastName'],
            role: data['role'],
            status: data['status'],
            phone: data['phone'],
            bio: data['bio'],
          ));
        },
      ),
    );
  }

  void _showDeleteConfirmDialog(BuildContext context, User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除用户 "${user.username}" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<UsersBloc>().add(DeleteUser(user.id));
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}

class UserFormDialog extends StatefulWidget {
  final String title;
  final User? user;
  final Function(Map<String, dynamic>) onSubmit;

  const UserFormDialog({
    super.key,
    required this.title,
    this.user,
    required this.onSubmit,
  });

  @override
  State<UserFormDialog> createState() => _UserFormDialogState();
}

class _UserFormDialogState extends State<UserFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _bioController = TextEditingController();
  
  UserRole _selectedRole = UserRole.user;
  UserStatus _selectedStatus = UserStatus.active;

  @override
  void initState() {
    super.initState();
    if (widget.user != null) {
      final user = widget.user!;
      _emailController.text = user.email;
      _usernameController.text = user.username;
      _firstNameController.text = user.firstName ?? '';
      _lastNameController.text = user.lastName ?? '';
      _phoneController.text = user.phone ?? '';
      _bioController.text = user.bio ?? '';
      _selectedRole = user.role;
      _selectedStatus = user.status;
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(labelText: '邮箱 *'),
                  validator: (value) {
                    if (value?.isEmpty ?? true) return '请输入邮箱';
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                      return '请输入有效邮箱';
                    }
                    return null;
                  },
                ),
                TextFormField(
                  controller: _usernameController,
                  decoration: const InputDecoration(labelText: '用户名 *'),
                  validator: (value) {
                    if (value?.isEmpty ?? true) return '请输入用户名';
                    return null;
                  },
                ),
                if (widget.user == null)
                  TextFormField(
                    controller: _passwordController,
                    decoration: const InputDecoration(labelText: '密码 *'),
                    obscureText: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) return '请输入密码';
                      if (value!.length < 6) return '密码至少6位';
                      return null;
                    },
                  ),
                TextFormField(
                  controller: _firstNameController,
                  decoration: const InputDecoration(labelText: '名'),
                ),
                TextFormField(
                  controller: _lastNameController,
                  decoration: const InputDecoration(labelText: '姓'),
                ),
                TextFormField(
                  controller: _phoneController,
                  decoration: const InputDecoration(labelText: '电话'),
                ),
                DropdownButtonFormField<UserRole>(
                  value: _selectedRole,
                  decoration: const InputDecoration(labelText: '角色'),
                  items: UserRole.values.map((role) => DropdownMenuItem(
                    value: role,
                    child: Text(role == UserRole.admin ? '管理员' : '普通用户'),
                  )).toList(),
                  onChanged: (value) => setState(() => _selectedRole = value!),
                ),
                DropdownButtonFormField<UserStatus>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(labelText: '状态'),
                  items: UserStatus.values.map((status) => DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusDisplayName(status)),
                  )).toList(),
                  onChanged: (value) => setState(() => _selectedStatus = value!),
                ),
                TextFormField(
                  controller: _bioController,
                  decoration: const InputDecoration(labelText: '简介'),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final data = <String, dynamic>{
                'email': _emailController.text,
                'username': _usernameController.text,
                if (widget.user == null) 'password': _passwordController.text,
                'firstName': _firstNameController.text.isEmpty ? null : _firstNameController.text,
                'lastName': _lastNameController.text.isEmpty ? null : _lastNameController.text,
                'phone': _phoneController.text.isEmpty ? null : _phoneController.text,
                'bio': _bioController.text.isEmpty ? null : _bioController.text,
                'role': _selectedRole,
                'status': _selectedStatus,
              };
              widget.onSubmit(data);
              Navigator.of(context).pop();
            }
          },
          child: Text(widget.user == null ? '创建' : '更新'),
        ),
      ],
    );
  }

  String _getStatusDisplayName(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return '活跃';
      case UserStatus.inactive:
        return '未激活';
      case UserStatus.suspended:
        return '已暂停';
    }
  }
}