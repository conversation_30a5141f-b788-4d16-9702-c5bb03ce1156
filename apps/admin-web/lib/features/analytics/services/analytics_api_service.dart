import 'package:dio/dio.dart';
import 'package:admin_web/shared/models/analytics.dart';

class AnalyticsApiService {
  final Dio _dio;

  AnalyticsApiService(this._dio);

  Future<DashboardStats> getDashboardStats({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get('/analytics/dashboard', queryParameters: {
      'days': days,
      if (organizationId != null) 'organizationId': organizationId,
    });

    return DashboardStats.fromJson(response.data);
  }

  Future<List<UserActivityTrend>> getUserActivityTrend({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get('/analytics/user-activity-trend', queryParameters: {
      'days': days,
      if (organizationId != null) 'organizationId': organizationId,
    });

    return (response.data as List)
        .map((item) => UserActivityTrend.fromJson(item))
        .toList();
  }

  Future<List<EventTypeDistribution>> getEventTypeDistribution({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get('/analytics/event-distribution', queryParameters: {
      'days': days,
      if (organizationId != null) 'organizationId': organizationId,
    });

    return (response.data as List)
        .map((item) => EventTypeDistribution.fromJson(item))
        .toList();
  }

  Future<List<TopActiveUser>> getTopActiveUsers({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get('/analytics/top-active-users', queryParameters: {
      'days': days,
      if (organizationId != null) 'organizationId': organizationId,
    });

    return (response.data as List)
        .map((item) => TopActiveUser.fromJson(item))
        .toList();
  }

  Future<List<HourlyActivityPattern>> getHourlyActivityPattern({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get('/analytics/hourly-pattern', queryParameters: {
      'days': days,
      if (organizationId != null) 'organizationId': organizationId,
    });

    return (response.data as List)
        .map((item) => HourlyActivityPattern.fromJson(item))
        .toList();
  }

  Future<List<AnalyticsEvent>> getEvents({
    String? startDate,
    String? endDate,
    String? organizationId,
    String? userId,
    AnalyticsEventType? eventType,
  }) async {
    final response = await _dio.get('/analytics/events', queryParameters: {
      if (startDate != null) 'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
      if (organizationId != null) 'organizationId': organizationId,
      if (userId != null) 'userId': userId,
      if (eventType != null) 'eventType': eventType.name,
    });

    return (response.data as List)
        .map((item) => AnalyticsEvent.fromJson(item))
        .toList();
  }

  Future<AnalyticsEvent> createEvent({
    required AnalyticsEventType eventType,
    String? userId,
    String? organizationId,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) async {
    final response = await _dio.post('/analytics/events', data: {
      'eventType': eventType.name,
      if (userId != null) 'userId': userId,
      if (organizationId != null) 'organizationId': organizationId,
      if (sessionId != null) 'sessionId': sessionId,
      if (metadata != null) 'metadata': metadata,
    });

    return AnalyticsEvent.fromJson(response.data);
  }
}