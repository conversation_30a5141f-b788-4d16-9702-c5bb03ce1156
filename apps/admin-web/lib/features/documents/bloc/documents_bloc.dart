import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:admin_web/shared/models/document.dart';
import 'package:admin_web/shared/models/pagination.dart';
import 'package:admin_web/features/documents/services/documents_api_service.dart';

// Events
abstract class DocumentsEvent extends Equatable {
  const DocumentsEvent();

  @override
  List<Object?> get props => [];
}

class LoadDocuments extends DocumentsEvent {
  final int page;
  final int pageSize;
  final String? search;
  final DocumentType? type;
  final DocumentStatus? status;
  final AccessLevel? accessLevel;
  final String? organizationId;
  final String? createdBy;
  final List<String>? tags;
  final String sortBy;
  final String sortOrder;
  final String? startDate;
  final String? endDate;
  final int? minSize;
  final int? maxSize;

  const LoadDocuments({
    this.page = 1,
    this.pageSize = 20,
    this.search,
    this.type,
    this.status,
    this.accessLevel,
    this.organizationId,
    this.createdBy,
    this.tags,
    this.sortBy = 'createdAt',
    this.sortOrder = 'DESC',
    this.startDate,
    this.endDate,
    this.minSize,
    this.maxSize,
  });

  @override
  List<Object?> get props => [
        page,
        pageSize,
        search,
        type,
        status,
        accessLevel,
        organizationId,
        createdBy,
        tags,
        sortBy,
        sortOrder,
        startDate,
        endDate,
        minSize,
        maxSize,
      ];
}

class LoadDocumentDetails extends DocumentsEvent {
  final String documentId;

  const LoadDocumentDetails(this.documentId);

  @override
  List<Object?> get props => [documentId];
}

class CreateDocument extends DocumentsEvent {
  final String title;
  final String? description;
  final String fileName;
  final String filePath;
  final int? fileSize;
  final String? mimeType;
  final DocumentType type;
  final DocumentStatus? status;
  final AccessLevel? accessLevel;
  final String? organizationId;
  final List<String>? tags;
  final Map<String, dynamic>? metadata;
  final String? thumbnailPath;
  final bool? isSearchable;
  final String? content;

  const CreateDocument({
    required this.title,
    this.description,
    required this.fileName,
    required this.filePath,
    this.fileSize,
    this.mimeType,
    required this.type,
    this.status,
    this.accessLevel,
    this.organizationId,
    this.tags,
    this.metadata,
    this.thumbnailPath,
    this.isSearchable,
    this.content,
  });

  @override
  List<Object?> get props => [
        title,
        description,
        fileName,
        filePath,
        fileSize,
        mimeType,
        type,
        status,
        accessLevel,
        organizationId,
        tags,
        metadata,
        thumbnailPath,
        isSearchable,
        content,
      ];
}

class UpdateDocument extends DocumentsEvent {
  final String documentId;
  final String? title;
  final String? description;
  final String? fileName;
  final String? filePath;
  final int? fileSize;
  final String? mimeType;
  final DocumentType? type;
  final DocumentStatus? status;
  final AccessLevel? accessLevel;
  final String? organizationId;
  final List<String>? tags;
  final Map<String, dynamic>? metadata;
  final String? thumbnailPath;
  final bool? isSearchable;
  final String? content;

  const UpdateDocument({
    required this.documentId,
    this.title,
    this.description,
    this.fileName,
    this.filePath,
    this.fileSize,
    this.mimeType,
    this.type,
    this.status,
    this.accessLevel,
    this.organizationId,
    this.tags,
    this.metadata,
    this.thumbnailPath,
    this.isSearchable,
    this.content,
  });

  @override
  List<Object?> get props => [
        documentId,
        title,
        description,
        fileName,
        filePath,
        fileSize,
        mimeType,
        type,
        status,
        accessLevel,
        organizationId,
        tags,
        metadata,
        thumbnailPath,
        isSearchable,
        content,
      ];
}

class DeleteDocument extends DocumentsEvent {
  final String documentId;

  const DeleteDocument(this.documentId);

  @override
  List<Object?> get props => [documentId];
}

class LoadDocumentStats extends DocumentsEvent {
  final String? organizationId;

  const LoadDocumentStats({this.organizationId});

  @override
  List<Object?> get props => [organizationId];
}

class SearchDocuments extends DocumentsEvent {
  final String query;
  final String? organizationId;

  const SearchDocuments({
    required this.query,
    this.organizationId,
  });

  @override
  List<Object?> get props => [query, organizationId];
}

// States
abstract class DocumentsState extends Equatable {
  const DocumentsState();

  @override
  List<Object?> get props => [];
}

class DocumentsInitial extends DocumentsState {}

class DocumentsLoading extends DocumentsState {}

class DocumentsLoaded extends DocumentsState {
  final List<Document> documents;
  final Pagination pagination;
  final DocumentStats? stats;

  const DocumentsLoaded({
    required this.documents,
    required this.pagination,
    this.stats,
  });

  @override
  List<Object?> get props => [documents, pagination, stats];
}

class DocumentDetailsLoaded extends DocumentsState {
  final Document document;

  const DocumentDetailsLoaded(this.document);

  @override
  List<Object?> get props => [document];
}

class DocumentsError extends DocumentsState {
  final String message;

  const DocumentsError(this.message);

  @override
  List<Object?> get props => [message];
}

class DocumentOperationSuccess extends DocumentsState {
  final String message;

  const DocumentOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class DocumentSearchResults extends DocumentsState {
  final List<Document> documents;
  final String query;

  const DocumentSearchResults({
    required this.documents,
    required this.query,
  });

  @override
  List<Object?> get props => [documents, query];
}

// Bloc
class DocumentsBloc extends Bloc<DocumentsEvent, DocumentsState> {
  final DocumentsApiService _documentsApiService;

  DocumentsBloc(this._documentsApiService) : super(DocumentsInitial()) {
    on<LoadDocuments>(_onLoadDocuments);
    on<LoadDocumentDetails>(_onLoadDocumentDetails);
    on<CreateDocument>(_onCreateDocument);
    on<UpdateDocument>(_onUpdateDocument);
    on<DeleteDocument>(_onDeleteDocument);
    on<LoadDocumentStats>(_onLoadDocumentStats);
    on<SearchDocuments>(_onSearchDocuments);
  }

  Future<void> _onLoadDocuments(
    LoadDocuments event,
    Emitter<DocumentsState> emit,
  ) async {
    emit(DocumentsLoading());

    try {
      final [documentsResponse, statsResponse] = await Future.wait([
        _documentsApiService.getDocuments(
          page: event.page,
          pageSize: event.pageSize,
          search: event.search,
          type: event.type,
          status: event.status,
          accessLevel: event.accessLevel,
          organizationId: event.organizationId,
          createdBy: event.createdBy,
          tags: event.tags,
          sortBy: event.sortBy,
          sortOrder: event.sortOrder,
          startDate: event.startDate,
          endDate: event.endDate,
          minSize: event.minSize,
          maxSize: event.maxSize,
        ),
        _documentsApiService.getDocumentStats(
          organizationId: event.organizationId,
        ),
      ]);

      emit(DocumentsLoaded(
        documents: (documentsResponse as PaginatedResponse<Document>).data,
        pagination: documentsResponse.pagination,
        stats: statsResponse as DocumentStats,
      ));
    } catch (e) {
      emit(DocumentsError('加载文档列表失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadDocumentDetails(
    LoadDocumentDetails event,
    Emitter<DocumentsState> emit,
  ) async {
    emit(DocumentsLoading());

    try {
      final document = await _documentsApiService.getDocument(event.documentId);
      emit(DocumentDetailsLoaded(document));
    } catch (e) {
      emit(DocumentsError('加载文档详情失败: ${e.toString()}'));
    }
  }

  Future<void> _onCreateDocument(
    CreateDocument event,
    Emitter<DocumentsState> emit,
  ) async {
    try {
      await _documentsApiService.createDocument(
        title: event.title,
        description: event.description,
        fileName: event.fileName,
        filePath: event.filePath,
        fileSize: event.fileSize,
        mimeType: event.mimeType,
        type: event.type,
        status: event.status,
        accessLevel: event.accessLevel,
        organizationId: event.organizationId,
        tags: event.tags,
        metadata: event.metadata,
        thumbnailPath: event.thumbnailPath,
        isSearchable: event.isSearchable,
        content: event.content,
      );

      emit(const DocumentOperationSuccess('文档创建成功'));

      // 重新加载文档列表
      add(const LoadDocuments());
    } catch (e) {
      emit(DocumentsError('创建文档失败: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateDocument(
    UpdateDocument event,
    Emitter<DocumentsState> emit,
  ) async {
    try {
      await _documentsApiService.updateDocument(
        event.documentId,
        title: event.title,
        description: event.description,
        fileName: event.fileName,
        filePath: event.filePath,
        fileSize: event.fileSize,
        mimeType: event.mimeType,
        type: event.type,
        status: event.status,
        accessLevel: event.accessLevel,
        organizationId: event.organizationId,
        tags: event.tags,
        metadata: event.metadata,
        thumbnailPath: event.thumbnailPath,
        isSearchable: event.isSearchable,
        content: event.content,
      );

      emit(const DocumentOperationSuccess('文档更新成功'));

      // 重新加载文档列表
      add(const LoadDocuments());
    } catch (e) {
      emit(DocumentsError('更新文档失败: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteDocument(
    DeleteDocument event,
    Emitter<DocumentsState> emit,
  ) async {
    try {
      await _documentsApiService.deleteDocument(event.documentId);
      emit(const DocumentOperationSuccess('文档删除成功'));

      // 重新加载文档列表
      add(const LoadDocuments());
    } catch (e) {
      emit(DocumentsError('删除文档失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadDocumentStats(
    LoadDocumentStats event,
    Emitter<DocumentsState> emit,
  ) async {
    try {
      final stats = await _documentsApiService.getDocumentStats(
        organizationId: event.organizationId,
      );

      if (state is DocumentsLoaded) {
        final currentState = state as DocumentsLoaded;
        emit(DocumentsLoaded(
          documents: currentState.documents,
          pagination: currentState.pagination,
          stats: stats,
        ));
      }
    } catch (e) {
      // 静默处理统计数据加载失败
    }
  }

  Future<void> _onSearchDocuments(
    SearchDocuments event,
    Emitter<DocumentsState> emit,
  ) async {
    emit(DocumentsLoading());

    try {
      final documents = await _documentsApiService.searchDocuments(
        query: event.query,
        organizationId: event.organizationId,
      );

      emit(DocumentSearchResults(
        documents: documents,
        query: event.query,
      ));
    } catch (e) {
      emit(DocumentsError('搜索文档失败: ${e.toString()}'));
    }
  }
}