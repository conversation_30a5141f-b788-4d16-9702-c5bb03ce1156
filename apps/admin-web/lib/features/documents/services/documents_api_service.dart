import 'package:dio/dio.dart';
import 'package:admin_web/shared/models/document.dart';
import 'package:admin_web/shared/models/pagination.dart';

class DocumentsApiService {
  final Dio _dio;

  DocumentsApiService(this._dio);

  Future<PaginatedResponse<Document>> getDocuments({
    int page = 1,
    int pageSize = 20,
    String? search,
    DocumentType? type,
    DocumentStatus? status,
    AccessLevel? accessLevel,
    String? organizationId,
    String? createdBy,
    List<String>? tags,
    String sortBy = 'createdAt',
    String sortOrder = 'DESC',
    String? startDate,
    String? endDate,
    int? minSize,
    int? maxSize,
  }) async {
    final response = await _dio.get('/documents', queryParameters: {
      'page': page,
      'pageSize': pageSize,
      if (search != null) 'search': search,
      if (type != null) 'type': type.name,
      if (status != null) 'status': status.name,
      if (accessLevel != null) 'accessLevel': accessLevel.name,
      if (organizationId != null) 'organizationId': organizationId,
      if (createdBy != null) 'createdBy': createdBy,
      if (tags != null) 'tags': tags,
      'sortBy': sortBy,
      'sortOrder': sortOrder,
      if (startDate != null) 'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
      if (minSize != null) 'minSize': minSize,
      if (maxSize != null) 'maxSize': maxSize,
    });

    return PaginatedResponse.fromJson(
      response.data,
      (json) => Document.fromJson(json as Map<String, dynamic>),
    );
  }

  Future<Document> getDocument(String id) async {
    final response = await _dio.get('/documents/$id');
    return Document.fromJson(response.data);
  }

  Future<Document> createDocument({
    required String title,
    String? description,
    required String fileName,
    required String filePath,
    int? fileSize,
    String? mimeType,
    required DocumentType type,
    DocumentStatus? status,
    AccessLevel? accessLevel,
    String? organizationId,
    List<String>? tags,
    Map<String, dynamic>? metadata,
    String? thumbnailPath,
    bool? isSearchable,
    String? content,
  }) async {
    final response = await _dio.post('/documents', data: {
      'title': title,
      if (description != null) 'description': description,
      'fileName': fileName,
      'filePath': filePath,
      if (fileSize != null) 'fileSize': fileSize,
      if (mimeType != null) 'mimeType': mimeType,
      'type': type.name,
      if (status != null) 'status': status.name,
      if (accessLevel != null) 'accessLevel': accessLevel.name,
      if (organizationId != null) 'organizationId': organizationId,
      if (tags != null) 'tags': tags,
      if (metadata != null) 'metadata': metadata,
      if (thumbnailPath != null) 'thumbnailPath': thumbnailPath,
      if (isSearchable != null) 'isSearchable': isSearchable,
      if (content != null) 'content': content,
    });

    return Document.fromJson(response.data);
  }

  Future<Document> updateDocument(
    String id, {
    String? title,
    String? description,
    String? fileName,
    String? filePath,
    int? fileSize,
    String? mimeType,
    DocumentType? type,
    DocumentStatus? status,
    AccessLevel? accessLevel,
    String? organizationId,
    List<String>? tags,
    Map<String, dynamic>? metadata,
    String? thumbnailPath,
    bool? isSearchable,
    String? content,
  }) async {
    final data = <String, dynamic>{};
    if (title != null) data['title'] = title;
    if (description != null) data['description'] = description;
    if (fileName != null) data['fileName'] = fileName;
    if (filePath != null) data['filePath'] = filePath;
    if (fileSize != null) data['fileSize'] = fileSize;
    if (mimeType != null) data['mimeType'] = mimeType;
    if (type != null) data['type'] = type.name;
    if (status != null) data['status'] = status.name;
    if (accessLevel != null) data['accessLevel'] = accessLevel.name;
    if (organizationId != null) data['organizationId'] = organizationId;
    if (tags != null) data['tags'] = tags;
    if (metadata != null) data['metadata'] = metadata;
    if (thumbnailPath != null) data['thumbnailPath'] = thumbnailPath;
    if (isSearchable != null) data['isSearchable'] = isSearchable;
    if (content != null) data['content'] = content;

    final response = await _dio.patch('/documents/$id', data: data);
    return Document.fromJson(response.data);
  }

  Future<void> deleteDocument(String id) async {
    await _dio.delete('/documents/$id');
  }

  Future<DocumentStats> getDocumentStats({String? organizationId}) async {
    final response = await _dio.get('/documents/stats', queryParameters: {
      if (organizationId != null) 'organizationId': organizationId,
    });

    return DocumentStats.fromJson(response.data);
  }

  Future<List<Document>> searchDocuments({
    required String query,
    String? organizationId,
  }) async {
    final response = await _dio.get('/documents/search', queryParameters: {
      'q': query,
      if (organizationId != null) 'organizationId': organizationId,
    });

    return (response.data as List)
        .map((item) => Document.fromJson(item))
        .toList();
  }

  Future<List<Document>> getPopularDocuments({
    String? organizationId,
    int limit = 10,
  }) async {
    final response = await _dio.get('/documents/popular', queryParameters: {
      if (organizationId != null) 'organizationId': organizationId,
      'limit': limit,
    });

    return (response.data as List)
        .map((item) => Document.fromJson(item))
        .toList();
  }

  Future<String> downloadDocument(String id) async {
    // 返回下载链接或触发下载
    return '/documents/$id/download';
  }

  Future<Map<String, dynamic>> uploadFile(
    String filePath, {
    Map<String, dynamic>? metadata,
  }) async {
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(filePath),
      if (metadata != null) ...metadata,
    });

    final response = await _dio.post('/documents/upload', data: formData);
    return response.data;
  }
}