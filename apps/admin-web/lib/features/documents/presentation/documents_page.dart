import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:intl/intl.dart';
import 'package:admin_web/shared/models/document.dart';
import 'package:admin_web/features/documents/bloc/documents_bloc.dart';
import 'package:admin_web/features/documents/services/documents_api_service.dart';
import 'package:admin_web/core/services/api_service.dart';

class DocumentsPage extends StatelessWidget {
  const DocumentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DocumentsBloc(
        DocumentsApiService(context.read<ApiService>().dio),
      )..add(const LoadDocuments()),
      child: const _DocumentsPageContent(),
    );
  }
}

class _DocumentsPageContent extends StatefulWidget {
  const _DocumentsPageContent();

  @override
  State<_DocumentsPageContent> createState() => _DocumentsPageContentState();
}

class _DocumentsPageContentState extends State<_DocumentsPageContent> {
  final TextEditingController _searchController = TextEditingController();
  DocumentType? _selectedType;
  DocumentStatus? _selectedStatus;
  AccessLevel? _selectedAccessLevel;
  int _currentPage = 1;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadDocuments() {
    context.read<DocumentsBloc>().add(LoadDocuments(
      page: _currentPage,
      search: _searchController.text.isEmpty ? null : _searchController.text,
      type: _selectedType,
      status: _selectedStatus,
      accessLevel: _selectedAccessLevel,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('文档管理'),
        actions: [
          ElevatedButton.icon(
            onPressed: () => _showUploadDialog(context),
            icon: const Icon(Icons.upload_file),
            label: const Text('上传文档'),
          ),
          const SizedBox(width: 16),
          IconButton(
            onPressed: _loadDocuments,
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: BlocListener<DocumentsBloc, DocumentsState>(
        listener: (context, state) {
          if (state is DocumentsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is DocumentOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildSearchAndFilters(),
              const SizedBox(height: 16),
              Expanded(child: _buildDocumentsTable()),
              _buildPagination(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索文档标题、描述或内容...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  _loadDocuments();
                },
              ),
            ),
            onSubmitted: (_) => _loadDocuments(),
          ),
        ),
        const SizedBox(width: 16),
        DropdownButton<DocumentType?>(
          value: _selectedType,
          hint: const Text('所有类型'),
          items: [
            const DropdownMenuItem(value: null, child: Text('所有类型')),
            ...DocumentType.values.map((type) => DropdownMenuItem(
              value: type,
              child: Text(_getTypeDisplayName(type)),
            )),
          ],
          onChanged: (value) {
            setState(() => _selectedType = value);
            _loadDocuments();
          },
        ),
        const SizedBox(width: 16),
        DropdownButton<DocumentStatus?>(
          value: _selectedStatus,
          hint: const Text('所有状态'),
          items: [
            const DropdownMenuItem(value: null, child: Text('所有状态')),
            ...DocumentStatus.values.map((status) => DropdownMenuItem(
              value: status,
              child: Text(_getStatusDisplayName(status)),
            )),
          ],
          onChanged: (value) {
            setState(() => _selectedStatus = value);
            _loadDocuments();
          },
        ),
        const SizedBox(width: 16),
        DropdownButton<AccessLevel?>(
          value: _selectedAccessLevel,
          hint: const Text('所有权限'),
          items: [
            const DropdownMenuItem(value: null, child: Text('所有权限')),
            ...AccessLevel.values.map((level) => DropdownMenuItem(
              value: level,
              child: Text(_getAccessLevelDisplayName(level)),
            )),
          ],
          onChanged: (value) {
            setState(() => _selectedAccessLevel = value);
            _loadDocuments();
          },
        ),
      ],
    );
  }

  Widget _buildDocumentsTable() {
    return BlocBuilder<DocumentsBloc, DocumentsState>(
      builder: (context, state) {
        if (state is DocumentsLoading) {
          return const Card(
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (state is DocumentsLoaded) {
          return Card(
            child: DataTable2(
              columnSpacing: 12,
              horizontalMargin: 12,
              minWidth: 800,
              columns: const [
                DataColumn2(label: Text('文档'), size: ColumnSize.L),
                DataColumn2(label: Text('类型'), size: ColumnSize.S),
                DataColumn2(label: Text('大小'), size: ColumnSize.S),
                DataColumn2(label: Text('状态'), size: ColumnSize.S),
                DataColumn2(label: Text('权限'), size: ColumnSize.S),
                DataColumn2(label: Text('查看/下载'), size: ColumnSize.M),
                DataColumn2(label: Text('创建时间'), size: ColumnSize.M),
                DataColumn2(label: Text('操作'), size: ColumnSize.M, fixedWidth: 140),
              ],
              rows: state.documents.map((doc) => _buildDocumentRow(doc)).toList(),
              empty: const Center(child: Text('暂无文档')),
            ),
          );
        }

        return const Card(
          child: Center(child: Text('加载失败')),
        );
      },
    );
  }

  DataRow _buildDocumentRow(Document document) {
    return DataRow(
      cells: [
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                document.title,
                style: const TextStyle(fontWeight: FontWeight.bold),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (document.description != null)
                Text(
                  document.description!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              Text(
                document.fileName,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[500],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        DataCell(
          Row(
            children: [
              Icon(
                _getTypeIcon(document.type),
                size: 20,
                color: _getTypeColor(document.type),
              ),
              const SizedBox(width: 4),
              Text(
                _getTypeDisplayName(document.type),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
        DataCell(Text(document.sizeFormatted)),
        DataCell(
          Chip(
            label: Text(
              document.statusDisplayName,
              style: const TextStyle(fontSize: 11),
            ),
            backgroundColor: _getStatusColor(document.status),
          ),
        ),
        DataCell(
          Chip(
            label: Text(
              document.accessLevelDisplayName,
              style: const TextStyle(fontSize: 11),
            ),
            backgroundColor: _getAccessLevelColor(document.accessLevel),
          ),
        ),
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  const Icon(Icons.visibility, size: 14),
                  const SizedBox(width: 2),
                  Text(
                    '${document.viewCount}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
              Row(
                children: [
                  const Icon(Icons.download, size: 14),
                  const SizedBox(width: 2),
                  Text(
                    '${document.downloadCount}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                DateFormat('yyyy-MM-dd').format(document.createdAt),
                style: const TextStyle(fontSize: 12),
              ),
              Text(
                DateFormat('HH:mm').format(document.createdAt),
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        DataCell(
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.visibility, size: 18),
                onPressed: () => _showDocumentDetails(context, document),
                tooltip: '查看详情',
              ),
              IconButton(
                icon: const Icon(Icons.download, size: 18),
                onPressed: () => _downloadDocument(document.id),
                tooltip: '下载',
              ),
              IconButton(
                icon: const Icon(Icons.edit, size: 18),
                onPressed: () => _showEditDocumentDialog(context, document),
                tooltip: '编辑',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 18),
                onPressed: () => _showDeleteConfirmDialog(context, document),
                tooltip: '删除',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPagination() {
    return BlocBuilder<DocumentsBloc, DocumentsState>(
      builder: (context, state) {
        if (state is! DocumentsLoaded) return const SizedBox.shrink();

        final pagination = state.pagination;
        final start = (pagination.page - 1) * pagination.pageSize + 1;
        final end = (start + state.documents.length - 1).clamp(0, pagination.total);

        return Card(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('显示 $start-$end 条，共 ${pagination.total} 条'),
                Row(
                  children: [
                    IconButton(
                      onPressed: pagination.hasPrev ? () {
                        setState(() => _currentPage--);
                        _loadDocuments();
                      } : null,
                      icon: const Icon(Icons.chevron_left),
                    ),
                    ...List.generate(
                      (pagination.totalPages).clamp(0, 5),
                      (index) {
                        final pageNum = index + 1;
                        return TextButton(
                          onPressed: pageNum == pagination.page ? null : () {
                            setState(() => _currentPage = pageNum);
                            _loadDocuments();
                          },
                          child: Text('$pageNum'),
                        );
                      },
                    ),
                    IconButton(
                      onPressed: pagination.hasNext ? () {
                        setState(() => _currentPage++);
                        _loadDocuments();
                      } : null,
                      icon: const Icon(Icons.chevron_right),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getTypeDisplayName(DocumentType type) {
    switch (type) {
      case DocumentType.pdf:
        return 'PDF';
      case DocumentType.word:
        return 'Word';
      case DocumentType.excel:
        return 'Excel';
      case DocumentType.powerpoint:
        return 'PPT';
      case DocumentType.text:
        return '文本';
      case DocumentType.image:
        return '图片';
      case DocumentType.video:
        return '视频';
      case DocumentType.audio:
        return '音频';
      case DocumentType.other:
        return '其他';
    }
  }

  String _getStatusDisplayName(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return '草稿';
      case DocumentStatus.published:
        return '已发布';
      case DocumentStatus.archived:
        return '已归档';
      case DocumentStatus.deleted:
        return '已删除';
    }
  }

  String _getAccessLevelDisplayName(AccessLevel level) {
    switch (level) {
      case AccessLevel.public:
        return '公开';
      case AccessLevel.internal:
        return '内部';
      case AccessLevel.private:
        return '私有';
      case AccessLevel.restricted:
        return '受限';
    }
  }

  IconData _getTypeIcon(DocumentType type) {
    switch (type) {
      case DocumentType.pdf:
        return Icons.picture_as_pdf;
      case DocumentType.word:
        return Icons.description;
      case DocumentType.excel:
        return Icons.table_chart;
      case DocumentType.powerpoint:
        return Icons.slideshow;
      case DocumentType.text:
        return Icons.text_snippet;
      case DocumentType.image:
        return Icons.image;
      case DocumentType.video:
        return Icons.video_file;
      case DocumentType.audio:
        return Icons.audio_file;
      case DocumentType.other:
        return Icons.insert_drive_file;
    }
  }

  Color _getTypeColor(DocumentType type) {
    switch (type) {
      case DocumentType.pdf:
        return Colors.red;
      case DocumentType.word:
        return Colors.blue;
      case DocumentType.excel:
        return Colors.green;
      case DocumentType.powerpoint:
        return Colors.orange;
      case DocumentType.text:
        return Colors.grey;
      case DocumentType.image:
        return Colors.purple;
      case DocumentType.video:
        return Colors.indigo;
      case DocumentType.audio:
        return Colors.teal;
      case DocumentType.other:
        return Colors.brown;
    }
  }

  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return Colors.orange.withValues(alpha: 0.2);
      case DocumentStatus.published:
        return Colors.green.withValues(alpha: 0.2);
      case DocumentStatus.archived:
        return Colors.grey.withValues(alpha: 0.2);
      case DocumentStatus.deleted:
        return Colors.red.withValues(alpha: 0.2);
    }
  }

  Color _getAccessLevelColor(AccessLevel level) {
    switch (level) {
      case AccessLevel.public:
        return Colors.green.withValues(alpha: 0.2);
      case AccessLevel.internal:
        return Colors.blue.withValues(alpha: 0.2);
      case AccessLevel.private:
        return Colors.orange.withValues(alpha: 0.2);
      case AccessLevel.restricted:
        return Colors.red.withValues(alpha: 0.2);
    }
  }

  void _showUploadDialog(BuildContext context) {
    // TODO: 实现文件上传对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('上传文档'),
        content: const Text('文件上传功能正在开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showDocumentDetails(BuildContext context, Document document) {
    // TODO: 实现文档详情对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(document.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('文件名: ${document.fileName}'),
            Text('大小: ${document.sizeFormatted}'),
            Text('类型: ${document.typeDisplayName}'),
            Text('状态: ${document.statusDisplayName}'),
            Text('权限: ${document.accessLevelDisplayName}'),
            if (document.description != null)
              Text('描述: ${document.description}'),
            Text('创建时间: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(document.createdAt)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showEditDocumentDialog(BuildContext context, Document document) {
    // TODO: 实现编辑文档对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑文档'),
        content: const Text('文档编辑功能正在开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmDialog(BuildContext context, Document document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除文档 "${document.title}" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<DocumentsBloc>().add(DeleteDocument(document.id));
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _downloadDocument(String documentId) {
    // TODO: 实现文档下载
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('下载功能正在开发中...')),
    );
  }
}