// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Document _$DocumentFromJson(Map<String, dynamic> json) => Document(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      mimeType: json['mimeType'] as String?,
      type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
      status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
      accessLevel: $enumDecode(_$AccessLevelEnumMap, json['accessLevel']),
      organizationId: json['organizationId'] as String?,
      createdBy: json['createdBy'] as String,
      creator: json['creator'] == null
          ? null
          : User.fromJson(json['creator'] as Map<String, dynamic>),
      updatedBy: json['updatedBy'] as String?,
      updater: json['updater'] == null
          ? null
          : User.fromJson(json['updater'] as Map<String, dynamic>),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      viewCount: (json['viewCount'] as num).toInt(),
      downloadCount: (json['downloadCount'] as num).toInt(),
      thumbnailPath: json['thumbnailPath'] as String?,
      isSearchable: json['isSearchable'] as bool,
      content: json['content'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DocumentToJson(Document instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'fileName': instance.fileName,
      'filePath': instance.filePath,
      'fileSize': instance.fileSize,
      'mimeType': instance.mimeType,
      'type': _$DocumentTypeEnumMap[instance.type]!,
      'status': _$DocumentStatusEnumMap[instance.status]!,
      'accessLevel': _$AccessLevelEnumMap[instance.accessLevel]!,
      'organizationId': instance.organizationId,
      'createdBy': instance.createdBy,
      'creator': instance.creator,
      'updatedBy': instance.updatedBy,
      'updater': instance.updater,
      'tags': instance.tags,
      'metadata': instance.metadata,
      'viewCount': instance.viewCount,
      'downloadCount': instance.downloadCount,
      'thumbnailPath': instance.thumbnailPath,
      'isSearchable': instance.isSearchable,
      'content': instance.content,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$DocumentTypeEnumMap = {
  DocumentType.pdf: 'pdf',
  DocumentType.word: 'word',
  DocumentType.excel: 'excel',
  DocumentType.powerpoint: 'powerpoint',
  DocumentType.text: 'text',
  DocumentType.image: 'image',
  DocumentType.video: 'video',
  DocumentType.audio: 'audio',
  DocumentType.other: 'other',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.draft: 'draft',
  DocumentStatus.published: 'published',
  DocumentStatus.archived: 'archived',
  DocumentStatus.deleted: 'deleted',
};

const _$AccessLevelEnumMap = {
  AccessLevel.public: 'public',
  AccessLevel.internal: 'internal',
  AccessLevel.private: 'private',
  AccessLevel.restricted: 'restricted',
};

DocumentStats _$DocumentStatsFromJson(Map<String, dynamic> json) =>
    DocumentStats(
      overview: DocumentOverviewStats.fromJson(
          json['overview'] as Map<String, dynamic>),
      activity: DocumentActivityStats.fromJson(
          json['activity'] as Map<String, dynamic>),
      distribution: DocumentDistributionStats.fromJson(
          json['distribution'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DocumentStatsToJson(DocumentStats instance) =>
    <String, dynamic>{
      'overview': instance.overview,
      'activity': instance.activity,
      'distribution': instance.distribution,
    };

DocumentOverviewStats _$DocumentOverviewStatsFromJson(
        Map<String, dynamic> json) =>
    DocumentOverviewStats(
      totalDocuments: (json['totalDocuments'] as num).toInt(),
      publishedDocuments: (json['publishedDocuments'] as num).toInt(),
      draftDocuments: (json['draftDocuments'] as num).toInt(),
      archivedDocuments: (json['archivedDocuments'] as num).toInt(),
      recentDocuments: (json['recentDocuments'] as num).toInt(),
    );

Map<String, dynamic> _$DocumentOverviewStatsToJson(
        DocumentOverviewStats instance) =>
    <String, dynamic>{
      'totalDocuments': instance.totalDocuments,
      'publishedDocuments': instance.publishedDocuments,
      'draftDocuments': instance.draftDocuments,
      'archivedDocuments': instance.archivedDocuments,
      'recentDocuments': instance.recentDocuments,
    };

DocumentActivityStats _$DocumentActivityStatsFromJson(
        Map<String, dynamic> json) =>
    DocumentActivityStats(
      totalViews: (json['totalViews'] as num).toInt(),
      totalDownloads: (json['totalDownloads'] as num).toInt(),
    );

Map<String, dynamic> _$DocumentActivityStatsToJson(
        DocumentActivityStats instance) =>
    <String, dynamic>{
      'totalViews': instance.totalViews,
      'totalDownloads': instance.totalDownloads,
    };

DocumentDistributionStats _$DocumentDistributionStatsFromJson(
        Map<String, dynamic> json) =>
    DocumentDistributionStats(
      byType: (json['byType'] as List<dynamic>)
          .map((e) =>
              DocumentTypeDistribution.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DocumentDistributionStatsToJson(
        DocumentDistributionStats instance) =>
    <String, dynamic>{
      'byType': instance.byType,
    };

DocumentTypeDistribution _$DocumentTypeDistributionFromJson(
        Map<String, dynamic> json) =>
    DocumentTypeDistribution(
      type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
      count: (json['count'] as num).toInt(),
    );

Map<String, dynamic> _$DocumentTypeDistributionToJson(
        DocumentTypeDistribution instance) =>
    <String, dynamic>{
      'type': _$DocumentTypeEnumMap[instance.type]!,
      'count': instance.count,
    };
