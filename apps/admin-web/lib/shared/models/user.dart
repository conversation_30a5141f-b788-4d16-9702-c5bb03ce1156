import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user.g.dart';

enum UserRole {
  @JsonValue('admin')
  admin,
  @JsonValue('user')
  user,
}

enum UserStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
}

@JsonSerializable()
class User extends Equatable {
  final String id;
  final String email;
  final String username;
  final String? firstName;
  final String? lastName;
  final String? avatar;
  final UserRole role;
  final UserStatus status;
  final String? organizationId;
  final String? phone;
  final String? bio;
  final Map<String, dynamic>? preferences;
  final DateTime? lastLoginAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? fullName;

  const User({
    required this.id,
    required this.email,
    required this.username,
    this.firstName,
    this.lastName,
    this.avatar,
    required this.role,
    required this.status,
    this.organizationId,
    this.phone,
    this.bio,
    this.preferences,
    this.lastLoginAt,
    required this.createdAt,
    required this.updatedAt,
    this.fullName,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  String get displayName => fullName ?? username;
  
  String get statusDisplayName {
    switch (status) {
      case UserStatus.active:
        return '活跃';
      case UserStatus.inactive:
        return '未激活';
      case UserStatus.suspended:
        return '已暂停';
    }
  }
  
  String get roleDisplayName {
    switch (role) {
      case UserRole.admin:
        return '管理员';
      case UserRole.user:
        return '普通用户';
    }
  }

  @override
  List<Object?> get props => [
        id,
        email,
        username,
        firstName,
        lastName,
        avatar,
        role,
        status,
        organizationId,
        phone,
        bio,
        preferences,
        lastLoginAt,
        createdAt,
        updatedAt,
        fullName,
      ];
}