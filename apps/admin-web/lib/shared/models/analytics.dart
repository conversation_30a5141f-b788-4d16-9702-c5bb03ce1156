import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'analytics.g.dart';

enum AnalyticsEventType {
  @JsonValue('login')
  login,
  @JsonValue('logout')
  logout,
  @JsonValue('document_view')
  documentView,
  @JsonValue('document_download')
  documentDownload,
  @JsonValue('search')
  search,
  @JsonValue('user_created')
  userCreated,
  @JsonValue('user_updated')
  userUpdated,
  @JsonValue('session_start')
  sessionStart,
  @JsonValue('session_end')
  sessionEnd,
}

@JsonSerializable()
class DashboardStats extends Equatable {
  final OverviewStats overview;
  final ActivityStats activity;
  final TimeRangeInfo timeRange;

  const DashboardStats({
    required this.overview,
    required this.activity,
    required this.timeRange,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) =>
      _$DashboardStatsFromJson(json);
  Map<String, dynamic> toJson() => _$DashboardStatsToJson(this);

  @override
  List<Object?> get props => [overview, activity, timeRange];
}

@JsonSerializable()
class OverviewStats extends Equatable {
  final int totalUsers;
  final int activeUsers;
  final int adminUsers;
  final int newUsers;
  final double newUsersGrowth;

  const OverviewStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.adminUsers,
    required this.newUsers,
    required this.newUsersGrowth,
  });

  factory OverviewStats.fromJson(Map<String, dynamic> json) =>
      _$OverviewStatsFromJson(json);
  Map<String, dynamic> toJson() => _$OverviewStatsToJson(this);

  @override
  List<Object?> get props => [totalUsers, activeUsers, adminUsers, newUsers, newUsersGrowth];
}

@JsonSerializable()
class ActivityStats extends Equatable {
  final int totalSessions;
  final int totalEvents;
  final int documentViews;
  final int searches;
  final double sessionsGrowth;
  final double documentViewsGrowth;

  const ActivityStats({
    required this.totalSessions,
    required this.totalEvents,
    required this.documentViews,
    required this.searches,
    required this.sessionsGrowth,
    required this.documentViewsGrowth,
  });

  factory ActivityStats.fromJson(Map<String, dynamic> json) =>
      _$ActivityStatsFromJson(json);
  Map<String, dynamic> toJson() => _$ActivityStatsToJson(this);

  @override
  List<Object?> get props => [totalSessions, totalEvents, documentViews, searches, sessionsGrowth, documentViewsGrowth];
}

@JsonSerializable()
class TimeRangeInfo extends Equatable {
  final int days;
  final DateTime startDate;
  final DateTime endDate;

  const TimeRangeInfo({
    required this.days,
    required this.startDate,
    required this.endDate,
  });

  factory TimeRangeInfo.fromJson(Map<String, dynamic> json) =>
      _$TimeRangeInfoFromJson(json);
  Map<String, dynamic> toJson() => _$TimeRangeInfoToJson(this);

  @override
  List<Object?> get props => [days, startDate, endDate];
}

@JsonSerializable()
class UserActivityTrend extends Equatable {
  final DateTime date;
  final int activeUsers;
  final int totalEvents;

  const UserActivityTrend({
    required this.date,
    required this.activeUsers,
    required this.totalEvents,
  });

  factory UserActivityTrend.fromJson(Map<String, dynamic> json) =>
      _$UserActivityTrendFromJson(json);
  Map<String, dynamic> toJson() => _$UserActivityTrendToJson(this);

  @override
  List<Object?> get props => [date, activeUsers, totalEvents];
}

@JsonSerializable()
class EventTypeDistribution extends Equatable {
  final AnalyticsEventType eventType;
  final int count;

  const EventTypeDistribution({
    required this.eventType,
    required this.count,
  });

  factory EventTypeDistribution.fromJson(Map<String, dynamic> json) =>
      _$EventTypeDistributionFromJson(json);
  Map<String, dynamic> toJson() => _$EventTypeDistributionToJson(this);

  String get displayName {
    switch (eventType) {
      case AnalyticsEventType.login:
        return '登录';
      case AnalyticsEventType.logout:
        return '登出';
      case AnalyticsEventType.documentView:
        return '文档查看';
      case AnalyticsEventType.documentDownload:
        return '文档下载';
      case AnalyticsEventType.search:
        return '搜索';
      case AnalyticsEventType.userCreated:
        return '用户创建';
      case AnalyticsEventType.userUpdated:
        return '用户更新';
      case AnalyticsEventType.sessionStart:
        return '会话开始';
      case AnalyticsEventType.sessionEnd:
        return '会话结束';
    }
  }

  @override
  List<Object?> get props => [eventType, count];
}

@JsonSerializable()
class TopActiveUser extends Equatable {
  final String userId;
  final String? username;
  final String? email;
  final int activityCount;
  final int activeDays;

  const TopActiveUser({
    required this.userId,
    this.username,
    this.email,
    required this.activityCount,
    required this.activeDays,
  });

  factory TopActiveUser.fromJson(Map<String, dynamic> json) =>
      _$TopActiveUserFromJson(json);
  Map<String, dynamic> toJson() => _$TopActiveUserToJson(this);

  String get displayName => username ?? email ?? 'Unknown User';

  @override
  List<Object?> get props => [userId, username, email, activityCount, activeDays];
}

@JsonSerializable()
class HourlyActivityPattern extends Equatable {
  final int hour;
  final int eventCount;
  final int uniqueUsers;

  const HourlyActivityPattern({
    required this.hour,
    required this.eventCount,
    required this.uniqueUsers,
  });

  factory HourlyActivityPattern.fromJson(Map<String, dynamic> json) =>
      _$HourlyActivityPatternFromJson(json);
  Map<String, dynamic> toJson() => _$HourlyActivityPatternToJson(this);

  @override
  List<Object?> get props => [hour, eventCount, uniqueUsers];
}

@JsonSerializable()
class AnalyticsEvent extends Equatable {
  final String id;
  final AnalyticsEventType eventType;
  final String? userId;
  final String? organizationId;
  final String? sessionId;
  final Map<String, dynamic>? metadata;
  final String? ipAddress;
  final String? userAgent;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AnalyticsEvent({
    required this.id,
    required this.eventType,
    this.userId,
    this.organizationId,
    this.sessionId,
    this.metadata,
    this.ipAddress,
    this.userAgent,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsEventFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsEventToJson(this);

  @override
  List<Object?> get props => [
        id,
        eventType,
        userId,
        organizationId,
        sessionId,
        metadata,
        ipAddress,
        userAgent,
        createdAt,
        updatedAt,
      ];
}