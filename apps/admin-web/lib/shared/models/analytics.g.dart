// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardStats _$DashboardStatsFromJson(Map<String, dynamic> json) =>
    DashboardStats(
      overview:
          OverviewStats.fromJson(json['overview'] as Map<String, dynamic>),
      activity:
          ActivityStats.fromJson(json['activity'] as Map<String, dynamic>),
      timeRange:
          TimeRangeInfo.fromJson(json['timeRange'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DashboardStatsToJson(DashboardStats instance) =>
    <String, dynamic>{
      'overview': instance.overview,
      'activity': instance.activity,
      'timeRange': instance.timeRange,
    };

OverviewStats _$OverviewStatsFromJson(Map<String, dynamic> json) =>
    OverviewStats(
      totalUsers: (json['totalUsers'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      adminUsers: (json['adminUsers'] as num).toInt(),
      newUsers: (json['newUsers'] as num).toInt(),
      newUsersGrowth: (json['newUsersGrowth'] as num).toDouble(),
    );

Map<String, dynamic> _$OverviewStatsToJson(OverviewStats instance) =>
    <String, dynamic>{
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'adminUsers': instance.adminUsers,
      'newUsers': instance.newUsers,
      'newUsersGrowth': instance.newUsersGrowth,
    };

ActivityStats _$ActivityStatsFromJson(Map<String, dynamic> json) =>
    ActivityStats(
      totalSessions: (json['totalSessions'] as num).toInt(),
      totalEvents: (json['totalEvents'] as num).toInt(),
      documentViews: (json['documentViews'] as num).toInt(),
      searches: (json['searches'] as num).toInt(),
      sessionsGrowth: (json['sessionsGrowth'] as num).toDouble(),
      documentViewsGrowth: (json['documentViewsGrowth'] as num).toDouble(),
    );

Map<String, dynamic> _$ActivityStatsToJson(ActivityStats instance) =>
    <String, dynamic>{
      'totalSessions': instance.totalSessions,
      'totalEvents': instance.totalEvents,
      'documentViews': instance.documentViews,
      'searches': instance.searches,
      'sessionsGrowth': instance.sessionsGrowth,
      'documentViewsGrowth': instance.documentViewsGrowth,
    };

TimeRangeInfo _$TimeRangeInfoFromJson(Map<String, dynamic> json) =>
    TimeRangeInfo(
      days: (json['days'] as num).toInt(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
    );

Map<String, dynamic> _$TimeRangeInfoToJson(TimeRangeInfo instance) =>
    <String, dynamic>{
      'days': instance.days,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
    };

UserActivityTrend _$UserActivityTrendFromJson(Map<String, dynamic> json) =>
    UserActivityTrend(
      date: DateTime.parse(json['date'] as String),
      activeUsers: (json['activeUsers'] as num).toInt(),
      totalEvents: (json['totalEvents'] as num).toInt(),
    );

Map<String, dynamic> _$UserActivityTrendToJson(UserActivityTrend instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'activeUsers': instance.activeUsers,
      'totalEvents': instance.totalEvents,
    };

EventTypeDistribution _$EventTypeDistributionFromJson(
        Map<String, dynamic> json) =>
    EventTypeDistribution(
      eventType: $enumDecode(_$AnalyticsEventTypeEnumMap, json['eventType']),
      count: (json['count'] as num).toInt(),
    );

Map<String, dynamic> _$EventTypeDistributionToJson(
        EventTypeDistribution instance) =>
    <String, dynamic>{
      'eventType': _$AnalyticsEventTypeEnumMap[instance.eventType]!,
      'count': instance.count,
    };

const _$AnalyticsEventTypeEnumMap = {
  AnalyticsEventType.login: 'login',
  AnalyticsEventType.logout: 'logout',
  AnalyticsEventType.documentView: 'document_view',
  AnalyticsEventType.documentDownload: 'document_download',
  AnalyticsEventType.search: 'search',
  AnalyticsEventType.userCreated: 'user_created',
  AnalyticsEventType.userUpdated: 'user_updated',
  AnalyticsEventType.sessionStart: 'session_start',
  AnalyticsEventType.sessionEnd: 'session_end',
};

TopActiveUser _$TopActiveUserFromJson(Map<String, dynamic> json) =>
    TopActiveUser(
      userId: json['userId'] as String,
      username: json['username'] as String?,
      email: json['email'] as String?,
      activityCount: (json['activityCount'] as num).toInt(),
      activeDays: (json['activeDays'] as num).toInt(),
    );

Map<String, dynamic> _$TopActiveUserToJson(TopActiveUser instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'email': instance.email,
      'activityCount': instance.activityCount,
      'activeDays': instance.activeDays,
    };

HourlyActivityPattern _$HourlyActivityPatternFromJson(
        Map<String, dynamic> json) =>
    HourlyActivityPattern(
      hour: (json['hour'] as num).toInt(),
      eventCount: (json['eventCount'] as num).toInt(),
      uniqueUsers: (json['uniqueUsers'] as num).toInt(),
    );

Map<String, dynamic> _$HourlyActivityPatternToJson(
        HourlyActivityPattern instance) =>
    <String, dynamic>{
      'hour': instance.hour,
      'eventCount': instance.eventCount,
      'uniqueUsers': instance.uniqueUsers,
    };

AnalyticsEvent _$AnalyticsEventFromJson(Map<String, dynamic> json) =>
    AnalyticsEvent(
      id: json['id'] as String,
      eventType: $enumDecode(_$AnalyticsEventTypeEnumMap, json['eventType']),
      userId: json['userId'] as String?,
      organizationId: json['organizationId'] as String?,
      sessionId: json['sessionId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$AnalyticsEventToJson(AnalyticsEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'eventType': _$AnalyticsEventTypeEnumMap[instance.eventType]!,
      'userId': instance.userId,
      'organizationId': instance.organizationId,
      'sessionId': instance.sessionId,
      'metadata': instance.metadata,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
