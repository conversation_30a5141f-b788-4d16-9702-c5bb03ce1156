import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Controllers (to be implemented)
// import { MobileProfileController } from './profile/mobile-profile.controller';
// import { MobileLearningController } from './learning/mobile-learning.controller';
// import { MobileDocumentsController } from './documents/mobile-documents.controller';

// Services
import { UsersService } from '@/modules/users/services/users.service';
import { CacheService } from '@/core/cache/cache.service';

// Entities
import { User } from '@/shared/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
  ],
  controllers: [
    // TODO: Add mobile controllers
    // MobileProfileController,
    // MobileLearningController,
    // MobileDocumentsController,
  ],
  providers: [
    UsersService,
    CacheService,
    // TODO: Add other mobile-specific services
    // LearningService,
    // NotificationService,
  ],
})
export class MobileApiModule {}