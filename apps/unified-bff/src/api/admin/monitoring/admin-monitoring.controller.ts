import {
  Controller,
  Get,
  Post,
  Delete,
  Query,
  UseGuards,
  Version,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '@/core/auth/guards/permissions.guard';
import { RequirePermissions } from '@/core/auth/decorators/permissions.decorator';
import { Permission } from '@/shared/enums/permission.enum';
import { MonitoringService } from '@/core/monitoring/monitoring.service';
import { LoggingService } from '@/core/logging/logging.service';

@ApiTags('Admin - System Monitoring')
@ApiSecurity('JWT-auth')
@Controller({ path: 'admin/monitoring', version: '1' })
@UseGuards(JwtAuthGuard, PermissionsGuard)
@RequirePermissions(Permission.SYSTEM_CONFIG)
export class AdminMonitoringController {
  constructor(
    private readonly monitoringService: MonitoringService,
    private readonly loggingService: LoggingService,
  ) {}

  @Get('health')
  @ApiOperation({ summary: '获取系统健康状态' })
  @ApiResponse({
    status: 200,
    description: '返回系统健康状态',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
        timestamp: { type: 'string', format: 'date-time' },
        uptime: { type: 'number', description: '系统运行时间（秒）' },
        version: { type: 'string', description: '应用版本' },
        checks: {
          type: 'object',
          properties: {
            database: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['up', 'down'] },
                responseTime: { type: 'number' },
                error: { type: 'string' },
              },
            },
            redis: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['up', 'down'] },
                responseTime: { type: 'number' },
                error: { type: 'string' },
              },
            },
            disk: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['ok', 'warning', 'critical'] },
                usage: { type: 'number' },
              },
            },
            memory: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['ok', 'warning', 'critical'] },
                usage: { type: 'number' },
              },
            },
            cpu: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['ok', 'warning', 'critical'] },
                usage: { type: 'number' },
              },
            },
          },
        },
      },
    },
  })
  async getHealthStatus() {
    return this.monitoringService.getHealthStatus();
  }

  @Get('metrics/system')
  @ApiOperation({ summary: '获取系统指标' })
  @ApiResponse({
    status: 200,
    description: '返回系统性能指标',
    schema: {
      type: 'object',
      properties: {
        timestamp: { type: 'string', format: 'date-time' },
        cpu: {
          type: 'object',
          properties: {
            usage: { type: 'number', description: 'CPU使用率（%）' },
            cores: { type: 'number', description: 'CPU核心数' },
            loadAvg: { type: 'array', items: { type: 'number' }, description: '负载平均值' },
          },
        },
        memory: {
          type: 'object',
          properties: {
            total: { type: 'number', description: '总内存（字节）' },
            free: { type: 'number', description: '空闲内存（字节）' },
            used: { type: 'number', description: '已用内存（字节）' },
            usagePercent: { type: 'number', description: '内存使用率（%）' },
            heap: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                used: { type: 'number' },
                external: { type: 'number' },
              },
            },
          },
        },
        disk: {
          type: 'object',
          properties: {
            free: { type: 'number' },
            used: { type: 'number' },
            total: { type: 'number' },
            usagePercent: { type: 'number' },
          },
        },
        uptime: { type: 'number', description: '系统运行时间（秒）' },
        pid: { type: 'number', description: '进程ID' },
      },
    },
  })
  async getSystemMetrics() {
    return this.monitoringService.getSystemMetrics();
  }

  @Get('metrics/application')
  @ApiOperation({ summary: '获取应用指标' })
  @ApiResponse({
    status: 200,
    description: '返回应用性能指标',
    schema: {
      type: 'object',
      properties: {
        timestamp: { type: 'string', format: 'date-time' },
        requests: {
          type: 'object',
          properties: {
            total: { type: 'number', description: '总请求数' },
            successful: { type: 'number', description: '成功请求数' },
            failed: { type: 'number', description: '失败请求数' },
            averageResponseTime: { type: 'number', description: '平均响应时间（毫秒）' },
            requestsPerSecond: { type: 'number', description: '每秒请求数' },
          },
        },
        errors: {
          type: 'object',
          properties: {
            total: { type: 'number', description: '总错误数' },
            rate: { type: 'number', description: '错误率' },
            byType: { type: 'object', description: '按类型分组的错误统计' },
          },
        },
        database: {
          type: 'object',
          properties: {
            connections: {
              type: 'object',
              properties: {
                active: { type: 'number' },
                idle: { type: 'number' },
                total: { type: 'number' },
              },
            },
            queries: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                slow: { type: 'number' },
                averageTime: { type: 'number' },
              },
            },
          },
        },
        cache: {
          type: 'object',
          properties: {
            hits: { type: 'number' },
            misses: { type: 'number' },
            hitRate: { type: 'number' },
            operations: { type: 'number' },
          },
        },
        auth: {
          type: 'object',
          properties: {
            logins: { type: 'number' },
            logouts: { type: 'number' },
            activeUsers: { type: 'number' },
            failures: { type: 'number' },
          },
        },
      },
    },
  })
  async getApplicationMetrics() {
    return this.monitoringService.getApplicationMetrics();
  }

  @Get('logs/search')
  @ApiOperation({ summary: '搜索系统日志' })
  @ApiQuery({ name: 'level', required: false, type: String, description: '日志级别' })
  @ApiQuery({ name: 'context', required: false, type: String, description: '日志上下文' })
  @ApiQuery({ name: 'startTime', required: false, type: String, description: '开始时间' })
  @ApiQuery({ name: 'endTime', required: false, type: String, description: '结束时间' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '结果限制数量' })
  @ApiResponse({
    status: 200,
    description: '返回日志搜索结果',
  })
  async searchLogs(
    @Query('level') level?: string,
    @Query('context') context?: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
    @Query('limit') limit: number = 100,
  ) {
    // 这里应该实现日志搜索功能
    // 简化实现，实际需要集成日志搜索引擎
    return {
      logs: [],
      total: 0,
      filters: {
        level,
        context,
        startTime,
        endTime,
        limit,
      },
      message: '日志搜索功能正在开发中',
    };
  }

  @Get('alerts')
  @ApiOperation({ summary: '获取系统告警' })
  @ApiQuery({ name: 'severity', required: false, type: String, description: '告警级别' })
  @ApiQuery({ name: 'status', required: false, type: String, description: '告警状态' })
  @ApiResponse({
    status: 200,
    description: '返回系统告警列表',
  })
  async getAlerts(
    @Query('severity') severity?: string,
    @Query('status') status?: string,
  ) {
    // 这里应该实现告警查询功能
    // 简化实现
    const alerts = [
      {
        id: '1',
        severity: 'warning',
        title: 'High Memory Usage',
        message: '内存使用率超过75%',
        timestamp: new Date().toISOString(),
        status: 'active',
        metadata: {
          currentUsage: 78,
          threshold: 75,
        },
      },
      {
        id: '2',
        severity: 'info',
        title: 'High Request Volume',
        message: '请求量异常增高',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        status: 'resolved',
        metadata: {
          requestsPerMinute: 1200,
          normalRange: '200-800',
        },
      },
    ];

    let filteredAlerts = alerts;

    if (severity) {
      filteredAlerts = filteredAlerts.filter(alert => alert.severity === severity);
    }

    if (status) {
      filteredAlerts = filteredAlerts.filter(alert => alert.status === status);
    }

    return {
      alerts: filteredAlerts,
      total: filteredAlerts.length,
      summary: {
        critical: alerts.filter(a => a.severity === 'critical').length,
        warning: alerts.filter(a => a.severity === 'warning').length,
        info: alerts.filter(a => a.severity === 'info').length,
        active: alerts.filter(a => a.status === 'active').length,
        resolved: alerts.filter(a => a.status === 'resolved').length,
      },
    };
  }

  @Post('metrics/reset')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重置应用指标' })
  @ApiResponse({
    status: 200,
    description: '指标重置成功',
  })
  async resetMetrics() {
    this.monitoringService.resetMetrics();
    
    this.loggingService.logBusinessEvent('METRICS_RESET', {
      timestamp: new Date().toISOString(),
    });

    return {
      message: '应用指标已重置',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('performance/top-endpoints')
  @ApiOperation({ summary: '获取性能最差的API端点' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '结果限制数量' })
  @ApiResponse({
    status: 200,
    description: '返回性能最差的API端点列表',
  })
  async getTopSlowEndpoints(@Query('limit') limit: number = 10) {
    // 这里应该从监控数据中获取最慢的端点
    // 简化实现
    const endpoints = [
      {
        method: 'GET',
        path: '/api/v1/analytics/reports',
        averageResponseTime: 2341,
        maxResponseTime: 5620,
        requestCount: 145,
        errorRate: 0.02,
      },
      {
        method: 'POST',
        path: '/api/v1/documents/search',
        averageResponseTime: 1876,
        maxResponseTime: 4200,
        requestCount: 892,
        errorRate: 0.01,
      },
      {
        method: 'GET',
        path: '/api/v1/users/export',
        averageResponseTime: 1634,
        maxResponseTime: 3800,
        requestCount: 23,
        errorRate: 0.00,
      },
    ];

    return {
      endpoints: endpoints.slice(0, limit),
      total: endpoints.length,
      period: 'last 24 hours',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('performance/database')
  @ApiOperation({ summary: '获取数据库性能指标' })
  @ApiResponse({
    status: 200,
    description: '返回数据库性能指标',
  })
  async getDatabasePerformance() {
    return {
      connections: {
        active: 5,
        idle: 15,
        total: 20,
        maxPoolSize: 25,
      },
      queries: {
        total: 15420,
        successful: 15398,
        failed: 22,
        slow: 45,
        averageTime: 125,
        slowestQuery: {
          query: 'SELECT * FROM users WHERE ...',
          duration: 2340,
          timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        },
      },
      transactions: {
        total: 1240,
        committed: 1238,
        rolledBack: 2,
        averageTime: 89,
      },
      locks: {
        waiting: 0,
        blocked: 0,
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('diagnostics')
  @ApiOperation({ summary: '运行系统诊断' })
  @ApiResponse({
    status: 200,
    description: '返回系统诊断结果',
  })
  async runDiagnostics() {
    const systemMetrics = await this.monitoringService.getSystemMetrics();
    const appMetrics = this.monitoringService.getApplicationMetrics();
    const healthStatus = await this.monitoringService.getHealthStatus();

    const diagnostics = {
      timestamp: new Date().toISOString(),
      overall: healthStatus.status,
      issues: [] as Array<{
        severity: 'critical' | 'warning' | 'info';
        category: string;
        message: string;
        recommendation?: string;
      }>,
      summary: {
        totalChecks: 10,
        passed: 0,
        warnings: 0,
        errors: 0,
      },
    };

    // CPU检查
    if (systemMetrics.cpu.usage > 90) {
      diagnostics.issues.push({
        severity: 'critical',
        category: 'Performance',
        message: `CPU使用率过高: ${systemMetrics.cpu.usage.toFixed(1)}%`,
        recommendation: '检查是否有资源密集型进程，考虑扩容或优化代码',
      });
      diagnostics.summary.errors++;
    } else if (systemMetrics.cpu.usage > 75) {
      diagnostics.issues.push({
        severity: 'warning',
        category: 'Performance',
        message: `CPU使用率较高: ${systemMetrics.cpu.usage.toFixed(1)}%`,
        recommendation: '监控CPU使用趋势，准备扩容计划',
      });
      diagnostics.summary.warnings++;
    } else {
      diagnostics.summary.passed++;
    }

    // 内存检查
    if (systemMetrics.memory.usagePercent > 90) {
      diagnostics.issues.push({
        severity: 'critical',
        category: 'Performance',
        message: `内存使用率过高: ${systemMetrics.memory.usagePercent.toFixed(1)}%`,
        recommendation: '检查内存泄漏，增加内存或优化内存使用',
      });
      diagnostics.summary.errors++;
    } else if (systemMetrics.memory.usagePercent > 75) {
      diagnostics.issues.push({
        severity: 'warning',
        category: 'Performance',
        message: `内存使用率较高: ${systemMetrics.memory.usagePercent.toFixed(1)}%`,
        recommendation: '监控内存使用趋势，检查是否有内存泄漏',
      });
      diagnostics.summary.warnings++;
    } else {
      diagnostics.summary.passed++;
    }

    // 错误率检查
    if (appMetrics.errors.rate > 0.05) {
      diagnostics.issues.push({
        severity: 'critical',
        category: 'Reliability',
        message: `错误率过高: ${(appMetrics.errors.rate * 100).toFixed(2)}%`,
        recommendation: '检查应用日志，修复导致错误的问题',
      });
      diagnostics.summary.errors++;
    } else if (appMetrics.errors.rate > 0.01) {
      diagnostics.issues.push({
        severity: 'warning',
        category: 'Reliability',
        message: `错误率较高: ${(appMetrics.errors.rate * 100).toFixed(2)}%`,
        recommendation: '关注错误趋势，优化异常处理',
      });
      diagnostics.summary.warnings++;
    } else {
      diagnostics.summary.passed++;
    }

    // 响应时间检查
    if (appMetrics.requests.averageResponseTime > 2000) {
      diagnostics.issues.push({
        severity: 'warning',
        category: 'Performance',
        message: `平均响应时间过长: ${appMetrics.requests.averageResponseTime.toFixed(0)}ms`,
        recommendation: '优化慢查询，检查网络延迟，考虑添加缓存',
      });
      diagnostics.summary.warnings++;
    } else {
      diagnostics.summary.passed++;
    }

    // 缓存命中率检查
    if (appMetrics.cache.hitRate < 0.5) {
      diagnostics.issues.push({
        severity: 'info',
        category: 'Performance',
        message: `缓存命中率较低: ${(appMetrics.cache.hitRate * 100).toFixed(1)}%`,
        recommendation: '检查缓存策略，优化缓存键设计',
      });
      diagnostics.summary.warnings++;
    } else {
      diagnostics.summary.passed++;
    }

    // 如果没有发现问题，添加一些正面的反馈
    if (diagnostics.issues.length === 0) {
      diagnostics.issues.push({
        severity: 'info',
        category: 'System',
        message: '系统运行正常，所有指标都在健康范围内',
      });
    }

    return diagnostics;
  }
}