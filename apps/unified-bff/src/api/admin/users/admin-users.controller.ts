import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseUUI<PERSON>ipe,
  HttpCode,
  HttpStatus,
  Version,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiSecurity,
} from '@nestjs/swagger';
import { UsersService } from '@/modules/users/services/users.service';
import { CreateUserDto } from '@/modules/users/dto/create-user.dto';
import { UpdateUserDto } from '@/modules/users/dto/update-user.dto';
import { UserQueryDto } from '@/modules/users/dto/user-query.dto';
import { UserResponseDto, UserStatsDto } from '@/modules/users/dto/user-response.dto';
import { PaginatedResponseDto } from '@/shared/dto/pagination.dto';
import { MessageResponseDto } from '@/shared/dto/response.dto';
import { RequirePermissions } from '@/core/auth/decorators/permissions.decorator';
import { Permission } from '@/shared/enums/permission.enum';
import { Throttle } from '@nestjs/throttler';

@ApiTags('Admin - Users')
@ApiSecurity('JWT-auth')
@Controller({ path: 'admin/users', version: '1' })
@RequirePermissions(Permission.USER_READ) // 控制器级别权限
export class AdminUsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @RequirePermissions(Permission.USER_CREATE)
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 1分钟内最多5次创建
  @ApiOperation({ summary: '创建新用户' })
  @ApiResponse({
    status: 201,
    description: '用户创建成功',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: '用户名或邮箱已存在',
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    const user = await this.usersService.create(createUserDto);
    return user.toSafeObject() as UserResponseDto;
  }

  @Get()
  @ApiOperation({ summary: '获取用户列表（分页）' })
  @ApiResponse({
    status: 200,
    description: '返回分页用户列表',
    type: PaginatedResponseDto<UserResponseDto>,
  })
  async findAll(@Query() query: UserQueryDto): Promise<PaginatedResponseDto<UserResponseDto>> {
    return this.usersService.findAll(query) as Promise<PaginatedResponseDto<UserResponseDto>>;
  }

  @Get('stats')
  @ApiOperation({ summary: '获取用户统计信息' })
  @ApiResponse({
    status: 200,
    description: '返回用户统计数据',
    type: UserStatsDto,
  })
  async getStats(): Promise<UserStatsDto> {
    return this.usersService.getStats();
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取用户详情' })
  @ApiParam({ name: 'id', description: '用户UUID' })
  @ApiResponse({
    status: 200,
    description: '返回用户详情',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<UserResponseDto> {
    const user = await this.usersService.findOne(id);
    return user.toSafeObject() as UserResponseDto;
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新用户信息' })
  @ApiParam({ name: 'id', description: '用户UUID' })
  @ApiResponse({
    status: 200,
    description: '用户更新成功',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  @ApiResponse({
    status: 409,
    description: '邮箱已存在',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.update(id, updateUserDto);
    return user.toSafeObject() as UserResponseDto;
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除用户' })
  @ApiParam({ name: 'id', description: '用户UUID' })
  @ApiResponse({
    status: 204,
    description: '用户删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.usersService.remove(id);
  }

  @Patch(':id/password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重置用户密码' })
  @ApiParam({ name: 'id', description: '用户UUID' })
  @ApiResponse({
    status: 200,
    description: '密码重置成功',
    type: MessageResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async updatePassword(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { password: string },
  ): Promise<MessageResponseDto> {
    await this.usersService.updatePassword(id, body.password);
    return new MessageResponseDto('密码重置成功');
  }

  @Post('batch/status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '批量更新用户状态' })
  @ApiResponse({
    status: 200,
    description: '用户状态更新成功',
    type: MessageResponseDto,
  })
  async batchUpdateStatus(
    @Body() body: { ids: string[]; status: string },
  ): Promise<MessageResponseDto> {
    await this.usersService.batchUpdateStatus(body.ids, body.status as any);
    return new MessageResponseDto(`成功更新 ${body.ids.length} 个用户的状态`);
  }

  @Delete('batch')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '批量删除用户' })
  @ApiResponse({
    status: 200,
    description: '用户删除成功',
    type: MessageResponseDto,
  })
  async batchRemove(@Body() body: { ids: string[] }): Promise<MessageResponseDto> {
    await this.usersService.batchRemove(body.ids);
    return new MessageResponseDto(`成功删除 ${body.ids.length} 个用户`);
  }
}