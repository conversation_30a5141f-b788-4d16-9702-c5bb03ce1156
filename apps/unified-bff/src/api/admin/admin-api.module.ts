import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Controllers
import { AdminUsersController } from './users/admin-users.controller';
import { AdminDocumentsController } from './documents/admin-documents.controller';
import { AdminPerformanceController } from './performance/admin-performance.controller';
import { AdminCacheController } from './cache/admin-cache.controller';
import { AdminMonitoringController } from './monitoring/admin-monitoring.controller';

// Services
import { UsersService } from '@/modules/users/services/users.service';
import { DocumentsService } from '@/modules/documents/services/documents.service';
import { CacheService } from '@/core/cache/cache.service';

// Entities
import { User } from '@/shared/entities/user.entity';
import { Document } from '@/shared/entities/document.entity';

// Modules
import { AnalyticsModule } from '@/modules/analytics/analytics.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Document]),
    AnalyticsModule,
  ],
  controllers: [
    AdminUsersController,
    AdminDocumentsController,
    AdminPerformanceController,
    AdminCacheController,
    AdminMonitoringController,
  ],
  providers: [
    UsersService,
    DocumentsService,
    CacheService,
  ],
})
export class AdminApiModule {}