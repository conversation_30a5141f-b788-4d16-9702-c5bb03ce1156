import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Controllers
import { AdminUsersController } from './users/admin-users.controller';

// Services
import { UsersService } from '@/modules/users/services/users.service';
import { CacheService } from '@/core/cache/cache.service';

// Entities
import { User } from '@/shared/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
  ],
  controllers: [
    AdminUsersController,
    // TODO: Add other admin controllers
    // AdminDocumentsController,
    // AdminAnalyticsController,
  ],
  providers: [
    UsersService,
    CacheService,
    // TODO: Add other services
    // DocumentsService,
    // AnalyticsService,
  ],
})
export class AdminApiModule {}