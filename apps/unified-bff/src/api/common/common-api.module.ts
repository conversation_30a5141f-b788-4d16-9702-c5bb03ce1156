import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Controllers
import { AuthController } from './auth/auth.controller';
import { MetricsController } from './metrics/metrics.controller';

// Core modules
import { AuthModule } from '@/core/auth/auth.module';
import { CacheModule } from '@/core/cache/cache.module';

// Services
import { CacheService } from '@/core/cache/cache.service';

// Entities
import { User } from '@/shared/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    AuthModule,
    CacheModule,
  ],
  controllers: [
    AuthController,
    MetricsController,
  ],
  providers: [
    CacheService,
  ],
})
export class CommonApiModule {}