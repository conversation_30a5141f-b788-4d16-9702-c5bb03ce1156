import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '@/shared/enums/user-role.enum';

export class AuthUserDto {
  @ApiProperty({
    description: '用户ID',
    example: 'uuid-123',
  })
  id: string;

  @ApiProperty({
    description: '用户名',
    example: 'admin',
  })
  username: string;

  @ApiProperty({
    description: '邮箱',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: '用户角色',
    enum: UserRole,
    example: UserRole.ADMIN,
  })
  role: UserRole;

  @ApiProperty({
    description: '名字',
    example: 'John',
    required: false,
  })
  firstName?: string;

  @ApiProperty({
    description: '姓氏',
    example: 'Doe',
    required: false,
  })
  lastName?: string;

  @ApiProperty({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  avatarUrl?: string;
}

export class AuthResponseDto {
  @ApiProperty({
    description: '用户信息',
    type: AuthUserDto,
  })
  user: AuthUserDto;

  @ApiProperty({
    description: '访问令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: '刷新令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;
}