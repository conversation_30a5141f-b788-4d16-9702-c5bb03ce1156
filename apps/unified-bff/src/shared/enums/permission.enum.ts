// 权限枚举 - 细粒度权限控制
export enum Permission {
  // 用户管理权限
  USER_READ = 'user:read',
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_MANAGE_ROLES = 'user:manage_roles',
  USER_EXPORT = 'user:export',

  // 文档管理权限
  DOCUMENT_READ = 'document:read',
  DOCUMENT_CREATE = 'document:create',
  DOCUMENT_UPDATE = 'document:update',
  DOCUMENT_DELETE = 'document:delete',
  DOCUMENT_PUBLISH = 'document:publish',
  DOCUMENT_APPROVE = 'document:approve',
  DOCUMENT_EXPORT = 'document:export',

  // 分析统计权限
  ANALYTICS_READ = 'analytics:read',
  ANALYTICS_EXPORT = 'analytics:export',
  ANALYTICS_ADMIN = 'analytics:admin',

  // 系统管理权限
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_MONITOR = 'system:monitor',
  SYSTEM_BACKUP = 'system:backup',
  SYSTEM_LOGS = 'system:logs',

  // API访问权限
  API_ADMIN = 'api:admin',
  API_MOBILE = 'api:mobile',
  API_PUBLIC = 'api:public',
}

// 权限组 - 按模块分组
export const PermissionGroups = {
  USER_MANAGEMENT: [
    Permission.USER_READ,
    Permission.USER_CREATE,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    Permission.USER_MANAGE_ROLES,
    Permission.USER_EXPORT,
  ],
  DOCUMENT_MANAGEMENT: [
    Permission.DOCUMENT_READ,
    Permission.DOCUMENT_CREATE,
    Permission.DOCUMENT_UPDATE,
    Permission.DOCUMENT_DELETE,
    Permission.DOCUMENT_PUBLISH,
    Permission.DOCUMENT_APPROVE,
    Permission.DOCUMENT_EXPORT,
  ],
  ANALYTICS: [
    Permission.ANALYTICS_READ,
    Permission.ANALYTICS_EXPORT,
    Permission.ANALYTICS_ADMIN,
  ],
  SYSTEM: [
    Permission.SYSTEM_CONFIG,
    Permission.SYSTEM_MONITOR,
    Permission.SYSTEM_BACKUP,
    Permission.SYSTEM_LOGS,
  ],
} as const;

// 角色权限映射
export const RolePermissions: Record<string, Permission[]> = {
  SUPER_ADMIN: [
    ...PermissionGroups.USER_MANAGEMENT,
    ...PermissionGroups.DOCUMENT_MANAGEMENT,
    ...PermissionGroups.ANALYTICS,
    ...PermissionGroups.SYSTEM,
    Permission.API_ADMIN,
    Permission.API_MOBILE,
    Permission.API_PUBLIC,
  ],
  ADMIN: [
    ...PermissionGroups.USER_MANAGEMENT,
    ...PermissionGroups.DOCUMENT_MANAGEMENT,
    ...PermissionGroups.ANALYTICS,
    Permission.API_ADMIN,
    Permission.API_PUBLIC,
  ],
  MANAGER: [
    Permission.USER_READ,
    Permission.USER_UPDATE,
    ...PermissionGroups.DOCUMENT_MANAGEMENT,
    Permission.ANALYTICS_READ,
    Permission.ANALYTICS_EXPORT,
    Permission.API_ADMIN,
    Permission.API_PUBLIC,
  ],
  USER: [
    Permission.USER_READ,
    Permission.DOCUMENT_READ,
    Permission.API_MOBILE,
    Permission.API_PUBLIC,
  ],
  GUEST: [
    Permission.DOCUMENT_READ,
    Permission.API_PUBLIC,
  ],
};