import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON>ler,
  BadRequestException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { FastifyRequest } from 'fastify';
import { MultipartFile } from '@fastify/multipart';

export interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
  filename?: string;
}

@Injectable()
export class FastifyFileInterceptor implements NestInterceptor {
  constructor(
    private readonly fieldName: string,
    private readonly options: {
      maxFileSize?: number;
      allowedMimeTypes?: string[];
    } = {}
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest<FastifyRequest & { file?: () => Promise<MultipartFile | undefined> }>();
    
    try {
      // 处理multipart/form-data
      const data = await request.file?.();
      
      if (!data) {
        return next.handle();
      }

      // 检查文件大小
      if (this.options.maxFileSize && data.file.bytesRead > this.options.maxFileSize) {
        throw new BadRequestException('File too large');
      }

      // 检查MIME类型
      if (this.options.allowedMimeTypes && !this.options.allowedMimeTypes.includes(data.mimetype)) {
        throw new BadRequestException('Invalid file type');
      }

      // 读取文件内容
      const chunks: Buffer[] = [];
      for await (const chunk of data.file) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);

      // 创建类似multer的文件对象
      const file: MulterFile = {
        fieldname: data.fieldname || this.fieldName,
        originalname: data.filename || 'unknown',
        encoding: data.encoding || '7bit',
        mimetype: data.mimetype,
        size: buffer.length,
        buffer: buffer,
        filename: data.filename,
      };

      // 将文件添加到请求对象
      (request as any).file = file;
      
      return next.handle();
    } catch (error) {
      throw new BadRequestException('File upload failed: ' + error.message);
    }
  }
}

// 工厂函数，类似于Express的FileInterceptor
export function FileInterceptor(
  fieldName: string,
  options: {
    maxFileSize?: number;
    allowedMimeTypes?: string[];
  } = {}
) {
  @Injectable()
  class FileInterceptorMixin implements NestInterceptor {
    public readonly interceptor = new FastifyFileInterceptor(fieldName, options);

    async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
      return this.interceptor.intercept(context, next);
    }
  }
  
  return FileInterceptorMixin;
}