import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum DocumentType {
  CONTRACT = 'CONTRACT',
  REPORT = 'REPORT',
  MANUAL = 'MANUAL',
  PRESENTATION = 'PRESENTATION',
  SPREADSHEET = 'SPREADSHEET',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  OTHER = 'OTHER',
}

export enum DocumentStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED',
}

export enum AccessLevel {
  PUBLIC = 'PUBLIC',
  INTERNAL = 'INTERNAL',
  CONFIDENTIAL = 'CONFIDENTIAL',
  PRIVATE = 'PRIVATE',
}

@Entity('documents')
@Index(['title'])
@Index(['type'])
@Index(['status'])
@Index(['accessLevel'])
@Index(['createdBy'])
@Index(['createdAt'])
@Index('IDX_DOCUMENT_TAGS', (doc) => [doc.tags])
@Index('IDX_DOCUMENT_METADATA', (doc) => [doc.metadata])
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'file_name', length: 255 })
  fileName: string;

  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_size', type: 'bigint', default: 0 })
  fileSize: number;

  @Column({ name: 'mime_type', nullable: true, length: 100 })
  mimeType?: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.OTHER,
  })
  type: DocumentType;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.DRAFT,
  })
  status: DocumentStatus;

  @Column({
    name: 'access_level',
    type: 'enum',
    enum: AccessLevel,
    default: AccessLevel.PRIVATE,
  })
  accessLevel: AccessLevel;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: string;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @Column('text', { array: true, default: '{}' })
  tags: string[];

  @Column('jsonb', { default: '{}' })
  metadata: Record<string, any>;

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'thumbnail_path', nullable: true })
  thumbnailPath?: string;

  @Column({ name: 'is_searchable', default: true })
  isSearchable: boolean;

  @Column({ type: 'text', nullable: true, comment: '提取的文本内容用于搜索' })
  content?: string;

  @Column({ default: 1 })
  version: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 虚拟属性
  get fileExtension(): string {
    return this.fileName.split('.').pop()?.toLowerCase() || '';
  }

  get formattedSize(): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = Number(this.fileSize);
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(size < 10 ? 1 : 0)} ${units[unitIndex]}`;
  }

  get isPublic(): boolean {
    return this.accessLevel === AccessLevel.PUBLIC;
  }

  get isPublished(): boolean {
    return this.status === DocumentStatus.PUBLISHED;
  }

  get isDraft(): boolean {
    return this.status === DocumentStatus.DRAFT;
  }

  get isDeleted(): boolean {
    return this.status === DocumentStatus.DELETED;
  }

  // 业务方法
  incrementViewCount(): void {
    this.viewCount += 1;
  }

  incrementDownloadCount(): void {
    this.downloadCount += 1;
  }

  addTag(tag: string): void {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    this.tags = this.tags.filter(t => t !== tag);
  }

  updateMetadata(key: string, value: any): void {
    this.metadata = { ...this.metadata, [key]: value };
  }

  // 权限检查
  canAccess(userRole: string, isOwner: boolean = false): boolean {
    switch (this.accessLevel) {
      case AccessLevel.PUBLIC:
        return true;
      case AccessLevel.INTERNAL:
        return ['ADMIN', 'MANAGER', 'USER'].includes(userRole);
      case AccessLevel.CONFIDENTIAL:
        return ['ADMIN', 'MANAGER'].includes(userRole) || isOwner;
      case AccessLevel.PRIVATE:
        return userRole === 'ADMIN' || isOwner;
      default:
        return false;
    }
  }

  canEdit(userRole: string, isOwner: boolean = false): boolean {
    if (this.isDeleted) return false;
    return userRole === 'ADMIN' || isOwner;
  }

  canDelete(userRole: string, isOwner: boolean = false): boolean {
    return userRole === 'ADMIN' || isOwner;
  }

  // 返回安全对象
  toSafeObject(): Partial<Document> {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      fileName: this.fileName,
      fileSize: this.fileSize,
      mimeType: this.mimeType,
      type: this.type,
      status: this.status,
      accessLevel: this.accessLevel,
      tags: this.tags,
      metadata: this.metadata,
      viewCount: this.viewCount,
      downloadCount: this.downloadCount,
      thumbnailPath: this.thumbnailPath,
      version: this.version,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      fileExtension: this.fileExtension,
      formattedSize: this.formattedSize,
      isPublic: this.isPublic,
      isPublished: this.isPublished,
      isDraft: this.isDraft,
    };
  }
}