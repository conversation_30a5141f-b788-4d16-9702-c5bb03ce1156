import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import * as bcrypt from 'bcryptjs';
import { UserRole } from '@/shared/enums/user-role.enum';
import { UserStatus } from '@/shared/enums/user-status.enum';
import { Permission, RolePermissions } from '@/shared/enums/permission.enum';

@Entity('users')
@Index(['email'])
@Index(['username'])
@Index(['role'])
@Index(['status'])
@Index(['createdAt'])
@Index(['lastLoginAt', 'status']) // 复合索引用于活跃用户查询
@Index(['role', 'status']) // 复合索引用于按角色和状态筛选
@Index(['emailVerified'], { where: 'email_verified = false' }) // 部分索引用于未验证邮箱
@Index(['createdAt', 'status']) // 时间范围和状态查询
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 50 })
  username: string;

  @Column({ unique: true, length: 100 })
  email: string;

  @Column({ name: 'password_hash' })
  @Exclude()
  passwordHash: string;

  @Column({ name: 'first_name', nullable: true, length: 50 })
  firstName?: string;

  @Column({ name: 'last_name', nullable: true, length: 50 })
  lastName?: string;

  @Column({ name: 'avatar_url', nullable: true })
  avatarUrl?: string;

  @Column({ nullable: true, length: 20 })
  phone?: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({ name: 'email_verified', default: false })
  emailVerified: boolean;

  @Column({ name: 'mobile_verified', default: false })
  mobileVerified: boolean;

  @Column({ name: 'last_login_at', nullable: true })
  lastLoginAt?: Date;

  @Column({ name: 'wechat_openid', nullable: true })
  wechatOpenid?: string;

  @Column({ 
    type: 'jsonb', 
    default: '{}',
    comment: '用户偏好设置'
  })
  preferences: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 虚拟属性
  get fullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return this.firstName || this.lastName || this.username;
  }

  get isActive(): boolean {
    return this.status === UserStatus.ACTIVE;
  }

  get isAdmin(): boolean {
    return [UserRole.SUPER_ADMIN, UserRole.ADMIN].includes(this.role);
  }

  // 密码处理
  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.passwordHash && !this.passwordHash.startsWith('$2a$')) {
      this.passwordHash = await bcrypt.hash(this.passwordHash, 12);
    }
  }

  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.passwordHash);
  }

  async setPassword(password: string): Promise<void> {
    this.passwordHash = await bcrypt.hash(password, 12);
  }

  // 返回安全对象（排除敏感信息）
  toSafeObject(): Partial<User> {
    const { passwordHash, ...safeUser } = this;
    return {
      ...safeUser,
      fullName: this.fullName,
      isActive: this.isActive,
      isAdmin: this.isAdmin,
    };
  }

  // 返回JWT载荷
  toJwtPayload(): any {
    return {
      sub: this.id,
      username: this.username,
      email: this.email,
      role: this.role,
      status: this.status,
    };
  }

  // 更新最后登录时间
  updateLastLogin(): void {
    this.lastLoginAt = new Date();
  }

  // 权限检查
  hasRole(role: UserRole): boolean {
    const roleHierarchy = {
      [UserRole.GUEST]: 0,
      [UserRole.USER]: 1,
      [UserRole.MANAGER]: 2,
      [UserRole.ADMIN]: 3,
      [UserRole.SUPER_ADMIN]: 4,
    };

    return roleHierarchy[this.role] >= roleHierarchy[role];
  }

  hasPermission(permission: Permission): boolean {
    // 超级管理员拥有所有权限
    if (this.role === UserRole.SUPER_ADMIN) {
      return true;
    }

    // 使用统一的权限映射
    const userPermissions = RolePermissions[this.role] || [];
    return userPermissions.includes(permission);
  }

  /**
   * 获取用户的所有权限
   */
  getPermissions(): Permission[] {
    return RolePermissions[this.role] || [];
  }

  /**
   * 检查用户是否拥有任一权限
   */
  hasAnyPermission(permissions: Permission[]): boolean {
    const userPermissions = this.getPermissions();
    return permissions.some(permission => userPermissions.includes(permission));
  }

  /**
   * 检查用户是否拥有所有权限
   */
  hasAllPermissions(permissions: Permission[]): boolean {
    const userPermissions = this.getPermissions();
    return permissions.every(permission => userPermissions.includes(permission));
  }
}