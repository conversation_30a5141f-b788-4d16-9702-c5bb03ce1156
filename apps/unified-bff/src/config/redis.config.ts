import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BullModuleOptions, BullOptionsFactory } from '@nestjs/bull';

@Injectable()
export class RedisConfig implements BullOptionsFactory {
  constructor(private configService: ConfigService) {}

  createBullOptions(): BullModuleOptions {
    return {
      redis: {
        host: this.configService.get('REDIS_HOST') || 'localhost',
        port: parseInt(this.configService.get('REDIS_PORT')) || 6379,
        password: this.configService.get('REDIS_PASSWORD'),
        db: parseInt(this.configService.get('REDIS_DB')) || 0,
        
        // 连接配置
        connectTimeout: 10000,
        lazyConnect: true,
        maxRetriesPerRequest: 3,
        retryDelayOnFailover: 100,
        
        // 连接池配置
        family: 4, // IPv4
        keepAlive: true,
      },
      
      // 默认任务配置
      defaultJobOptions: {
        removeOnComplete: 10, // 保留最近10个完成的任务
        removeOnFail: 5,      // 保留最近5个失败的任务
        attempts: 3,          // 重试3次
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    };
  }
}