import { registerAs } from '@nestjs/config';

export const securityConfig = registerAs('security', () => ({
  // 限流配置
  throttle: {
    short: {
      ttl: parseInt(process.env.THROTTLE_SHORT_TTL, 10) || 1000, // 1秒
      limit: parseInt(process.env.THROTTLE_SHORT_LIMIT, 10) || 10,
    },
    medium: {
      ttl: parseInt(process.env.THROTTLE_MEDIUM_TTL, 10) || 60000, // 1分钟
      limit: parseInt(process.env.THROTTLE_MEDIUM_LIMIT, 10) || 100,
    },
    long: {
      ttl: parseInt(process.env.THROTTLE_LONG_TTL, 10) || 900000, // 15分钟
      limit: parseInt(process.env.THROTTLE_LONG_LIMIT, 10) || 1000,
    },
    auth: {
      ttl: parseInt(process.env.THROTTLE_AUTH_TTL, 10) || 900000, // 15分钟
      limit: parseInt(process.env.THROTTLE_AUTH_LIMIT, 10) || 5, // 登录尝试
    },
  },

  // IP白名单配置
  ipWhitelist: process.env.IP_WHITELIST?.split(',').map(ip => ip.trim()) || [],

  // 请求签名配置
  requestSignature: {
    enabled: process.env.REQUEST_SIGNATURE_ENABLED === 'true',
    secret: process.env.REQUEST_SIGNATURE_SECRET || 'masteryos-unified-bff-secret',
    maxAge: parseInt(process.env.REQUEST_SIGNATURE_MAX_AGE, 10) || 300000, // 5分钟
  },

  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGINS?.split(',') || [
      'http://localhost:3200', // Admin Web
      'http://localhost:3100', // Mobile Web 
      'http://localhost:8080', // Dev server
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'X-Requested-With',
      'X-Request-ID',
      'X-Timestamp',
      'X-Signature',
    ],
  },

  // CSP配置
  csp: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // 开发环境可能需要unsafe-eval
    styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
    imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
    connectSrc: ["'self'", 'https://api.github.com'], // 添加需要连接的外部API
    fontSrc: ["'self'", 'https://fonts.gstatic.com'],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"],
    childSrc: ["'none'"],
    workerSrc: ["'self'"],
    manifestSrc: ["'self'"],
  },

  // Helmet配置
  helmet: {
    contentSecurityPolicy: {
      useDefaults: false,
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false, // 开发环境关闭
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
    frameguard: { action: 'deny' },
    noSniff: true,
    xssFilter: true,
    referrerPolicy: { policy: 'same-origin' },
  },

  // API密钥配置
  apiKeys: {
    enabled: process.env.API_KEYS_ENABLED === 'true',
    adminKey: process.env.ADMIN_API_KEY,
    clientKeys: process.env.CLIENT_API_KEYS?.split(',') || [],
  },

  // 加密配置
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16,
    secretKey: process.env.ENCRYPTION_SECRET_KEY || 'masteryos-encryption-key-32-chars',
  },
}));