import { registerAs } from '@nestjs/config';

export const authConfig = registerAs('auth', () => ({
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    
    // JWT算法配置
    algorithm: 'HS256',
    issuer: 'masteryos',
    audience: 'masteryos-users',
  },
  
  // 密码策略
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    
    // bcrypt配置
    saltRounds: 12,
  },
  
  // 会话配置
  session: {
    maxConcurrentSessions: 5, // 最大并发会话数
    sessionTimeout: 24 * 60 * 60 * 1000, // 24小时
  },
  
  // 限流配置
  rateLimit: {
    login: {
      windowMs: 15 * 60 * 1000, // 15分钟
      maxAttempts: 5,           // 最多5次尝试
      blockDuration: 15 * 60 * 1000, // 锁定15分钟
    },
    
    api: {
      windowMs: 15 * 60 * 1000, // 15分钟
      maxRequests: 100,         // 最多100次请求
    },
  },
  
  // 第三方登录配置
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      redirectUri: process.env.GOOGLE_REDIRECT_URI,
    },
    
    wechat: {
      appId: process.env.WECHAT_APP_ID,
      appSecret: process.env.WECHAT_APP_SECRET,
      redirectUri: process.env.WECHAT_REDIRECT_URI,
    },
  },
  
  // 验证码配置
  verification: {
    email: {
      expiresIn: 10 * 60 * 1000, // 10分钟
      maxAttempts: 5,
    },
    
    sms: {
      expiresIn: 5 * 60 * 1000,  // 5分钟
      maxAttempts: 3,
      cooldown: 60 * 1000,       // 1分钟冷却
    },
  },
}));