import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';

@Injectable()
export class DatabaseConfig implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const isProduction = this.configService.get('NODE_ENV') === 'production';
    
    return {
      type: 'postgres',
      url: this.configService.get('DATABASE_URL') || 
           'postgresql://masteryos:masteryos@localhost:8182/masteryos',
      
      // 实体配置
      entities: [__dirname + '/../**/*.entity{.ts,.js}'],
      migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
      
      // 开发环境配置
      synchronize: !isProduction, // 生产环境禁用同步
      logging: !isProduction ? ['query', 'error'] : ['error'],
      
      // 连接池配置
      extra: {
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000,
      },
      
      // SSL配置（生产环境）
      ssl: isProduction ? { rejectUnauthorized: false } : false,
      
      // 自动加载实体
      autoLoadEntities: true,
      
      // 数据库连接重试
      retryAttempts: 5,
      retryDelay: 3000,
    };
  }
}

// 用于CLI工具的数据源配置
export const dataSource = new DataSource({
  type: 'postgres',
  url: process.env.DATABASE_URL || 'postgresql://masteryos:masteryos@localhost:8182/masteryos',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.NODE_ENV !== 'production',
} as DataSourceOptions);