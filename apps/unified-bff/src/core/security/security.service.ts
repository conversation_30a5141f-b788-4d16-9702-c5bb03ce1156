import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

@Injectable()
export class SecurityService {
  constructor(private configService: ConfigService) {}

  /**
   * 检查IP是否在白名单中
   */
  isIpWhitelisted(ip: string): boolean {
    const whitelist = this.configService.get<string[]>('security.ipWhitelist') || [];
    
    // 如果没有配置白名单，则允许所有IP
    if (whitelist.length === 0) {
      return true;
    }

    return whitelist.some(whitelistedIp => {
      // 支持CIDR格式和通配符
      if (whitelistedIp.includes('/')) {
        return this.isIpInCidr(ip, whitelistedIp);
      }
      
      if (whitelistedIp.includes('*')) {
        const regex = new RegExp(whitelistedIp.replace(/\*/g, '.*'));
        return regex.test(ip);
      }
      
      return ip === whitelistedIp;
    });
  }

  /**
   * 检查IP是否在CIDR范围内
   */
  private isIpInCidr(ip: string, cidr: string): boolean {
    const [network, prefixLength] = cidr.split('/');
    const mask = ~(2 ** (32 - parseInt(prefixLength)) - 1);
    
    const ipInt = this.ipToInt(ip);
    const networkInt = this.ipToInt(network);
    
    return (ipInt & mask) === (networkInt & mask);
  }

  /**
   * 将IP地址转换为整数
   */
  private ipToInt(ip: string): number {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
  }

  /**
   * 生成请求签名用于防重放攻击
   */
  generateRequestSignature(method: string, url: string, body: string, timestamp: number): string {
    const secret = this.configService.get('security.requestSignature.secret', 'default-secret');
    const data = `${method}${url}${body}${timestamp}`;
    return crypto.createHmac('sha256', secret).update(data).digest('hex');
  }

  /**
   * 验证请求签名
   */
  verifyRequestSignature(
    signature: string,
    method: string,
    url: string,
    body: string,
    timestamp: number,
  ): boolean {
    const expectedSignature = this.generateRequestSignature(method, url, body, timestamp);
    
    // 防止时间窗口攻击 - 只允许5分钟内的请求
    const maxAge = this.configService.get('security.requestSignature.maxAge', 300000); // 5分钟
    if (Date.now() - timestamp > maxAge) {
      return false;
    }

    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  /**
   * 生成API密钥
   */
  generateApiKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 验证API密钥格式
   */
  isValidApiKey(apiKey: string): boolean {
    return /^[a-f0-9]{64}$/.test(apiKey);
  }

  /**
   * 检查是否为可疑的用户代理
   */
  isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
    ];

    // 允许的用户代理白名单
    const allowedBots = [
      /googlebot/i,
      /bingbot/i,
      /facebookexternalhit/i,
      /twitterbot/i,
    ];

    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
    const isAllowed = allowedBots.some(pattern => pattern.test(userAgent));

    return isSuspicious && !isAllowed;
  }

  /**
   * 生成CSP(Content Security Policy)头
   */
  generateCspHeader(): string {
    const cspConfig = this.configService.get('security.csp', {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    });

    return Object.entries(cspConfig)
      .map(([directive, sources]) => `${directive} ${(sources as string[]).join(' ')}`)
      .join('; ');
  }
}