import { Injectable, Logger, Inject } from '@nestjs/common';
import {
  CacheStrategy,
  Cache<PERSON>ey,
  CacheOptions,
  CacheStats,
} from './strategies/cache-strategy.interface';

export interface CachePattern {
  pattern: string;
  strategy: string;
  options?: CacheOptions;
}

export interface CacheConfig {
  defaultStrategy: string;
  patterns: CachePattern[];
  enableMetrics: boolean;
  enableCircuitBreaker: boolean;
  circuitBreakerConfig?: {
    failureThreshold: number;
    timeout: number;
    monitoringPeriod: number;
  };
}

interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
}

@Injectable()
export class AdvancedCacheService {
  private readonly logger = new Logger(AdvancedCacheService.name);
  private readonly circuitBreakers = new Map<string, CircuitBreakerState>();
  private metrics = {
    requests: 0,
    hits: 0,
    misses: 0,
    errors: 0,
    avgResponseTime: 0,
    responseTimeSum: 0,
  };

  constructor(
    @Inject('CACHE_STRATEGIES') private readonly strategies: Map<string, CacheStrategy>,
    @Inject('CACHE_CONFIG') private readonly config: CacheConfig,
  ) {}

  /**
   * 高级缓存获取，支持模式匹配和回退策略
   */
  async get<T>(key: string | CacheKey, options?: {
    fallback?: () => Promise<T>;
    staleWhileRevalidate?: boolean;
    refreshAhead?: boolean;
  }): Promise<T | null> {
    const startTime = Date.now();
    const strategy = this.selectStrategy(key);
    
    try {
      this.metrics.requests++;

      // 检查熔断器状态
      if (this.config.enableCircuitBreaker && this.isCircuitOpen(strategy.name)) {
        this.logger.warn(`Circuit breaker is OPEN for strategy: ${strategy.name}`);
        return options?.fallback ? await options.fallback() : null;
      }

      let value = await strategy.get<T>(key);
      
      if (value !== null) {
        this.metrics.hits++;
        this.recordSuccess(strategy.name);
        
        // 检查是否需要提前刷新
        if (options?.refreshAhead) {
          this.scheduleRefresh(key, strategy, options.fallback);
        }
      } else {
        this.metrics.misses++;
        
        // 使用回退函数
        if (options?.fallback) {
          value = await options.fallback();
          if (value !== null) {
            // 异步设置缓存
            this.set(key, value).catch(error => {
              this.logger.warn('Failed to set cache after fallback', error);
            });
          }
        }
      }

      return value;
    } catch (error) {
      this.metrics.errors++;
      this.recordFailure(strategy.name);
      this.logger.error(`Cache get error for key: ${this.normalizeKey(key)}`, error);
      
      // 使用回退函数
      if (options?.fallback) {
        try {
          return await options.fallback();
        } catch (fallbackError) {
          this.logger.error('Fallback function also failed', fallbackError);
        }
      }
      
      return null;
    } finally {
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
    }
  }

  /**
   * 高级缓存设置
   */
  async set<T>(key: string | CacheKey, value: T, options?: CacheOptions): Promise<void> {
    const strategy = this.selectStrategy(key);
    
    try {
      // 检查熔断器状态
      if (this.config.enableCircuitBreaker && this.isCircuitOpen(strategy.name)) {
        this.logger.warn(`Circuit breaker is OPEN for strategy: ${strategy.name}, skipping set`);
        return;
      }

      await strategy.set(key, value, options);
      this.recordSuccess(strategy.name);
    } catch (error) {
      this.recordFailure(strategy.name);
      this.logger.error(`Cache set error for key: ${this.normalizeKey(key)}`, error);
      throw error;
    }
  }

  /**
   * 批量获取，支持不同的缓存策略
   */
  async mget<T>(keys: Array<{ key: string | CacheKey; fallback?: () => Promise<T> }>): Promise<Array<T | null>> {
    // 按策略分组
    const strategyGroups = new Map<CacheStrategy, Array<{ index: number; key: string | CacheKey; fallback?: () => Promise<T> }>>();
    
    keys.forEach((item, index) => {
      const strategy = this.selectStrategy(item.key);
      if (!strategyGroups.has(strategy)) {
        strategyGroups.set(strategy, []);
      }
      strategyGroups.get(strategy)!.push({ ...item, index });
    });

    const results: Array<T | null> = new Array(keys.length).fill(null);
    const fallbackPromises: Array<{ index: number; promise: Promise<T> }> = [];

    // 并行处理各个策略组
    const groupPromises = Array.from(strategyGroups.entries()).map(async ([strategy, items]) => {
      try {
        const groupKeys = items.map(item => item.key);
        const groupResults = await (strategy as any).mget?.(groupKeys) || 
          await Promise.all(groupKeys.map(key => strategy.get<T>(key)));

        items.forEach((item, groupIndex) => {
          const value = groupResults[groupIndex];
          if (value !== null) {
            results[item.index] = value;
          } else if (item.fallback) {
            // 收集需要回退的项目
            fallbackPromises.push({
              index: item.index,
              promise: item.fallback(),
            });
          }
        });
      } catch (error) {
        this.logger.error(`Batch get error for strategy: ${strategy.name}`, error);
      }
    });

    await Promise.allSettled(groupPromises);

    // 处理回退
    if (fallbackPromises.length > 0) {
      const fallbackResults = await Promise.allSettled(
        fallbackPromises.map(item => item.promise)
      );

      fallbackPromises.forEach((item, index) => {
        const result = fallbackResults[index];
        if (result.status === 'fulfilled' && result.value !== null) {
          results[item.index] = result.value;
          
          // 异步设置缓存
          const key = keys[item.index].key;
          this.set(key, result.value).catch(error => {
            this.logger.warn('Failed to set cache after fallback', error);
          });
        }
      });
    }

    return results;
  }

  /**
   * 智能缓存：结合多种优化策略
   */
  async smartCache<T>(
    key: string | CacheKey,
    loader: () => Promise<T>,
    options?: {
      ttl?: number;
      tags?: string[];
      refreshAhead?: number; // 在过期前多少毫秒开始刷新
      lockTimeout?: number; // 防止缓存击穿的锁超时时间
      maxStale?: number; // 最大允许返回过期数据的时间
    }
  ): Promise<T> {
    const lockKey = `lock:${this.normalizeKey(key)}`;
    const strategy = this.selectStrategy(key);

    try {
      // 首先尝试获取缓存
      let cached = await strategy.get<T>(key);
      const ttl = await strategy.ttl(key);

      // 检查是否需要提前刷新
      if (cached !== null && options?.refreshAhead && ttl > 0 && ttl < options.refreshAhead) {
        // 异步刷新
        this.refreshInBackground(key, loader, options);
        return cached;
      }

      if (cached !== null) {
        return cached;
      }

      // 缓存未命中，尝试获取锁防止缓存击穿
      const lockAcquired = await this.acquireLock(lockKey, options?.lockTimeout || 5000);
      
      if (lockAcquired) {
        try {
          // 双重检查
          cached = await strategy.get<T>(key);
          if (cached !== null) {
            return cached;
          }

          // 加载数据
          const data = await loader();
          
          // 设置缓存
          await strategy.set(key, data, {
            ttl: options?.ttl,
            tags: options?.tags,
          });

          return data;
        } finally {
          await this.releaseLock(lockKey);
        }
      } else {
        // 获取锁失败，等待一段时间后重试或返回过期数据
        await new Promise(resolve => setTimeout(resolve, 100));
        
        cached = await strategy.get<T>(key);
        if (cached !== null) {
          return cached;
        }

        // 如果还是没有数据，直接加载（可能导致缓存击穿，但避免死锁）
        return await loader();
      }
    } catch (error) {
      this.logger.error(`Smart cache error for key: ${this.normalizeKey(key)}`, error);
      
      // 尝试返回过期数据
      if (options?.maxStale) {
        // 这里需要扩展缓存策略来支持获取过期数据
        // 暂时直接执行loader
      }
      
      throw error;
    }
  }

  /**
   * 缓存预热
   */
  async warmup(warmupRules: Array<{
    keyPattern: string;
    loader: (key: string) => Promise<any>;
    options?: CacheOptions;
    batchSize?: number;
  }>): Promise<void> {
    for (const rule of warmupRules) {
      try {
        // 这里可以根据keyPattern生成具体的key列表
        // 简化实现，假设keyPattern就是具体的key
        const data = await rule.loader(rule.keyPattern);
        await this.set(rule.keyPattern, data, rule.options);
      } catch (error) {
        this.logger.warn(`Failed to warmup cache for pattern: ${rule.keyPattern}`, error);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<{
    global: typeof this.metrics;
    strategies: Array<{ name: string; stats: CacheStats }>;
  }> {
    const strategyStats = [];
    
    for (const [name, strategy] of this.strategies) {
      const stats = await strategy.getStats();
      strategyStats.push({ name, stats });
    }

    return {
      global: { ...this.metrics },
      strategies: strategyStats,
    };
  }

  /**
   * 清理过期缓存
   */
  async cleanup(): Promise<void> {
    const promises = Array.from(this.strategies.values()).map(strategy => {
      if ((strategy as any).cleanup) {
        return (strategy as any).cleanup();
      }
      return Promise.resolve();
    });

    await Promise.allSettled(promises);
  }

  /**
   * 根据key选择合适的缓存策略
   */
  private selectStrategy(key: string | CacheKey): CacheStrategy {
    const keyStr = this.normalizeKey(key);
    
    // 按模式匹配查找策略
    for (const pattern of this.config.patterns) {
      if (this.matchPattern(keyStr, pattern.pattern)) {
        const strategy = this.strategies.get(pattern.strategy);
        if (strategy) {
          return strategy;
        }
      }
    }
    
    // 使用默认策略
    const defaultStrategy = this.strategies.get(this.config.defaultStrategy);
    if (!defaultStrategy) {
      throw new Error(`Default cache strategy '${this.config.defaultStrategy}' not found`);
    }
    
    return defaultStrategy;
  }

  private matchPattern(key: string, pattern: string): boolean {
    // 简单的通配符匹配
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return regex.test(key);
  }

  private normalizeKey(key: string | CacheKey): string {
    if (typeof key === 'string') {
      return key;
    }
    
    const parts = [key.prefix, key.identifier];
    if (key.version) {
      parts.push(`v${key.version}`);
    }
    
    return parts.join(':');
  }

  private isCircuitOpen(strategyName: string): boolean {
    const breaker = this.circuitBreakers.get(strategyName);
    if (!breaker || !this.config.circuitBreakerConfig) {
      return false;
    }

    const now = Date.now();
    const { failureThreshold, timeout, monitoringPeriod } = this.config.circuitBreakerConfig;

    if (breaker.state === 'OPEN') {
      if (now - breaker.lastFailureTime > timeout) {
        breaker.state = 'HALF_OPEN';
        return false;
      }
      return true;
    }

    if (breaker.state === 'HALF_OPEN') {
      return false;
    }

    // CLOSED state
    if (now - breaker.lastFailureTime > monitoringPeriod) {
      breaker.failures = 0;
    }

    return breaker.failures >= failureThreshold;
  }

  private recordSuccess(strategyName: string): void {
    if (!this.config.enableCircuitBreaker) return;

    let breaker = this.circuitBreakers.get(strategyName);
    if (!breaker) {
      breaker = { failures: 0, lastFailureTime: 0, state: 'CLOSED' };
      this.circuitBreakers.set(strategyName, breaker);
    }

    if (breaker.state === 'HALF_OPEN') {
      breaker.state = 'CLOSED';
      breaker.failures = 0;
    }
  }

  private recordFailure(strategyName: string): void {
    if (!this.config.enableCircuitBreaker) return;

    let breaker = this.circuitBreakers.get(strategyName);
    if (!breaker) {
      breaker = { failures: 0, lastFailureTime: 0, state: 'CLOSED' };
      this.circuitBreakers.set(strategyName, breaker);
    }

    breaker.failures++;
    breaker.lastFailureTime = Date.now();

    const { failureThreshold } = this.config.circuitBreakerConfig!;
    if (breaker.failures >= failureThreshold) {
      breaker.state = 'OPEN';
    }
  }

  private updateResponseTime(responseTime: number): void {
    this.metrics.responseTimeSum += responseTime;
    this.metrics.avgResponseTime = this.metrics.responseTimeSum / this.metrics.requests;
  }

  private async acquireLock(lockKey: string, timeout: number): Promise<boolean> {
    // 简化的锁实现，实际应该使用Redis或其他分布式锁
    return true;
  }

  private async releaseLock(lockKey: string): Promise<void> {
    // 释放锁的实现
  }

  private async scheduleRefresh<T>(
    key: string | CacheKey,
    strategy: CacheStrategy,
    loader?: () => Promise<T>
  ): Promise<void> {
    if (!loader) return;

    try {
      const newValue = await loader();
      await strategy.set(key, newValue);
    } catch (error) {
      this.logger.warn(`Failed to refresh cache for key: ${this.normalizeKey(key)}`, error);
    }
  }

  private async refreshInBackground<T>(
    key: string | CacheKey,
    loader: () => Promise<T>,
    options?: CacheOptions
  ): Promise<void> {
    // 异步刷新
    setImmediate(async () => {
      try {
        const newValue = await loader();
        await this.set(key, newValue, options);
      } catch (error) {
        this.logger.warn(`Background refresh failed for key: ${this.normalizeKey(key)}`, error);
      }
    });
  }
}