export interface CacheKey {
  prefix: string;
  identifier: string;
  version?: string;
  tags?: string[];
}

export interface CacheOptions {
  ttl?: number; // 生存时间（毫秒）
  maxAge?: number; // 最大存活时间
  staleWhileRevalidate?: number; // 过期后继续提供旧数据的时间
  refreshAhead?: number; // 提前刷新时间
  tags?: string[]; // 缓存标签，用于批量失效
  compress?: boolean; // 是否压缩数据
  serialize?: boolean; // 是否序列化
}

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  tags: string[];
  hits: number;
  size: number;
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalKeys: number;
  memoryUsage: number;
  evictions: number;
}

export enum CacheEvictionPolicy {
  LRU = 'lru',
  LFU = 'lfu', 
  FIFO = 'fifo',
  TTL = 'ttl',
}

export interface CacheStrategy {
  name: string;
  get<T>(key: string | CacheKey): Promise<T | null>;
  set<T>(key: string | CacheKey, value: T, options?: CacheOptions): Promise<void>;
  del(key: string | CacheKey): Promise<boolean>;
  invalidateByTags(tags: string[]): Promise<number>;
  clear(): Promise<void>;
  exists(key: string | CacheKey): Promise<boolean>;
  ttl(key: string | CacheKey): Promise<number>;
  getStats(): Promise<CacheStats>;
}

export interface MultiLevelCacheConfig {
  l1: {
    strategy: string;
    maxSize: number;
    ttl: number;
  };
  l2: {
    strategy: string;
    ttl: number;
  };
  l3?: {
    strategy: string;
    ttl: number;
  };
}