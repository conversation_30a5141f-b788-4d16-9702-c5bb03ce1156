import { Injectable, Inject, Logger } from '@nestjs/common';
import {
  CacheStrategy,
  CacheKey,
  CacheOptions,
  CacheStats,
  MultiLevelCacheConfig,
} from './cache-strategy.interface';

interface CacheLevel {
  strategy: CacheStrategy;
  config: {
    maxSize?: number;
    ttl: number;
    writeThrough?: boolean; // 是否写穿透
    writeBack?: boolean; // 是否写回
  };
}

@Injectable()
export class MultiLevelCacheStrategy implements CacheStrategy {
  readonly name = 'multi-level';
  private readonly logger = new Logger(MultiLevelCacheStrategy.name);
  private readonly levels: CacheLevel[] = [];

  constructor(
    @Inject('CACHE_STRATEGIES') private readonly strategies: Map<string, CacheStrategy>,
    @Inject('MULTI_LEVEL_CONFIG') private readonly config: MultiLevelCacheConfig,
  ) {
    this.initializeLevels();
  }

  async get<T>(key: string | CacheKey): Promise<T | null> {
    let value: T | null = null;
    let foundLevel = -1;

    // 从L1开始查找
    for (let i = 0; i < this.levels.length; i++) {
      const level = this.levels[i];
      value = await level.strategy.get<T>(key);
      
      if (value !== null) {
        foundLevel = i;
        break;
      }
    }

    // 如果在较低级别找到数据，需要回填到上级缓存
    if (value !== null && foundLevel > 0) {
      await this.backfillCache(key, value, foundLevel);
    }

    return value;
  }

  async set<T>(key: string | CacheKey, value: T, options: CacheOptions = {}): Promise<void> {
    const promises: Promise<void>[] = [];

    // 根据配置决定写入策略
    for (let i = 0; i < this.levels.length; i++) {
      const level = this.levels[i];
      const levelTtl = this.calculateLevelTtl(level.config.ttl, options.ttl);
      
      const levelOptions: CacheOptions = {
        ...options,
        ttl: levelTtl,
      };

      promises.push(level.strategy.set(key, value, levelOptions));
    }

    // 并行写入所有级别
    await Promise.allSettled(promises);
  }

  async del(key: string | CacheKey): Promise<boolean> {
    const promises = this.levels.map(level => level.strategy.del(key));
    const results = await Promise.allSettled(promises);
    
    // 只要有一个级别删除成功就返回true
    return results.some(result => result.status === 'fulfilled' && result.value);
  }

  async invalidateByTags(tags: string[]): Promise<number> {
    let totalInvalidated = 0;
    
    const promises = this.levels.map(level => level.strategy.invalidateByTags(tags));
    const results = await Promise.allSettled(promises);
    
    for (const result of results) {
      if (result.status === 'fulfilled') {
        totalInvalidated += result.value;
      }
    }
    
    return totalInvalidated;
  }

  async clear(): Promise<void> {
    const promises = this.levels.map(level => level.strategy.clear());
    await Promise.allSettled(promises);
  }

  async exists(key: string | CacheKey): Promise<boolean> {
    // 从L1开始检查
    for (const level of this.levels) {
      const exists = await level.strategy.exists(key);
      if (exists) {
        return true;
      }
    }
    return false;
  }

  async ttl(key: string | CacheKey): Promise<number> {
    // 返回最高级别缓存的TTL
    for (const level of this.levels) {
      const exists = await level.strategy.exists(key);
      if (exists) {
        return await level.strategy.ttl(key);
      }
    }
    return -1;
  }

  async getStats(): Promise<CacheStats> {
    const levelStats = await Promise.all(
      this.levels.map(level => level.strategy.getStats())
    );

    // 合并统计信息
    const combinedStats: CacheStats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalKeys: 0,
      memoryUsage: 0,
      evictions: 0,
    };

    for (const stats of levelStats) {
      combinedStats.hits += stats.hits;
      combinedStats.misses += stats.misses;
      combinedStats.totalKeys += stats.totalKeys;
      combinedStats.memoryUsage += stats.memoryUsage;
      combinedStats.evictions += stats.evictions;
    }

    const total = combinedStats.hits + combinedStats.misses;
    combinedStats.hitRate = total > 0 ? combinedStats.hits / total : 0;

    return combinedStats;
  }

  /**
   * 获取各级别的详细统计
   */
  async getLevelStats(): Promise<Array<{ level: number; strategy: string; stats: CacheStats }>> {
    const results = [];
    
    for (let i = 0; i < this.levels.length; i++) {
      const level = this.levels[i];
      const stats = await level.strategy.getStats();
      results.push({
        level: i + 1,
        strategy: level.strategy.name,
        stats,
      });
    }
    
    return results;
  }

  /**
   * 强制同步所有级别的数据
   */
  async syncLevels(key: string | CacheKey): Promise<void> {
    let value: any = null;
    
    // 从最高级别获取数据
    for (const level of this.levels) {
      value = await level.strategy.get(key);
      if (value !== null) {
        break;
      }
    }
    
    if (value !== null) {
      // 写入所有级别
      const promises = this.levels.map(level => 
        level.strategy.set(key, value, { ttl: level.config.ttl })
      );
      await Promise.allSettled(promises);
    }
  }

  /**
   * 预热缓存
   */
  async warmup(
    keys: Array<{ 
      key: string | CacheKey; 
      loader: () => Promise<any>; 
      options?: CacheOptions;
      levels?: number[]; // 指定预热的级别
    }>
  ): Promise<void> {
    const promises = keys.map(async ({ key, loader, options, levels }) => {
      try {
        const value = await loader();
        
        if (levels) {
          // 只预热指定级别
          const targetLevels = levels.map(l => this.levels[l - 1]).filter(Boolean);
          await Promise.allSettled(
            targetLevels.map(level => 
              level.strategy.set(key, value, { 
                ...options, 
                ttl: level.config.ttl 
              })
            )
          );
        } else {
          // 预热所有级别
          await this.set(key, value, options);
        }
      } catch (error) {
        this.logger.warn(`Failed to warmup cache for key: ${this.normalizeKey(key)}`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * 批量操作支持
   */
  async mget<T>(keys: Array<string | CacheKey>): Promise<Array<T | null>> {
    const results: Array<T | null> = new Array(keys.length).fill(null);
    const remainingKeys: number[] = Array.from({ length: keys.length }, (_, i) => i);

    // 按级别依次查询
    for (let levelIndex = 0; levelIndex < this.levels.length && remainingKeys.length > 0; levelIndex++) {
      const level = this.levels[levelIndex];
      const keysToQuery = remainingKeys.map(i => keys[i]);
      
      try {
        const levelResults = await (level.strategy as any).mget?.(keysToQuery) || 
          await Promise.all(keysToQuery.map(key => level.strategy.get<T>(key)));
        
        // 处理结果并准备回填
        const backfillData: Array<{ index: number; key: string | CacheKey; value: T }> = [];
        
        for (let i = remainingKeys.length - 1; i >= 0; i--) {
          const resultIndex = remainingKeys[i];
          const value = levelResults[i];
          
          if (value !== null) {
            results[resultIndex] = value;
            
            // 如果不是从L1获取的数据，需要回填
            if (levelIndex > 0) {
              backfillData.push({
                index: resultIndex,
                key: keys[resultIndex],
                value,
              });
            }
            
            remainingKeys.splice(i, 1);
          }
        }
        
        // 执行回填
        if (backfillData.length > 0) {
          this.backfillMultiple(backfillData, levelIndex);
        }
      } catch (error) {
        this.logger.warn(`Failed to mget from level ${levelIndex + 1}`, error);
      }
    }

    return results;
  }

  private initializeLevels(): void {
    // L1 缓存配置
    if (this.config.l1) {
      const strategy = this.strategies.get(this.config.l1.strategy);
      if (strategy) {
        this.levels.push({
          strategy,
          config: {
            maxSize: this.config.l1.maxSize,
            ttl: this.config.l1.ttl,
            writeThrough: true,
          },
        });
      }
    }

    // L2 缓存配置
    if (this.config.l2) {
      const strategy = this.strategies.get(this.config.l2.strategy);
      if (strategy) {
        this.levels.push({
          strategy,
          config: {
            ttl: this.config.l2.ttl,
            writeThrough: true,
          },
        });
      }
    }

    // L3 缓存配置（可选）
    if (this.config.l3) {
      const strategy = this.strategies.get(this.config.l3.strategy);
      if (strategy) {
        this.levels.push({
          strategy,
          config: {
            ttl: this.config.l3.ttl,
            writeThrough: false,
          },
        });
      }
    }

    this.logger.log(`Initialized ${this.levels.length} cache levels`);
  }

  private async backfillCache<T>(key: string | CacheKey, value: T, foundLevel: number): Promise<void> {
    // 回填到更高级别的缓存
    const backfillPromises = [];
    
    for (let i = 0; i < foundLevel; i++) {
      const level = this.levels[i];
      const ttl = level.config.ttl;
      
      backfillPromises.push(
        level.strategy.set(key, value, { ttl }).catch(error => {
          this.logger.warn(`Failed to backfill level ${i + 1}`, error);
        })
      );
    }
    
    // 异步执行回填，不阻塞主流程
    Promise.allSettled(backfillPromises);
  }

  private async backfillMultiple<T>(
    data: Array<{ index: number; key: string | CacheKey; value: T }>,
    foundLevel: number
  ): Promise<void> {
    // 批量回填到更高级别的缓存
    for (let i = 0; i < foundLevel; i++) {
      const level = this.levels[i];
      const ttl = level.config.ttl;
      
      const backfillPromises = data.map(({ key, value }) =>
        level.strategy.set(key, value, { ttl }).catch(error => {
          this.logger.warn(`Failed to backfill key ${this.normalizeKey(key)} to level ${i + 1}`, error);
        })
      );
      
      // 异步执行回填
      Promise.allSettled(backfillPromises);
    }
  }

  private calculateLevelTtl(levelTtl: number, optionsTtl?: number): number {
    if (optionsTtl === undefined) {
      return levelTtl;
    }
    
    // 使用较小的TTL值
    return Math.min(levelTtl, optionsTtl);
  }

  private normalizeKey(key: string | CacheKey): string {
    if (typeof key === 'string') {
      return key;
    }
    
    const parts = [key.prefix, key.identifier];
    if (key.version) {
      parts.push(`v${key.version}`);
    }
    
    return parts.join(':');
  }
}