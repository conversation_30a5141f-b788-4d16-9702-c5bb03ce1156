import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  // 用户缓存 - 30分钟
  async setUser(userId: string, userData: any): Promise<void> {
    const key = `user:${userId}`;
    await this.cacheManager.set(key, userData, 30 * 60 * 1000);
  }

  async getUser(userId: string): Promise<any> {
    const key = `user:${userId}`;
    return this.cacheManager.get(key);
  }

  async deleteUser(userId: string): Promise<void> {
    const key = `user:${userId}`;
    await this.cacheManager.del(key);
  }

  // API响应缓存 - 5分钟
  async setApiResponse(path: string, queryParams: any, data: any): Promise<void> {
    const key = this.generateApiCacheKey(path, queryParams);
    await this.cacheManager.set(key, data, 5 * 60 * 1000);
  }

  async getApiResponse(path: string, queryParams: any): Promise<any> {
    const key = this.generateApiCacheKey(path, queryParams);
    return this.cacheManager.get(key);
  }

  // 统计数据缓存 - 1小时
  async setStats(type: string, date: string, data: any): Promise<void> {
    const key = `stats:${type}:${date}`;
    await this.cacheManager.set(key, data, 60 * 60 * 1000);
  }

  async getStats(type: string, date: string): Promise<any> {
    const key = `stats:${type}:${date}`;
    return this.cacheManager.get(key);
  }

  // 验证码缓存
  async setVerificationCode(
    type: 'email' | 'sms',
    target: string,
    code: string,
    ttl: number = 5 * 60 * 1000, // 5分钟
  ): Promise<void> {
    const key = `verification:${type}:${target}`;
    await this.cacheManager.set(key, code, ttl);
  }

  async getVerificationCode(type: 'email' | 'sms', target: string): Promise<string | null> {
    const key = `verification:${type}:${target}`;
    return this.cacheManager.get(key);
  }

  async deleteVerificationCode(type: 'email' | 'sms', target: string): Promise<void> {
    const key = `verification:${type}:${target}`;
    await this.cacheManager.del(key);
  }

  // JWT黑名单
  async addToBlacklist(jti: string, exp: number): Promise<void> {
    const key = `blacklist:${jti}`;
    const ttl = (exp * 1000) - Date.now(); // 过期时间
    if (ttl > 0) {
      await this.cacheManager.set(key, true, ttl);
    }
  }

  async isBlacklisted(jti: string): Promise<boolean> {
    const key = `blacklist:${jti}`;
    const result = await this.cacheManager.get(key);
    return !!result;
  }

  // 通用缓存方法
  async set(key: string, value: any, ttl?: number): Promise<void> {
    await this.cacheManager.set(key, value, ttl);
  }

  async get<T>(key: string): Promise<T | null> {
    return this.cacheManager.get<T>(key);
  }

  async del(key: string): Promise<void> {
    await this.cacheManager.del(key);
  }

  async reset(): Promise<void> {
    // cache-manager v6 doesn't have reset method, use store.reset if available
    const store = (this.cacheManager as any).store;
    if (store && typeof store.reset === 'function') {
      await store.reset();
    }
  }

  // 批量操作
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const promises = keys.map(key => this.get<T>(key));
    return Promise.all(promises);
  }

  async mset(keyValues: Array<{ key: string; value: any; ttl?: number }>): Promise<void> {
    const promises = keyValues.map(({ key, value, ttl }) => this.set(key, value, ttl));
    await Promise.all(promises);
  }

  async mdel(keys: string[]): Promise<void> {
    const promises = keys.map(key => this.del(key));
    await Promise.all(promises);
  }

  // 工具方法
  private generateApiCacheKey(path: string, queryParams: any): string {
    const queryString = Object.keys(queryParams)
      .sort()
      .map(key => `${key}=${queryParams[key]}`)
      .join('&');
    
    return `api:${path}:${this.hashString(queryString)}`;
  }

  private hashString(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString();
  }

  // 缓存装饰器辅助方法
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    let cached = await this.get<T>(key);
    
    if (cached === null || cached === undefined) {
      cached = await factory();
      await this.set(key, cached, ttl);
    }
    
    return cached;
  }
}