import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as client from 'prom-client';

@Injectable()
export class PrometheusService implements OnModuleInit {
  private readonly logger = new Logger(PrometheusService.name);
  private registry: client.Registry;

  // HTTP指标
  private httpRequestsTotal: client.Counter<string>;
  private httpRequestDuration: client.Histogram<string>;
  private httpRequestsInFlight: client.Gauge<string>;

  // 系统指标
  private systemMemoryUsage: client.Gauge<string>;
  private systemCpuUsage: client.Gauge<string>;
  private systemUptime: client.Gauge<string>;

  // 应用指标
  private activeUsers: client.Gauge<string>;
  private databaseConnections: client.Gauge<string>;
  private cacheHitRate: client.Gauge<string>;
  private queueSize: client.Gauge<string>;

  // 业务指标
  private userRegistrations: client.Counter<string>;
  private documentUploads: client.Counter<string>;
  private authenticationAttempts: client.Counter<string>;

  // 错误指标
  private errorRate: client.Gauge<string>;
  private databaseErrors: client.Counter<string>;
  private cacheErrors: client.Counter<string>;

  constructor(private configService: ConfigService) {
    this.registry = new client.Registry();
    this.initializeMetrics();
  }

  onModuleInit() {
    // 收集默认指标
    client.collectDefaultMetrics({
      register: this.registry,
      prefix: 'masteryos_',
    });

    this.logger.log('Prometheus metrics initialized');
  }

  private initializeMetrics() {
    // HTTP请求指标
    this.httpRequestsTotal = new client.Counter({
      name: 'masteryos_http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code', 'version'],
      registers: [this.registry],
    });

    this.httpRequestDuration = new client.Histogram({
      name: 'masteryos_http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
      registers: [this.registry],
    });

    this.httpRequestsInFlight = new client.Gauge({
      name: 'masteryos_http_requests_in_flight',
      help: 'Current number of HTTP requests being processed',
      registers: [this.registry],
    });

    // 系统指标
    this.systemMemoryUsage = new client.Gauge({
      name: 'masteryos_system_memory_usage_bytes',
      help: 'System memory usage in bytes',
      labelNames: ['type'],
      registers: [this.registry],
    });

    this.systemCpuUsage = new client.Gauge({
      name: 'masteryos_system_cpu_usage_percent',
      help: 'System CPU usage percentage',
      registers: [this.registry],
    });

    this.systemUptime = new client.Gauge({
      name: 'masteryos_system_uptime_seconds',
      help: 'System uptime in seconds',
      registers: [this.registry],
    });

    // 应用指标
    this.activeUsers = new client.Gauge({
      name: 'masteryos_active_users_total',
      help: 'Number of currently active users',
      registers: [this.registry],
    });

    this.databaseConnections = new client.Gauge({
      name: 'masteryos_database_connections_total',
      help: 'Number of database connections',
      labelNames: ['state'],
      registers: [this.registry],
    });

    this.cacheHitRate = new client.Gauge({
      name: 'masteryos_cache_hit_rate',
      help: 'Cache hit rate (0-1)',
      labelNames: ['cache_name'],
      registers: [this.registry],
    });

    this.queueSize = new client.Gauge({
      name: 'masteryos_queue_size_total',
      help: 'Number of items in queue',
      labelNames: ['queue_name'],
      registers: [this.registry],
    });

    // 业务指标
    this.userRegistrations = new client.Counter({
      name: 'masteryos_user_registrations_total',
      help: 'Total number of user registrations',
      labelNames: ['source'],
      registers: [this.registry],
    });

    this.documentUploads = new client.Counter({
      name: 'masteryos_document_uploads_total',
      help: 'Total number of document uploads',
      labelNames: ['type', 'user_role'],
      registers: [this.registry],
    });

    this.authenticationAttempts = new client.Counter({
      name: 'masteryos_authentication_attempts_total',
      help: 'Total number of authentication attempts',
      labelNames: ['type', 'result'],
      registers: [this.registry],
    });

    // 错误指标
    this.errorRate = new client.Gauge({
      name: 'masteryos_error_rate',
      help: 'Application error rate (0-1)',
      labelNames: ['service'],
      registers: [this.registry],
    });

    this.databaseErrors = new client.Counter({
      name: 'masteryos_database_errors_total',
      help: 'Total number of database errors',
      labelNames: ['type', 'operation'],
      registers: [this.registry],
    });

    this.cacheErrors = new client.Counter({
      name: 'masteryos_cache_errors_total',
      help: 'Total number of cache errors',
      labelNames: ['type', 'operation'],
      registers: [this.registry],
    });
  }

  /**
   * 记录HTTP请求指标
   */
  recordHttpRequest(
    method: string,
    route: string,
    statusCode: number,
    duration: number,
    version?: string
  ): void {
    const labels = {
      method,
      route,
      status_code: statusCode.toString(),
      version: version || 'v1',
    };

    this.httpRequestsTotal.inc(labels);
    this.httpRequestDuration.observe(
      { method, route, status_code: statusCode.toString() },
      duration / 1000 // 转换为秒
    );
  }

  /**
   * 更新正在处理的请求数
   */
  incrementRequestsInFlight(): void {
    this.httpRequestsInFlight.inc();
  }

  decrementRequestsInFlight(): void {
    this.httpRequestsInFlight.dec();
  }

  /**
   * 更新系统指标
   */
  updateSystemMetrics(metrics: {
    memoryUsage: { total: number; used: number; free: number; heap: number };
    cpuUsage: number;
    uptime: number;
  }): void {
    this.systemMemoryUsage.set({ type: 'total' }, metrics.memoryUsage.total);
    this.systemMemoryUsage.set({ type: 'used' }, metrics.memoryUsage.used);
    this.systemMemoryUsage.set({ type: 'free' }, metrics.memoryUsage.free);
    this.systemMemoryUsage.set({ type: 'heap' }, metrics.memoryUsage.heap);
    
    this.systemCpuUsage.set(metrics.cpuUsage);
    this.systemUptime.set(metrics.uptime);
  }

  /**
   * 更新应用指标
   */
  updateApplicationMetrics(metrics: {
    activeUsers: number;
    databaseConnections: { active: number; idle: number; total: number };
    cacheHitRate: number;
    queueSizes: Record<string, number>;
  }): void {
    this.activeUsers.set(metrics.activeUsers);
    
    this.databaseConnections.set({ state: 'active' }, metrics.databaseConnections.active);
    this.databaseConnections.set({ state: 'idle' }, metrics.databaseConnections.idle);
    this.databaseConnections.set({ state: 'total' }, metrics.databaseConnections.total);
    
    this.cacheHitRate.set({ cache_name: 'default' }, metrics.cacheHitRate);
    
    Object.entries(metrics.queueSizes).forEach(([queueName, size]) => {
      this.queueSize.set({ queue_name: queueName }, size);
    });
  }

  /**
   * 记录业务事件
   */
  recordUserRegistration(source: string = 'web'): void {
    this.userRegistrations.inc({ source });
  }

  recordDocumentUpload(type: string, userRole: string): void {
    this.documentUploads.inc({ type, user_role: userRole });
  }

  recordAuthenticationAttempt(type: 'login' | 'refresh' | 'logout', result: 'success' | 'failure'): void {
    this.authenticationAttempts.inc({ type, result });
  }

  /**
   * 记录错误指标
   */
  updateErrorRate(service: string, errorRate: number): void {
    this.errorRate.set({ service }, errorRate);
  }

  recordDatabaseError(type: string, operation: string): void {
    this.databaseErrors.inc({ type, operation });
  }

  recordCacheError(type: string, operation: string): void {
    this.cacheErrors.inc({ type, operation });
  }

  /**
   * 获取所有指标
   */
  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  /**
   * 获取指标的JSON格式
   */
  async getMetricsAsJson(): Promise<client.MetricObjectWithValues<client.MetricValue<string>>[]> {
    return this.registry.getMetricsAsJSON();
  }

  /**
   * 重置所有指标
   */
  resetMetrics(): void {
    this.registry.resetMetrics();
    this.logger.log('Prometheus metrics reset');
  }

  /**
   * 获取特定指标
   */
  getMetric(name: string): client.Metric<string> | undefined {
    return this.registry.getSingleMetric(name);
  }

  /**
   * 创建自定义指标
   */
  createCounter(name: string, help: string, labelNames?: string[]): client.Counter<string> {
    return new client.Counter({
      name: `masteryos_${name}`,
      help,
      labelNames,
      registers: [this.registry],
    });
  }

  createGauge(name: string, help: string, labelNames?: string[]): client.Gauge<string> {
    return new client.Gauge({
      name: `masteryos_${name}`,
      help,
      labelNames,
      registers: [this.registry],
    });
  }

  createHistogram(
    name: string,
    help: string,
    labelNames?: string[],
    buckets?: number[]
  ): client.Histogram<string> {
    return new client.Histogram({
      name: `masteryos_${name}`,
      help,
      labelNames,
      buckets,
      registers: [this.registry],
    });
  }

  /**
   * 健康检查
   */
  isHealthy(): boolean {
    try {
      // 检查registry是否正常工作
      this.registry.metrics();
      return true;
    } catch (error) {
      this.logger.error('Prometheus health check failed', error);
      return false;
    }
  }

  /**
   * 获取指标统计信息
   */
  async getMetricsStats(): Promise<{
    totalMetrics: number;
    metricTypes: Record<string, number>;
    lastUpdated: Date;
  }> {
    const metrics = await this.registry.getMetricsAsJSON();
    const metricTypes: Record<string, number> = {};

    metrics.forEach(metric => {
      metricTypes[metric.type] = (metricTypes[metric.type] || 0) + 1;
    });

    return {
      totalMetrics: metrics.length,
      metricTypes,
      lastUpdated: new Date(),
    };
  }
}