import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggingService } from '../logging/logging.service';
import * as os from 'os';
import * as process from 'process';

export interface SystemMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    cores: number;
    loadAvg: number[];
  };
  memory: {
    total: number;
    free: number;
    used: number;
    usagePercent: number;
    heap: {
      total: number;
      used: number;
      external: number;
    };
  };
  disk: {
    free: number;
    used: number;
    total: number;
    usagePercent: number;
  };
  uptime: number;
  pid: number;
}

export interface ApplicationMetrics {
  timestamp: string;
  requests: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
    requestsPerSecond: number;
  };
  errors: {
    total: number;
    rate: number;
    byType: Record<string, number>;
  };
  database: {
    connections: {
      active: number;
      idle: number;
      total: number;
    };
    queries: {
      total: number;
      slow: number;
      averageTime: number;
    };
  };
  cache: {
    hits: number;
    misses: number;
    hitRate: number;
    operations: number;
  };
  auth: {
    logins: number;
    logouts: number;
    activeUsers: number;
    failures: number;
  };
}

export interface HealthStatus {
  status: 'healthy' | 'warning' | 'critical';
  timestamp: string;
  uptime: number;
  version: string;
  checks: {
    database: { status: 'up' | 'down'; responseTime?: number; error?: string };
    redis: { status: 'up' | 'down'; responseTime?: number; error?: string };
    disk: { status: 'ok' | 'warning' | 'critical'; usage: number };
    memory: { status: 'ok' | 'warning' | 'critical'; usage: number };
    cpu: { status: 'ok' | 'warning' | 'critical'; usage: number };
  };
}

@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);
  private readonly metrics: {
    requests: Map<string, { count: number; totalTime: number; errors: number }>;
    errors: Map<string, number>;
    database: { queries: number; slowQueries: number; totalTime: number };
    cache: { hits: number; misses: number; operations: number };
    auth: { logins: number; logouts: number; failures: number };
  };

  private startTime: number;
  private lastSystemCheck: number = 0;
  private systemMetricsCache: SystemMetrics | null = null;

  constructor(
    private configService: ConfigService,
    private loggingService: LoggingService,
  ) {
    this.startTime = Date.now();
    this.metrics = {
      requests: new Map(),
      errors: new Map(),
      database: { queries: 0, slowQueries: 0, totalTime: 0 },
      cache: { hits: 0, misses: 0, operations: 0 },
      auth: { logins: 0, logouts: 0, failures: 0 },
    };

    // 启动定期监控
    this.startPeriodicMonitoring();
  }

  /**
   * 记录API请求指标
   */
  recordApiRequest(method: string, path: string, statusCode: number, responseTime: number): void {
    const key = `${method} ${path}`;
    const current = this.metrics.requests.get(key) || { count: 0, totalTime: 0, errors: 0 };
    
    current.count++;
    current.totalTime += responseTime;
    
    if (statusCode >= 400) {
      current.errors++;
    }
    
    this.metrics.requests.set(key, current);

    // 记录性能指标
    if (responseTime > 1000) {
      this.loggingService.logPerformance('SLOW_API_REQUEST', responseTime, {
        method,
        path,
        statusCode,
      });
    }
  }

  /**
   * 记录错误
   */
  recordError(errorType: string, error?: Error): void {
    const current = this.metrics.errors.get(errorType) || 0;
    this.metrics.errors.set(errorType, current + 1);

    if (error) {
      this.loggingService.error(`Application Error: ${errorType}`, error);
    }
  }

  /**
   * 记录数据库查询指标
   */
  recordDatabaseQuery(duration: number, isSlow: boolean = false): void {
    this.metrics.database.queries++;
    this.metrics.database.totalTime += duration;
    
    if (isSlow) {
      this.metrics.database.slowQueries++;
    }
  }

  /**
   * 记录缓存操作
   */
  recordCacheOperation(operation: 'hit' | 'miss' | 'set' | 'del'): void {
    this.metrics.cache.operations++;
    
    if (operation === 'hit') {
      this.metrics.cache.hits++;
    } else if (operation === 'miss') {
      this.metrics.cache.misses++;
    }
  }

  /**
   * 记录认证事件
   */
  recordAuthEvent(event: 'login' | 'logout' | 'failure'): void {
    switch (event) {
      case 'login':
        this.metrics.auth.logins++;
        break;
      case 'logout':
        this.metrics.auth.logouts++;
        break;
      case 'failure':
        this.metrics.auth.failures++;
        break;
    }
  }

  /**
   * 获取系统指标
   */
  async getSystemMetrics(): Promise<SystemMetrics> {
    const now = Date.now();
    
    // 如果缓存有效（5秒内），直接返回缓存
    if (this.systemMetricsCache && (now - this.lastSystemCheck) < 5000) {
      return this.systemMetricsCache;
    }

    const memoryUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    // 获取CPU使用率（简化版本）
    const cpuUsage = await this.getCpuUsage();

    const metrics: SystemMetrics = {
      timestamp: new Date().toISOString(),
      cpu: {
        usage: cpuUsage,
        cores: os.cpus().length,
        loadAvg: os.loadavg(),
      },
      memory: {
        total: totalMemory,
        free: freeMemory,
        used: usedMemory,
        usagePercent: (usedMemory / totalMemory) * 100,
        heap: {
          total: memoryUsage.heapTotal,
          used: memoryUsage.heapUsed,
          external: memoryUsage.external,
        },
      },
      disk: {
        free: 0, // 需要实现磁盘空间检查
        used: 0,
        total: 0,
        usagePercent: 0,
      },
      uptime: process.uptime(),
      pid: process.pid,
    };

    this.systemMetricsCache = metrics;
    this.lastSystemCheck = now;

    return metrics;
  }

  /**
   * 获取应用指标
   */
  getApplicationMetrics(): ApplicationMetrics {
    const now = Date.now();
    const uptime = now - this.startTime;
    const uptimeSeconds = uptime / 1000;

    // 计算请求统计
    let totalRequests = 0;
    let totalErrors = 0;
    let totalResponseTime = 0;

    for (const stats of this.metrics.requests.values()) {
      totalRequests += stats.count;
      totalErrors += stats.errors;
      totalResponseTime += stats.totalTime;
    }

    const successfulRequests = totalRequests - totalErrors;
    const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;
    const requestsPerSecond = uptimeSeconds > 0 ? totalRequests / uptimeSeconds : 0;

    // 计算错误统计
    const totalErrorCount = Array.from(this.metrics.errors.values()).reduce((sum, count) => sum + count, 0);
    const errorRate = totalRequests > 0 ? totalErrorCount / totalRequests : 0;
    const errorsByType: Record<string, number> = {};
    
    for (const [type, count] of this.metrics.errors.entries()) {
      errorsByType[type] = count;
    }

    // 缓存统计
    const cacheHitRate = this.metrics.cache.operations > 0 
      ? this.metrics.cache.hits / (this.metrics.cache.hits + this.metrics.cache.misses) 
      : 0;

    return {
      timestamp: new Date().toISOString(),
      requests: {
        total: totalRequests,
        successful: successfulRequests,
        failed: totalErrors,
        averageResponseTime,
        requestsPerSecond,
      },
      errors: {
        total: totalErrorCount,
        rate: errorRate,
        byType: errorsByType,
      },
      database: {
        connections: {
          active: 0, // 需要从数据库连接池获取
          idle: 0,
          total: 0,
        },
        queries: {
          total: this.metrics.database.queries,
          slow: this.metrics.database.slowQueries,
          averageTime: this.metrics.database.queries > 0 
            ? this.metrics.database.totalTime / this.metrics.database.queries 
            : 0,
        },
      },
      cache: {
        hits: this.metrics.cache.hits,
        misses: this.metrics.cache.misses,
        hitRate: cacheHitRate,
        operations: this.metrics.cache.operations,
      },
      auth: {
        logins: this.metrics.auth.logins,
        logouts: this.metrics.auth.logouts,
        activeUsers: 0, // 需要从会话存储获取
        failures: this.metrics.auth.failures,
      },
    };
  }

  /**
   * 获取健康状态
   */
  async getHealthStatus(): Promise<HealthStatus> {
    const systemMetrics = await this.getSystemMetrics();
    const appMetrics = this.getApplicationMetrics();

    // 检查各项健康指标
    const checks = {
      database: await this.checkDatabaseHealth(),
      redis: await this.checkRedisHealth(),
      disk: this.checkDiskHealth(systemMetrics),
      memory: this.checkMemoryHealth(systemMetrics),
      cpu: this.checkCpuHealth(systemMetrics),
    };

    // 计算整体健康状态
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    for (const check of Object.values(checks)) {
      if (check.status === 'critical' || check.status === 'down') {
        status = 'critical';
        break;
      } else if (check.status === 'warning') {
        status = 'warning';
      }
    }

    return {
      status,
      timestamp: new Date().toISOString(),
      uptime: systemMetrics.uptime,
      version: process.env.npm_package_version || '1.0.0',
      checks,
    };
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.metrics.requests.clear();
    this.metrics.errors.clear();
    this.metrics.database = { queries: 0, slowQueries: 0, totalTime: 0 };
    this.metrics.cache = { hits: 0, misses: 0, operations: 0 };
    this.metrics.auth = { logins: 0, logouts: 0, failures: 0 };
    
    this.logger.log('Metrics reset successfully');
  }

  /**
   * 启动定期监控
   */
  private startPeriodicMonitoring(): void {
    // 每分钟记录系统指标
    setInterval(async () => {
      try {
        const systemMetrics = await this.getSystemMetrics();
        const appMetrics = this.getApplicationMetrics();

        this.loggingService.logPerformance('SYSTEM_METRICS', 0, systemMetrics);
        this.loggingService.logPerformance('APPLICATION_METRICS', 0, appMetrics);

        // 检查告警条件
        await this.checkAlerts(systemMetrics, appMetrics);
      } catch (error) {
        this.logger.error('Failed to collect periodic metrics', error);
      }
    }, 60000); // 1分钟

    // 每5分钟进行健康检查
    setInterval(async () => {
      try {
        const healthStatus = await this.getHealthStatus();
        this.loggingService.info('Health Check Completed', { 
          additionalData: { healthStatus } 
        });

        if (healthStatus.status === 'critical') {
          this.loggingService.error('System Health Critical', new Error('Health check failed'), {
            additionalData: { checks: healthStatus.checks },
          });
        }
      } catch (error) {
        this.logger.error('Failed to perform health check', error);
      }
    }, 300000); // 5分钟
  }

  /**
   * 检查告警条件
   */
  private async checkAlerts(systemMetrics: SystemMetrics, appMetrics: ApplicationMetrics): Promise<void> {
    // CPU使用率告警
    if (systemMetrics.cpu.usage > 80) {
      this.loggingService.logSecurityEvent('HIGH_CPU_USAGE', {
        usage: systemMetrics.cpu.usage,
        threshold: 80,
      });
    }

    // 内存使用率告警
    if (systemMetrics.memory.usagePercent > 85) {
      this.loggingService.logSecurityEvent('HIGH_MEMORY_USAGE', {
        usage: systemMetrics.memory.usagePercent,
        threshold: 85,
      });
    }

    // 错误率告警
    if (appMetrics.errors.rate > 0.05) { // 5%
      this.loggingService.logSecurityEvent('HIGH_ERROR_RATE', {
        rate: appMetrics.errors.rate,
        threshold: 0.05,
      });
    }

    // 响应时间告警
    if (appMetrics.requests.averageResponseTime > 2000) { // 2秒
      this.loggingService.logSecurityEvent('HIGH_RESPONSE_TIME', {
        averageTime: appMetrics.requests.averageResponseTime,
        threshold: 2000,
      });
    }
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const totalUsage = endUsage.user + endUsage.system;
        const totalTime = 100000; // 100ms in microseconds
        const cpuPercent = (totalUsage / totalTime) * 100;
        
        resolve(Math.min(cpuPercent, 100));
      }, 100);
    });
  }

  /**
   * 检查数据库健康状态
   */
  private async checkDatabaseHealth(): Promise<{ status: 'up' | 'down'; responseTime?: number; error?: string }> {
    try {
      const startTime = Date.now();
      // 这里应该实际执行数据库连接测试
      // 暂时返回模拟结果
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'up',
        responseTime,
      };
    } catch (error) {
      return {
        status: 'down',
        error: error.message,
      };
    }
  }

  /**
   * 检查Redis健康状态
   */
  private async checkRedisHealth(): Promise<{ status: 'up' | 'down'; responseTime?: number; error?: string }> {
    try {
      const startTime = Date.now();
      // 这里应该实际执行Redis连接测试
      // 暂时返回模拟结果
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'up',
        responseTime,
      };
    } catch (error) {
      return {
        status: 'down',
        error: error.message,
      };
    }
  }

  /**
   * 检查磁盘健康状态
   */
  private checkDiskHealth(metrics: SystemMetrics): { status: 'ok' | 'warning' | 'critical'; usage: number } {
    const usage = metrics.disk.usagePercent;
    
    if (usage > 90) {
      return { status: 'critical', usage };
    } else if (usage > 75) {
      return { status: 'warning', usage };
    } else {
      return { status: 'ok', usage };
    }
  }

  /**
   * 检查内存健康状态
   */
  private checkMemoryHealth(metrics: SystemMetrics): { status: 'ok' | 'warning' | 'critical'; usage: number } {
    const usage = metrics.memory.usagePercent;
    
    if (usage > 90) {
      return { status: 'critical', usage };
    } else if (usage > 75) {
      return { status: 'warning', usage };
    } else {
      return { status: 'ok', usage };
    }
  }

  /**
   * 检查CPU健康状态
   */
  private checkCpuHealth(metrics: SystemMetrics): { status: 'ok' | 'warning' | 'critical'; usage: number } {
    const usage = metrics.cpu.usage;
    
    if (usage > 90) {
      return { status: 'critical', usage };
    } else if (usage > 75) {
      return { status: 'warning', usage };
    } else {
      return { status: 'ok', usage };
    }
  }
}