import { Injectable, Logger, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import { Request, Response } from 'express';

export interface LogContext {
  userId?: string;
  requestId?: string;
  userAgent?: string;
  ip?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  error?: Error;
  additionalData?: Record<string, any>;
}

export interface ApiLogData {
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  userId?: string;
  userAgent?: string;
  ip?: string;
  requestId?: string;
  requestBody?: any;
  responseBody?: any;
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
  timestamp: string;
}

@Injectable()
export class LoggingService {
  private readonly logger = new Logger(LoggingService.name);
  private winstonLogger: winston.Logger;

  constructor(private configService: ConfigService) {
    this.initializeWinstonLogger();
  }

  private initializeWinstonLogger() {
    const logLevel = this.configService.get<string>('LOG_LEVEL', 'info');
    const logDir = this.configService.get<string>('LOG_DIR', './logs');
    const enableFileLogging = this.configService.get<boolean>('ENABLE_FILE_LOGGING', true);
    const enableConsoleLogging = this.configService.get<boolean>('ENABLE_CONSOLE_LOGGING', true);

    const transports: winston.transport[] = [];

    // 控制台输出
    if (enableConsoleLogging) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.colorize(),
            winston.format.printf(({ timestamp, level, message, context, ...meta }) => {
              const contextStr = context ? `[${context}] ` : '';
              const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
              return `${timestamp} ${level}: ${contextStr}${message} ${metaStr}`;
            })
          ),
        })
      );
    }

    // 文件输出
    if (enableFileLogging) {
      // 应用日志
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/application-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '14d',
          level: logLevel,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.errors({ stack: true }),
            winston.format.json()
          ),
        })
      );

      // API访问日志
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/api-access-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '50m',
          maxFiles: '30d',
          level: 'info',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          ),
        })
      );

      // 错误日志
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/error-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '30d',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.errors({ stack: true }),
            winston.format.json()
          ),
        })
      );

      // 性能日志
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/performance-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '30m',
          maxFiles: '7d',
          level: 'info',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          ),
        })
      );
    }

    this.winstonLogger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      transports,
      exceptionHandlers: enableFileLogging ? [
        new DailyRotateFile({
          filename: `${logDir}/exceptions-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '14d',
        })
      ] : [],
      rejectionHandlers: enableFileLogging ? [
        new DailyRotateFile({
          filename: `${logDir}/rejections-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '14d',
        })
      ] : [],
    });
  }

  /**
   * 记录信息日志
   */
  info(message: string, context?: LogContext): void {
    this.winstonLogger.info(message, { context: 'Application', ...context });
  }

  /**
   * 记录警告日志
   */
  warn(message: string, context?: LogContext): void {
    this.winstonLogger.warn(message, { context: 'Application', ...context });
  }

  /**
   * 记录错误日志
   */
  error(message: string, error?: Error, context?: LogContext): void {
    this.winstonLogger.error(message, {
      context: 'Application',
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name,
      } : undefined,
      ...context,
    });
  }

  /**
   * 记录调试日志
   */
  debug(message: string, context?: LogContext): void {
    this.winstonLogger.debug(message, { context: 'Application', ...context });
  }

  /**
   * 记录详细日志
   */
  verbose(message: string, context?: LogContext): void {
    this.winstonLogger.verbose(message, { context: 'Application', ...context });
  }

  /**
   * 记录API访问日志
   */
  logApiRequest(data: ApiLogData): void {
    this.winstonLogger.info('API Request', {
      context: 'API',
      type: 'request',
      ...data,
    });
  }

  /**
   * 记录性能数据
   */
  logPerformance(operation: string, duration: number, metadata?: Record<string, any>): void {
    this.winstonLogger.info('Performance Metric', {
      context: 'Performance',
      operation,
      duration,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录数据库查询性能
   */
  logDatabaseQuery(query: string, duration: number, metadata?: Record<string, any>): void {
    this.winstonLogger.info('Database Query', {
      context: 'Database',
      query: this.sanitizeQuery(query),
      duration,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录缓存操作
   */
  logCacheOperation(operation: 'hit' | 'miss' | 'set' | 'del', key: string, metadata?: Record<string, any>): void {
    this.winstonLogger.info('Cache Operation', {
      context: 'Cache',
      operation,
      key,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录安全事件
   */
  logSecurityEvent(event: string, metadata?: Record<string, any>): void {
    this.winstonLogger.warn('Security Event', {
      context: 'Security',
      event,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录业务事件
   */
  logBusinessEvent(event: string, metadata?: Record<string, any>): void {
    this.winstonLogger.info('Business Event', {
      context: 'Business',
      event,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 从请求中提取日志上下文
   */
  extractLogContext(req: Request, res?: Response): LogContext {
    const context: LogContext = {
      requestId: req.headers['x-request-id'] as string || this.generateRequestId(),
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: this.getClientIp(req),
    };

    // 尝试从JWT token中提取用户ID
    if (req.user) {
      context.userId = (req.user as any).id || (req.user as any).sub;
    }

    if (res) {
      context.statusCode = res.statusCode;
    }

    return context;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(req: Request): string {
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 清理SQL查询中的敏感信息
   */
  private sanitizeQuery(query: string): string {
    // 移除密码等敏感字段的值
    return query
      .replace(/(password\s*=\s*)'[^']*'/gi, "$1'***'")
      .replace(/(password\s*=\s*)"[^"]*"/gi, '$1"***"')
      .replace(/(token\s*=\s*)'[^']*'/gi, "$1'***'")
      .replace(/(token\s*=\s*)"[^"]*"/gi, '$1"***"');
  }

  /**
   * 获取原始winston logger实例
   */
  getWinstonLogger(): winston.Logger {
    return this.winstonLogger;
  }

  /**
   * 批量记录日志
   */
  async logBatch(logs: Array<{ level: LogLevel; message: string; context?: LogContext }>): Promise<void> {
    for (const log of logs) {
      switch (log.level) {
        case 'error':
          this.error(log.message, undefined, log.context);
          break;
        case 'warn':
          this.warn(log.message, log.context);
          break;
        case 'log':
          this.info(log.message, log.context);
          break;
        case 'debug':
          this.debug(log.message, log.context);
          break;
        case 'verbose':
          this.verbose(log.message, log.context);
          break;
      }
    }
  }

  /**
   * 设置日志级别
   */
  setLogLevel(level: string): void {
    this.winstonLogger.level = level;
  }

  /**
   * 检查是否启用了指定级别的日志
   */
  isLevelEnabled(level: string): boolean {
    return this.winstonLogger.isLevelEnabled(level);
  }
}