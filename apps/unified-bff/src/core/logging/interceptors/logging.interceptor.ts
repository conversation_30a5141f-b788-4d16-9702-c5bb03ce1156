import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON>ler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { LoggingService, ApiLogData } from '../logging.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  constructor(private readonly loggingService: LoggingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    // 提取请求信息
    const { method, url, headers, body: requestBody } = request;
    const userAgent = headers['user-agent'] || '';
    const ip = this.getClientIp(request);
    const requestId = headers['x-request-id'] as string || this.generateRequestId();

    // 设置请求ID到响应头
    response.setHeader('x-request-id', requestId);

    // 提取用户信息
    const userId = request.user ? (request.user as any).id || (request.user as any).sub : undefined;

    return next.handle().pipe(
      tap((responseBody) => {
        const responseTime = Date.now() - startTime;
        const { statusCode } = response;

        // 构建API日志数据
        const logData: ApiLogData = {
          method,
          url,
          statusCode,
          responseTime,
          userId,
          userAgent,
          ip,
          requestId,
          timestamp: new Date().toISOString(),
        };

        // 根据配置决定是否记录请求和响应体
        if (this.shouldLogRequestBody(method, url)) {
          logData.requestBody = this.sanitizeRequestBody(requestBody);
        }

        if (this.shouldLogResponseBody(method, url, statusCode)) {
          logData.responseBody = this.sanitizeResponseBody(responseBody);
        }

        // 记录API访问日志
        this.loggingService.logApiRequest(logData);

        // 记录性能指标
        if (responseTime > 1000) { // 超过1秒的请求记录为慢请求
          this.loggingService.logPerformance(`API_SLOW_REQUEST`, responseTime, {
            method,
            url,
            statusCode,
            userId,
          });
        }

        // 记录业务事件
        this.logBusinessEvents(method, url, statusCode, userId);
      }),
      catchError((error) => {
        const responseTime = Date.now() - startTime;
        const statusCode = error.status || 500;

        // 构建错误日志数据
        const logData: ApiLogData = {
          method,
          url,
          statusCode,
          responseTime,
          userId,
          userAgent,
          ip,
          requestId,
          error: {
            message: error.message,
            stack: error.stack,
            code: error.code,
          },
          timestamp: new Date().toISOString(),
        };

        if (this.shouldLogRequestBody(method, url)) {
          logData.requestBody = this.sanitizeRequestBody(requestBody);
        }

        // 记录错误日志
        this.loggingService.logApiRequest(logData);
        this.loggingService.error(`API Error: ${method} ${url}`, error, {
          requestId,
          userId,
          statusCode,
          responseTime,
        });

        throw error;
      })
    );
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * 判断是否应该记录请求体
   */
  private shouldLogRequestBody(method: string, url: string): boolean {
    // 不记录GET请求的body
    if (method === 'GET') {
      return false;
    }

    // 不记录敏感接口的请求体
    const sensitiveEndpoints = [
      '/auth/login',
      '/auth/register',
      '/auth/reset-password',
      '/users/password',
    ];

    return !sensitiveEndpoints.some(endpoint => url.includes(endpoint));
  }

  /**
   * 判断是否应该记录响应体
   */
  private shouldLogResponseBody(method: string, url: string, statusCode: number): boolean {
    // 只记录错误响应
    if (statusCode >= 400) {
      return true;
    }

    // 不记录文件上传下载的响应体
    if (url.includes('/upload') || url.includes('/download')) {
      return false;
    }

    // 不记录大数据量接口的响应体
    if (url.includes('/export') || url.includes('/report')) {
      return false;
    }

    return false;
  }

  /**
   * 清理请求体中的敏感信息
   */
  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'credential'];

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    });

    return sanitized;
  }

  /**
   * 清理响应体中的敏感信息
   */
  private sanitizeResponseBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'passwordHash', 'token', 'refreshToken', 'secret'];

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    });

    // 如果是数组，递归处理每个元素
    if (Array.isArray(sanitized)) {
      return sanitized.map(item => this.sanitizeResponseBody(item));
    }

    // 如果有嵌套对象，递归处理
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeResponseBody(sanitized[key]);
      }
    });

    return sanitized;
  }

  /**
   * 记录业务事件
   */
  private logBusinessEvents(method: string, url: string, statusCode: number, userId?: string): void {
    // 用户相关事件
    if (url.includes('/auth/login') && statusCode === 200) {
      this.loggingService.logBusinessEvent('USER_LOGIN', { userId, method, url });
    }

    if (url.includes('/auth/logout') && statusCode === 200) {
      this.loggingService.logBusinessEvent('USER_LOGOUT', { userId, method, url });
    }

    if (url.includes('/users') && method === 'POST' && statusCode === 201) {
      this.loggingService.logBusinessEvent('USER_CREATED', { userId, method, url });
    }

    if (url.includes('/users') && method === 'DELETE' && statusCode === 200) {
      this.loggingService.logBusinessEvent('USER_DELETED', { userId, method, url });
    }

    // 文档相关事件
    if (url.includes('/documents') && method === 'POST' && statusCode === 201) {
      this.loggingService.logBusinessEvent('DOCUMENT_CREATED', { userId, method, url });
    }

    if (url.includes('/documents') && method === 'DELETE' && statusCode === 200) {
      this.loggingService.logBusinessEvent('DOCUMENT_DELETED', { userId, method, url });
    }

    // 安全事件
    if (statusCode === 401) {
      this.loggingService.logSecurityEvent('UNAUTHORIZED_ACCESS', { method, url, userId });
    }

    if (statusCode === 403) {
      this.loggingService.logSecurityEvent('FORBIDDEN_ACCESS', { method, url, userId });
    }

    if (statusCode === 429) {
      this.loggingService.logSecurityEvent('RATE_LIMIT_EXCEEDED', { method, url, userId });
    }
  }
}