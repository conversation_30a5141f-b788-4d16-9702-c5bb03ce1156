import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/shared/entities/user.entity';
import { CacheService } from '@/core/cache/cache.service';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private jwtService: JwtService,
    private cacheService: CacheService,
  ) {}

  // 验证用户凭据
  async validateUser(usernameOrEmail: string, password: string): Promise<User | null> {
    const user = await this.usersRepository.findOne({
      where: [
        { username: usernameOrEmail },
        { email: usernameOrEmail },
      ],
    });

    if (user && await user.validatePassword(password)) {
      user.updateLastLogin();
      await this.usersRepository.save(user);
      return user;
    }

    return null;
  }

  // 生成JWT令牌
  async generateTokens(user: User): Promise<{ accessToken: string; refreshToken: string }> {
    const payload = user.toJwtPayload();
    
    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    // 缓存refresh token
    await this.cacheService.set(`refresh:${user.id}`, refreshToken, 7 * 24 * 60 * 60 * 1000);

    return {
      accessToken,
      refreshToken,
    };
  }

  // 验证JWT令牌
  async validateToken(token: string): Promise<User | null> {
    try {
      const payload = this.jwtService.verify(token);
      const user = await this.usersRepository.findOne({ where: { id: payload.sub } });
      
      if (!user || !user.isActive) {
        return null;
      }

      return user;
    } catch (error) {
      return null;
    }
  }

  // 刷新令牌
  async refreshToken(refreshToken: string): Promise<{ accessToken: string } | null> {
    try {
      const payload = this.jwtService.verify(refreshToken);
      const user = await this.usersRepository.findOne({ where: { id: payload.sub } });
      
      if (!user || !user.isActive) {
        return null;
      }

      // 验证refresh token是否有效
      const cachedToken = await this.cacheService.get(`refresh:${user.id}`);
      if (cachedToken !== refreshToken) {
        return null;
      }

      const newPayload = user.toJwtPayload();
      const accessToken = this.jwtService.sign(newPayload);

      return { accessToken };
    } catch (error) {
      return null;
    }
  }

  // 登出（将token加入黑名单）
  async logout(token: string): Promise<void> {
    try {
      const payload = this.jwtService.decode(token) as any;
      if (payload?.jti && payload?.exp) {
        await this.cacheService.addToBlacklist(payload.jti, payload.exp);
      }
      
      if (payload?.sub) {
        await this.cacheService.del(`refresh:${payload.sub}`);
      }
    } catch (error) {
      // 忽略解码错误
    }
  }
}