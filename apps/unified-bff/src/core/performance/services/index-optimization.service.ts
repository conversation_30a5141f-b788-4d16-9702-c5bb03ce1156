import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';

export interface IndexRecommendation {
  tableName: string;
  recommendedIndex: string;
  reason: string;
  estimatedImpact: 'HIGH' | 'MEDIUM' | 'LOW';
  sql: string;
}

export interface IndexAnalysis {
  indexName: string;
  tableName: string;
  columns: string[];
  size: string;
  usageCount: number;
  effectiveRatio: number;
  status: 'OPTIMAL' | 'UNDERUSED' | 'DUPLICATE' | 'REDUNDANT';
  recommendation?: string;
}

@Injectable()
export class IndexOptimizationService {
  private readonly logger = new Logger(IndexOptimizationService.name);

  constructor(
    @InjectConnection()
    private connection: Connection,
  ) {}

  /**
   * 分析当前索引使用情况
   */
  async analyzeIndexes(): Promise<IndexAnalysis[]> {
    try {
      const indexes = await this.getIndexInformation();
      const analyses: IndexAnalysis[] = [];

      for (const index of indexes) {
        const analysis: IndexAnalysis = {
          indexName: index.indexname,
          tableName: index.tablename,
          columns: this.parseIndexColumns(index.indexdef),
          size: index.size,
          usageCount: index.idx_tup_read || 0,
          effectiveRatio: this.calculateEffectiveRatio(index),
          status: this.determineIndexStatus(index),
        };

        // 添加优化建议
        analysis.recommendation = this.getIndexRecommendation(analysis);
        analyses.push(analysis);
      }

      return analyses;
    } catch (error) {
      this.logger.error('Failed to analyze indexes', error);
      return [];
    }
  }

  /**
   * 获取索引优化建议
   */
  async getIndexRecommendations(): Promise<IndexRecommendation[]> {
    const recommendations: IndexRecommendation[] = [];

    try {
      // 分析慢查询模式
      const slowQueryPatterns = await this.analyzeSlowQueryPatterns();
      recommendations.push(...slowQueryPatterns);

      // 分析缺失的索引
      const missingIndexes = await this.analyzeMissingIndexes();
      recommendations.push(...missingIndexes);

      // 分析复合索引机会
      const compositeIndexes = await this.analyzeCompositeIndexOpportunities();
      recommendations.push(...compositeIndexes);

      return recommendations;
    } catch (error) {
      this.logger.error('Failed to get index recommendations', error);
      return [];
    }
  }

  /**
   * 创建推荐的索引
   */
  async createRecommendedIndex(recommendation: IndexRecommendation): Promise<boolean> {
    try {
      this.logger.log(`Creating index: ${recommendation.recommendedIndex}`);
      await this.connection.query(recommendation.sql);
      this.logger.log(`Successfully created index: ${recommendation.recommendedIndex}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to create index: ${recommendation.recommendedIndex}`, error);
      return false;
    }
  }

  /**
   * 删除未使用的索引
   */
  async removeUnusedIndexes(dryRun: boolean = true): Promise<string[]> {
    const unusedIndexes: string[] = [];

    try {
      const analyses = await this.analyzeIndexes();
      const candidates = analyses.filter(
        analysis => analysis.status === 'UNDERUSED' && analysis.usageCount === 0
      );

      for (const candidate of candidates) {
        // 跳过主键和唯一约束相关的索引
        if (this.isSystemIndex(candidate.indexName)) {
          continue;
        }

        const dropSql = `DROP INDEX IF EXISTS "${candidate.indexName}"`;
        unusedIndexes.push(dropSql);

        if (!dryRun) {
          try {
            await this.connection.query(dropSql);
            this.logger.log(`Dropped unused index: ${candidate.indexName}`);
          } catch (error) {
            this.logger.error(`Failed to drop index: ${candidate.indexName}`, error);
          }
        }
      }

      if (dryRun) {
        this.logger.log(`Found ${unusedIndexes.length} unused indexes (dry run)`);
      }

      return unusedIndexes;
    } catch (error) {
      this.logger.error('Failed to remove unused indexes', error);
      return [];
    }
  }

  /**
   * 重建索引统计信息
   */
  async rebuildIndexStatistics(tableName?: string): Promise<void> {
    try {
      if (tableName) {
        await this.connection.query(`ANALYZE "${tableName}"`);
        this.logger.log(`Rebuilt statistics for table: ${tableName}`);
      } else {
        await this.connection.query('ANALYZE');
        this.logger.log('Rebuilt statistics for all tables');
      }
    } catch (error) {
      this.logger.error('Failed to rebuild index statistics', error);
      throw error;
    }
  }

  /**
   * 获取索引维护建议
   */
  async getMaintenanceRecommendations(): Promise<string[]> {
    const recommendations: string[] = [];

    try {
      // 检查索引膨胀
      const bloatedIndexes = await this.findBloatedIndexes();
      if (bloatedIndexes.length > 0) {
        recommendations.push(`发现 ${bloatedIndexes.length} 个膨胀的索引，建议重建`);
      }

      // 检查重复索引
      const duplicateIndexes = await this.findDuplicateIndexes();
      if (duplicateIndexes.length > 0) {
        recommendations.push(`发现 ${duplicateIndexes.length} 组重复索引，建议删除多余的索引`);
      }

      // 检查未使用的索引
      const analyses = await this.analyzeIndexes();
      const unusedCount = analyses.filter(a => a.status === 'UNDERUSED').length;
      if (unusedCount > 0) {
        recommendations.push(`发现 ${unusedCount} 个低使用率索引，建议评估是否需要删除`);
      }

      return recommendations;
    } catch (error) {
      this.logger.error('Failed to get maintenance recommendations', error);
      return [];
    }
  }

  /**
   * 私有方法：获取索引信息
   */
  private async getIndexInformation(): Promise<any[]> {
    return this.connection.query(`
      SELECT 
        i.schemaname,
        i.tablename,
        i.indexname,
        i.indexdef,
        pg_size_pretty(pg_relation_size(indexrelid)) as size,
        s.idx_tup_read,
        s.idx_tup_fetch,
        t.n_tup_ins + t.n_tup_upd + t.n_tup_del as table_writes
      FROM pg_indexes i
      LEFT JOIN pg_stat_user_indexes s ON i.indexname = s.indexname 
        AND i.schemaname = s.schemaname 
        AND i.tablename = s.tablename
      LEFT JOIN pg_stat_user_tables t ON i.tablename = t.relname 
        AND i.schemaname = t.schemaname
      WHERE i.schemaname = 'public'
      ORDER BY i.tablename, i.indexname
    `);
  }

  /**
   * 私有方法：解析索引列
   */
  private parseIndexColumns(indexDef: string): string[] {
    const match = indexDef.match(/\(([^)]+)\)/);
    if (!match) return [];
    
    return match[1]
      .split(',')
      .map(col => col.trim().replace(/"/g, ''))
      .filter(col => col.length > 0);
  }

  /**
   * 私有方法：计算索引有效比率
   */
  private calculateEffectiveRatio(index: any): number {
    const reads = index.idx_tup_read || 0;
    const fetches = index.idx_tup_fetch || 0;
    const total = reads + fetches;
    
    return total > 0 ? reads / total : 0;
  }

  /**
   * 私有方法：确定索引状态
   */
  private determineIndexStatus(index: any): IndexAnalysis['status'] {
    const usageCount = index.idx_tup_read || 0;
    const effectiveRatio = this.calculateEffectiveRatio(index);

    if (usageCount === 0) {
      return 'UNDERUSED';
    } else if (effectiveRatio < 0.1) {
      return 'UNDERUSED';
    } else if (effectiveRatio > 0.8) {
      return 'OPTIMAL';
    } else {
      return 'OPTIMAL';
    }
  }

  /**
   * 私有方法：获取索引建议
   */
  private getIndexRecommendation(analysis: IndexAnalysis): string {
    switch (analysis.status) {
      case 'UNDERUSED':
        return '此索引使用率低，考虑删除以节省空间和维护成本';
      case 'OPTIMAL':
        return '索引使用良好';
      case 'DUPLICATE':
        return '此索引与其他索引重复，考虑删除';
      case 'REDUNDANT':
        return '此索引可能是冗余的，检查是否被其他复合索引覆盖';
      default:
        return '无特别建议';
    }
  }

  /**
   * 私有方法：分析慢查询模式
   */
  private async analyzeSlowQueryPatterns(): Promise<IndexRecommendation[]> {
    // 这里可以集成慢查询日志分析
    // 暂时返回一些常见的索引建议
    return [
      {
        tableName: 'users',
        recommendedIndex: 'idx_users_last_login_status',
        reason: '优化活跃用户查询，经常按最后登录时间和状态筛选',
        estimatedImpact: 'HIGH',
        sql: 'CREATE INDEX CONCURRENTLY idx_users_last_login_status ON users (last_login_at, status) WHERE status = \'ACTIVE\'',
      },
      {
        tableName: 'documents',
        recommendedIndex: 'idx_documents_created_by_status',
        reason: '优化按创建者和状态的文档查询',
        estimatedImpact: 'MEDIUM',
        sql: 'CREATE INDEX CONCURRENTLY idx_documents_created_by_status ON documents (created_by, status)',
      },
    ];
  }

  /**
   * 私有方法：分析缺失的索引
   */
  private async analyzeMissingIndexes(): Promise<IndexRecommendation[]> {
    const recommendations: IndexRecommendation[] = [];

    // 检查常见的查询模式
    const commonPatterns = [
      {
        table: 'analytics_events',
        columns: ['event_type', 'created_at'],
        reason: '优化按事件类型和时间范围的分析查询',
      },
      {
        table: 'documents',
        columns: ['access_level', 'status'],
        reason: '优化权限和状态过滤查询',
      },
    ];

    for (const pattern of commonPatterns) {
      const indexName = `idx_${pattern.table}_${pattern.columns.join('_')}`;
      const exists = await this.checkIndexExists(indexName);
      
      if (!exists) {
        recommendations.push({
          tableName: pattern.table,
          recommendedIndex: indexName,
          reason: pattern.reason,
          estimatedImpact: 'MEDIUM',
          sql: `CREATE INDEX CONCURRENTLY ${indexName} ON ${pattern.table} (${pattern.columns.join(', ')})`,
        });
      }
    }

    return recommendations;
  }

  /**
   * 私有方法：分析复合索引机会
   */
  private async analyzeCompositeIndexOpportunities(): Promise<IndexRecommendation[]> {
    // 分析查询模式，寻找可以合并的单列索引
    return [];
  }

  /**
   * 私有方法：检查索引是否存在
   */
  private async checkIndexExists(indexName: string): Promise<boolean> {
    const result = await this.connection.query(
      'SELECT 1 FROM pg_indexes WHERE indexname = $1',
      [indexName]
    );
    return result.length > 0;
  }

  /**
   * 私有方法：查找膨胀的索引
   */
  private async findBloatedIndexes(): Promise<string[]> {
    try {
      const result = await this.connection.query(`
        SELECT indexname
        FROM pg_stat_user_indexes 
        WHERE idx_tup_read > 0 
          AND (idx_tup_read + idx_tup_fetch) > 0
          AND idx_tup_read::float / (idx_tup_read + idx_tup_fetch) < 0.1
      `);
      
      return result.map((row: any) => row.indexname);
    } catch (error) {
      this.logger.error('Failed to find bloated indexes', error);
      return [];
    }
  }

  /**
   * 私有方法：查找重复索引
   */
  private async findDuplicateIndexes(): Promise<string[][]> {
    // 简化实现，实际需要更复杂的重复检测逻辑
    return [];
  }

  /**
   * 私有方法：检查是否为系统索引
   */
  private isSystemIndex(indexName: string): boolean {
    const systemPatterns = [
      '_pkey',
      '_key',
      'pk_',
      'unique_',
      'uq_',
    ];
    
    return systemPatterns.some(pattern => indexName.includes(pattern));
  }
}