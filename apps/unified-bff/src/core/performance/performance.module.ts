import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueryOptimizationService } from './services/query-optimization.service';
import { DatabaseMetricsService } from './services/database-metrics.service';
import { IndexOptimizationService } from './services/index-optimization.service';
import { SlowQueryInterceptor } from './interceptors/slow-query.interceptor';
import { User } from '@/shared/entities/user.entity';
import { Document } from '@/shared/entities/document.entity';
import { AnalyticsEvent, AnalyticsDailyStats } from '@/shared/entities/analytics.entity';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([User, Document, AnalyticsEvent, AnalyticsDailyStats]),
  ],
  providers: [
    QueryOptimizationService,
    DatabaseMetricsService,
    IndexOptimizationService,
    SlowQueryInterceptor,
  ],
  exports: [
    QueryOptimizationService,
    DatabaseMetricsService,
    IndexOptimizationService,
    SlowQueryInterceptor,
  ],
})
export class PerformanceModule {}