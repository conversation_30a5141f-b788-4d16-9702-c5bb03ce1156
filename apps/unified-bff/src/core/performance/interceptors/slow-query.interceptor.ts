import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { DatabaseMetricsService } from '../services/database-metrics.service';

@Injectable()
export class SlowQueryInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SlowQueryInterceptor.name);

  constructor(
    private readonly metricsService: DatabaseMetricsService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;

    return next.handle().pipe(
      tap({
        next: () => {
          const executionTime = Date.now() - startTime;
          
          // 记录慢接口（超过2秒）
          if (executionTime > 2000) {
            this.logger.warn(`Slow Endpoint Detected`, {
              method,
              url,
              executionTime: `${executionTime}ms`,
              timestamp: new Date().toISOString(),
            });
          }

          // 为数据库相关的接口记录性能指标
          if (this.isDatabaseRelatedEndpoint(url)) {
            this.metricsService.recordQueryExecution(
              `${method} ${url}`,
              executionTime
            );
          }
        },
        error: (error) => {
          const executionTime = Date.now() - startTime;
          this.logger.error(`Endpoint Error`, {
            method,
            url,
            executionTime: `${executionTime}ms`,
            error: error.message,
            timestamp: new Date().toISOString(),
          });
        },
      }),
    );
  }

  /**
   * 判断是否为数据库相关的接口
   */
  private isDatabaseRelatedEndpoint(url: string): boolean {
    const databaseEndpoints = [
      '/admin/users',
      '/admin/documents',
      '/analytics',
      '/auth',
    ];

    return databaseEndpoints.some(endpoint => url.includes(endpoint));
  }
}