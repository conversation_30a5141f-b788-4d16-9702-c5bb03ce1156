import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AIService } from './services/ai.service';
import { DocumentEmbeddingService } from './services/document-embedding.service';
import { QAService } from './services/qa.service';
import { SummaryService } from './services/summary.service';
import { SearchService } from './services/search.service';
import { VectorStoreService } from './services/vector-store.service';
import { AIController } from './controllers/ai.controller';

@Module({
  imports: [ConfigModule],
  providers: [
    AIService,
    DocumentEmbeddingService,
    QAService,
    SummaryService,
    SearchService,
    VectorStoreService,
  ],
  controllers: [AIController],
  exports: [
    AIService,
    DocumentEmbeddingService,
    QAService,
    SummaryService,
    SearchService,
    VectorStoreService,
  ],
})
export class AIModule {}