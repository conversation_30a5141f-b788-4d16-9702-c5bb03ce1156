import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsN<PERSON>ber, Min, Max } from 'class-validator';

export class CreateCompletionDto {
  @ApiProperty({
    description: '输入提示文本',
    example: '请帮我写一个关于人工智能的简介。',
  })
  @IsString()
  prompt: string;

  @ApiProperty({
    description: '系统消息（可选）',
    example: '你是一个专业的技术写作助手。',
    required: false,
  })
  @IsOptional()
  @IsString()
  systemMessage?: string;

  @ApiProperty({
    description: '生成温度，控制随机性 (0.0-2.0)',
    example: 0.7,
    minimum: 0,
    maximum: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiProperty({
    description: '最大生成令牌数',
    example: 500,
    minimum: 1,
    maximum: 4000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4000)
  maxTokens?: number;
}