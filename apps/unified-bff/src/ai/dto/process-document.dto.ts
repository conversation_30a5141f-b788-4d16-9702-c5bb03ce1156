import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject } from 'class-validator';

export class ProcessDocumentDto {
  @ApiProperty({
    description: '文档内容（如果不上传文件）',
    example: '这是一个示例文档内容...',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: '向量存储集合名称',
    example: 'company-docs',
  })
  @IsString()
  collectionName: string;

  @ApiProperty({
    description: '文档元数据',
    example: {
      title: '公司政策文档',
      category: 'policy',
      author: 'HR部门',
      tags: ['政策', '人力资源'],
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: '上传的文件',
    type: 'string',
    format: 'binary',
    required: false,
  })
  file?: any;
}