import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIService } from './ai.service';
import { DocumentEmbeddingService } from './document-embedding.service';

export interface SummaryOptions {
  maxLength?: number;
  style?: 'concise' | 'detailed' | 'bullet' | 'outline';
  focusKeywords?: string[];
  includeKeyPoints?: boolean;
  includeStatistics?: boolean;
  language?: 'zh' | 'en';
  customPrompt?: string;
}

export interface SummaryResult {
  summary: string;
  keyPoints?: string[];
  statistics?: {
    originalLength: number;
    summaryLength: number;
    compressionRatio: number;
    processingTime: number;
  };
  confidence: number;
}

export interface AnalysisResult {
  sentiment: {
    overall: 'positive' | 'negative' | 'neutral';
    score: number;
    confidence: number;
  };
  topics: Array<{
    topic: string;
    relevance: number;
    keywords: string[];
  }>;
  entities: Array<{
    entity: string;
    type: 'person' | 'organization' | 'location' | 'date' | 'other';
    frequency: number;
  }>;
  readability: {
    level: 'easy' | 'medium' | 'hard';
    score: number;
  };
  keyInsights: string[];
}

@Injectable()
export class SummaryService {
  private readonly logger = new Logger(SummaryService.name);

  constructor(
    private readonly aiService: AIService,
    private readonly embeddingService: DocumentEmbeddingService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 生成文档摘要
   */
  async generateSummary(
    content: string,
    options: SummaryOptions = {}
  ): Promise<SummaryResult> {
    const startTime = Date.now();
    const originalLength = content.length;

    try {
      this.logger.log(`Generating summary for content of length ${originalLength}`);

      // 如果内容太长，先进行分块处理
      if (content.length > 8000) {
        return await this.generateLongContentSummary(content, options);
      }

      const summary = await this.generateBasicSummary(content, options);
      const summaryLength = summary.length;
      const processingTime = Date.now() - startTime;

      let keyPoints: string[] | undefined;
      if (options.includeKeyPoints) {
        keyPoints = await this.extractKeyPoints(content, summary);
      }

      const confidence = this.calculateSummaryConfidence(content, summary);

      const result: SummaryResult = {
        summary,
        keyPoints,
        statistics: options.includeStatistics ? {
          originalLength,
          summaryLength,
          compressionRatio: summaryLength / originalLength,
          processingTime,
        } : undefined,
        confidence,
      };

      this.logger.log(`Summary generated in ${processingTime}ms`);
      return result;

    } catch (error) {
      this.logger.error('Error generating summary:', error);
      throw new Error('Failed to generate summary');
    }
  }

  /**
   * 批量摘要生成
   */
  async generateBatchSummaries(
    contents: Array<{ id: string; content: string; options?: SummaryOptions }>
  ): Promise<Array<{ id: string; result: SummaryResult }>> {
    try {
      const results = await Promise.all(
        contents.map(async ({ id, content, options }) => ({
          id,
          result: await this.generateSummary(content, options || {}),
        }))
      );

      this.logger.log(`Generated ${results.length} summaries`);
      return results;
    } catch (error) {
      this.logger.error('Error in batch summary generation:', error);
      throw new Error('Failed to generate batch summaries');
    }
  }

  /**
   * 生成摘要并进行内容分析
   */
  async summarizeAndAnalyze(
    content: string,
    options: SummaryOptions = {}
  ): Promise<{
    summary: SummaryResult;
    analysis: AnalysisResult;
  }> {
    try {
      const [summary, analysis] = await Promise.all([
        this.generateSummary(content, options),
        this.analyzeContent(content),
      ]);

      return { summary, analysis };
    } catch (error) {
      this.logger.error('Error in summarize and analyze:', error);
      throw new Error('Failed to summarize and analyze content');
    }
  }

  /**
   * 对比摘要（比较多个文档）
   */
  async generateComparativeSummary(
    documents: Array<{
      title: string;
      content: string;
    }>,
    options: SummaryOptions = {}
  ): Promise<{
    individualSummaries: Array<{ title: string; summary: string }>;
    comparativeSummary: string;
    keyDifferences: string[];
    commonThemes: string[];
  }> {
    try {
      // 1. 生成各个文档的摘要
      const individualSummaries = await Promise.all(
        documents.map(async (doc) => ({
          title: doc.title,
          summary: (await this.generateSummary(doc.content, {
            ...options,
            maxLength: 200,
          })).summary,
        }))
      );

      // 2. 生成对比摘要
      const allSummaries = individualSummaries
        .map(s => `${s.title}: ${s.summary}`)
        .join('\n\n');

      const comparativeSummary = await this.generateComparativeAnalysis(allSummaries);

      // 3. 提取关键差异和共同主题
      const [keyDifferences, commonThemes] = await Promise.all([
        this.extractKeyDifferences(allSummaries),
        this.extractCommonThemes(allSummaries),
      ]);

      return {
        individualSummaries,
        comparativeSummary,
        keyDifferences,
        commonThemes,
      };

    } catch (error) {
      this.logger.error('Error generating comparative summary:', error);
      throw new Error('Failed to generate comparative summary');
    }
  }

  /**
   * 生成基础摘要
   */
  private async generateBasicSummary(
    content: string,
    options: SummaryOptions
  ): Promise<string> {
    const {
      maxLength = 200,
      style = 'concise',
      focusKeywords = [],
      language = 'zh',
      customPrompt,
    } = options;

    if (customPrompt) {
      return await this.aiService.generateFromTemplate(
        customPrompt,
        { content },
        { temperature: 0.3, maxTokens: maxLength * 2 }
      );
    }

    const styleInstructions = this.getStyleInstructions(style, language);
    const keywordFocus = focusKeywords.length > 0
      ? `请特别关注以下关键词：${focusKeywords.join(', ')}`
      : '';

    const systemMessage = `你是一个专业的文档摘要生成器。${styleInstructions}${keywordFocus}`;

    const template = `请为以下内容生成一个${maxLength}字左右的摘要：

内容：
{content}

摘要：`;

    return await this.aiService.generateFromTemplate(
      template,
      { content },
      {
        systemMessage,
        temperature: 0.3,
        maxTokens: maxLength * 2,
      }
    );
  }

  /**
   * 处理长内容摘要
   */
  private async generateLongContentSummary(
    content: string,
    options: SummaryOptions
  ): Promise<SummaryResult> {
    const startTime = Date.now();

    try {
      // 1. 将长内容分块
      const chunks = await this.embeddingService.processDocumentContent(content);

      // 2. 为每个块生成摘要
      const chunkSummaries = await Promise.all(
        chunks.map(chunk =>
          this.generateBasicSummary(chunk.content, {
            ...options,
            maxLength: 100,
          })
        )
      );

      // 3. 合并块摘要
      const combinedSummary = chunkSummaries.join(' ');

      // 4. 生成最终摘要
      const finalSummary = await this.generateBasicSummary(combinedSummary, options);

      const processingTime = Date.now() - startTime;
      const confidence = this.calculateSummaryConfidence(content, finalSummary);

      return {
        summary: finalSummary,
        statistics: options.includeStatistics ? {
          originalLength: content.length,
          summaryLength: finalSummary.length,
          compressionRatio: finalSummary.length / content.length,
          processingTime,
        } : undefined,
        confidence,
      };

    } catch (error) {
      this.logger.error('Error generating long content summary:', error);
      throw new Error('Failed to generate long content summary');
    }
  }

  /**
   * 提取关键点
   */
  private async extractKeyPoints(content: string, summary: string): Promise<string[]> {
    const template = `基于以下内容和摘要，提取3-5个最重要的关键点：

原始内容：
{content}

摘要：
{summary}

请以JSON数组格式返回关键点，例如：["关键点1", "关键点2", "关键点3"]

关键点：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { content, summary },
        { temperature: 0.3, maxTokens: 200 }
      );

      const keyPoints = JSON.parse(response);
      return Array.isArray(keyPoints) ? keyPoints : [];
    } catch (error) {
      this.logger.warn('Failed to parse key points');
      return [];
    }
  }

  /**
   * 分析内容
   */
  private async analyzeContent(content: string): Promise<AnalysisResult> {
    try {
      const [sentiment, topics, entities, readability, insights] = await Promise.all([
        this.analyzeSentiment(content),
        this.extractTopics(content),
        this.extractEntities(content),
        this.analyzeReadability(content),
        this.extractKeyInsights(content),
      ]);

      return {
        sentiment,
        topics,
        entities,
        readability,
        keyInsights: insights,
      };
    } catch (error) {
      this.logger.error('Error analyzing content:', error);
      throw new Error('Failed to analyze content');
    }
  }

  /**
   * 情感分析
   */
  private async analyzeSentiment(content: string): Promise<{
    overall: 'positive' | 'negative' | 'neutral';
    score: number;
    confidence: number;
  }> {
    const template = `请分析以下内容的情感倾向，并以JSON格式返回结果：

内容：
{content}

请返回以下格式：
{
  "overall": "positive|negative|neutral",
  "score": 0.0到1.0之间的数值,
  "confidence": 0.0到1.0之间的置信度
}

分析结果：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { content },
        { temperature: 0.1, maxTokens: 100 }
      );

      return JSON.parse(response);
    } catch (error) {
      return { overall: 'neutral', score: 0.5, confidence: 0.5 };
    }
  }

  /**
   * 提取主题
   */
  private async extractTopics(content: string): Promise<Array<{
    topic: string;
    relevance: number;
    keywords: string[];
  }>> {
    const template = `请从以下内容中提取3-5个主要主题，并以JSON格式返回：

内容：
{content}

请返回以下格式的数组：
[
  {
    "topic": "主题名称",
    "relevance": 0.0到1.0之间的相关性分数,
    "keywords": ["关键词1", "关键词2"]
  }
]

主题：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { content },
        { temperature: 0.3, maxTokens: 300 }
      );

      const topics = JSON.parse(response);
      return Array.isArray(topics) ? topics : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 提取实体
   */
  private async extractEntities(content: string): Promise<Array<{
    entity: string;
    type: 'person' | 'organization' | 'location' | 'date' | 'other';
    frequency: number;
  }>> {
    // 这里可以集成更专业的NER模型，现在使用简化版本
    const template = `请从以下内容中提取人名、组织名、地名、日期等实体，并以JSON格式返回：

内容：
{content}

请返回以下格式的数组（最多10个实体）：
[
  {
    "entity": "实体名称",
    "type": "person|organization|location|date|other",
    "frequency": 出现次数
  }
]

实体：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { content },
        { temperature: 0.1, maxTokens: 400 }
      );

      const entities = JSON.parse(response);
      return Array.isArray(entities) ? entities : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 可读性分析
   */
  private async analyzeReadability(content: string): Promise<{
    level: 'easy' | 'medium' | 'hard';
    score: number;
  }> {
    // 简化的可读性分析
    const sentences = content.split(/[.!?]/).length;
    const words = content.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;

    let level: 'easy' | 'medium' | 'hard';
    let score: number;

    if (avgWordsPerSentence < 15) {
      level = 'easy';
      score = 0.8;
    } else if (avgWordsPerSentence < 25) {
      level = 'medium';
      score = 0.6;
    } else {
      level = 'hard';
      score = 0.4;
    }

    return { level, score };
  }

  /**
   * 提取关键洞察
   */
  private async extractKeyInsights(content: string): Promise<string[]> {
    const template = `请从以下内容中提取3-5个关键洞察或要点：

内容：
{content}

请以JSON数组格式返回洞察，例如：["洞察1", "洞察2", "洞察3"]

关键洞察：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { content },
        { temperature: 0.3, maxTokens: 300 }
      );

      const insights = JSON.parse(response);
      return Array.isArray(insights) ? insights : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 生成对比分析
   */
  private async generateComparativeAnalysis(summaries: string): Promise<string> {
    const template = `请基于以下各个文档的摘要，生成一个对比分析报告：

各文档摘要：
{summaries}

对比分析：`;

    return await this.aiService.generateFromTemplate(
      template,
      { summaries },
      { temperature: 0.3, maxTokens: 400 }
    );
  }

  /**
   * 提取关键差异
   */
  private async extractKeyDifferences(summaries: string): Promise<string[]> {
    const template = `请从以下摘要中提取关键差异点：

摘要：
{summaries}

请以JSON数组格式返回差异点，例如：["差异1", "差异2", "差异3"]

关键差异：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { summaries },
        { temperature: 0.3, maxTokens: 200 }
      );

      const differences = JSON.parse(response);
      return Array.isArray(differences) ? differences : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 提取共同主题
   */
  private async extractCommonThemes(summaries: string): Promise<string[]> {
    const template = `请从以下摘要中提取共同主题：

摘要：
{summaries}

请以JSON数组格式返回共同主题，例如：["主题1", "主题2", "主题3"]

共同主题：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { summaries },
        { temperature: 0.3, maxTokens: 200 }
      );

      const themes = JSON.parse(response);
      return Array.isArray(themes) ? themes : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 获取风格指令
   */
  private getStyleInstructions(style: string, language: string): string {
    const instructions = {
      zh: {
        concise: '请生成简洁明了的摘要，突出核心要点。',
        detailed: '请生成详细的摘要，包含重要细节和背景信息。',
        bullet: '请以要点形式生成摘要，使用项目符号列表。',
        outline: '请以大纲形式生成摘要，包含层次结构。',
      },
      en: {
        concise: 'Generate a concise summary highlighting key points.',
        detailed: 'Generate a detailed summary including important details and context.',
        bullet: 'Generate a summary in bullet point format.',
        outline: 'Generate a summary in outline format with hierarchical structure.',
      },
    };

    return instructions[language]?.[style] || instructions.zh.concise;
  }

  /**
   * 计算摘要置信度
   */
  private calculateSummaryConfidence(originalContent: string, summary: string): number {
    const originalLength = originalContent.length;
    const summaryLength = summary.length;
    const compressionRatio = summaryLength / originalLength;

    // 基于压缩比的基础置信度
    let confidence = 0.8;

    // 压缩比调整
    if (compressionRatio < 0.1) {
      confidence *= 0.9; // 压缩过度
    } else if (compressionRatio > 0.5) {
      confidence *= 0.8; // 压缩不足
    }

    // 摘要质量调整
    if (summary.includes('无法') || summary.includes('不确定')) {
      confidence *= 0.5;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }
}