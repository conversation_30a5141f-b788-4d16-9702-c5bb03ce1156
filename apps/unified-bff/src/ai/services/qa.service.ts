import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIService } from './ai.service';
import { DocumentEmbeddingService } from './document-embedding.service';
import { VectorStoreService, SearchResult } from './vector-store.service';

export interface QAOptions {
  collectionName: string;
  topK?: number;
  minScore?: number;
  includeContext?: boolean;
  contextWindow?: number;
  temperature?: number;
  maxTokens?: number;
}

export interface QAResult {
  answer: string;
  sources: Array<{
    id: string;
    content: string;
    metadata: Record<string, any>;
    score: number;
  }>;
  confidence: number;
  processing_time: number;
}

@Injectable()
export class QAService {
  private readonly logger = new Logger(QAService.name);

  constructor(
    private readonly aiService: AIService,
    private readonly embeddingService: DocumentEmbeddingService,
    private readonly vectorStoreService: VectorStoreService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 对文档进行问答
   */
  async askQuestion(question: string, options: QAOptions): Promise<QAResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Processing question: "${question}"`);

      // 1. 生成问题的嵌入向量
      const queryEmbedding = await this.embeddingService.generateQueryEmbedding(question);

      // 2. 搜索相关文档块
      const searchResults = await this.vectorStoreService.similaritySearch(
        options.collectionName,
        queryEmbedding,
        {
          topK: options.topK || 5,
          minScore: options.minScore || 0.5,
        }
      );

      if (searchResults.length === 0) {
        return {
          answer: '抱歉，我在文档中没有找到与您问题相关的信息。',
          sources: [],
          confidence: 0,
          processing_time: Date.now() - startTime,
        };
      }

      // 3. 构建上下文
      const context = this.buildContext(searchResults, options.contextWindow || 2000);

      // 4. 生成答案
      const answer = await this.generateAnswer(question, context, {
        temperature: options.temperature,
        maxTokens: options.maxTokens,
      });

      // 5. 计算置信度
      const confidence = this.calculateConfidence(searchResults, answer);

      const result: QAResult = {
        answer,
        sources: searchResults.map(result => ({
          id: result.chunk.id,
          content: result.chunk.content,
          metadata: result.chunk.metadata,
          score: result.score,
        })),
        confidence,
        processing_time: Date.now() - startTime,
      };

      this.logger.log(`Question answered in ${result.processing_time}ms with confidence ${confidence}`);
      return result;

    } catch (error) {
      this.logger.error('Error in askQuestion:', error);
      throw new Error('Failed to process question');
    }
  }

  /**
   * 批量问答
   */
  async askQuestions(questions: string[], options: QAOptions): Promise<QAResult[]> {
    try {
      const results = await Promise.all(
        questions.map(question => this.askQuestion(question, options))
      );

      this.logger.log(`Processed ${questions.length} questions`);
      return results;
    } catch (error) {
      this.logger.error('Error in batch question processing:', error);
      throw new Error('Failed to process batch questions');
    }
  }

  /**
   * 对话式问答（保持上下文）
   */
  async conversationalQA(
    question: string,
    conversationHistory: Array<{ role: 'user' | 'assistant'; content: string }>,
    options: QAOptions
  ): Promise<QAResult> {
    const startTime = Date.now();

    try {
      // 1. 结合历史对话重新构造问题
      const contextualQuestion = await this.rewriteQuestionWithHistory(question, conversationHistory);

      // 2. 进行常规问答
      const result = await this.askQuestion(contextualQuestion, options);

      // 3. 调整答案以适应对话上下文
      const conversationalAnswer = await this.adaptAnswerForConversation(
        result.answer,
        question,
        conversationHistory
      );

      return {
        ...result,
        answer: conversationalAnswer,
        processing_time: Date.now() - startTime,
      };

    } catch (error) {
      this.logger.error('Error in conversational QA:', error);
      throw new Error('Failed to process conversational question');
    }
  }

  /**
   * 多步骤推理问答
   */
  async complexReasoning(question: string, options: QAOptions): Promise<QAResult> {
    const startTime = Date.now();

    try {
      // 1. 分解复杂问题
      const subQuestions = await this.decomposeQuestion(question);

      // 2. 逐步回答子问题
      const subAnswers: QAResult[] = [];
      for (const subQuestion of subQuestions) {
        const subAnswer = await this.askQuestion(subQuestion, {
          ...options,
          topK: 3, // 每个子问题使用较少的文档
        });
        subAnswers.push(subAnswer);
      }

      // 3. 综合子答案生成最终答案
      const finalAnswer = await this.synthesizeAnswers(question, subAnswers);

      // 4. 合并所有来源
      const allSources = subAnswers.flatMap(answer => answer.sources);
      const uniqueSources = this.deduplicateSources(allSources);

      // 5. 计算总体置信度
      const avgConfidence = subAnswers.reduce((sum, answer) => sum + answer.confidence, 0) / subAnswers.length;

      return {
        answer: finalAnswer,
        sources: uniqueSources,
        confidence: avgConfidence,
        processing_time: Date.now() - startTime,
      };

    } catch (error) {
      this.logger.error('Error in complex reasoning:', error);
      throw new Error('Failed to process complex reasoning question');
    }
  }

  /**
   * 构建上下文
   */
  private buildContext(searchResults: SearchResult[], maxLength: number): string {
    let context = '';
    let currentLength = 0;

    for (const result of searchResults) {
      const chunk = `[来源: ${result.chunk.metadata.source || 'Unknown'}]\n${result.chunk.content}\n\n`;
      
      if (currentLength + chunk.length <= maxLength) {
        context += chunk;
        currentLength += chunk.length;
      } else {
        // 如果添加当前块会超出长度限制，截取部分内容
        const remainingLength = maxLength - currentLength;
        if (remainingLength > 100) { // 至少保留100个字符
          context += chunk.substring(0, remainingLength - 3) + '...';
        }
        break;
      }
    }

    return context;
  }

  /**
   * 生成答案
   */
  private async generateAnswer(
    question: string,
    context: string,
    options?: { temperature?: number; maxTokens?: number }
  ): Promise<string> {
    const systemMessage = `你是一个专业的文档问答助手。请根据提供的文档上下文来回答用户的问题。

请遵循以下指导原则：
1. 只基于提供的上下文信息回答问题
2. 如果上下文中没有足够信息回答问题，请诚实说明
3. 提供准确、简洁、有用的答案
4. 如果适当，可以引用具体的来源
5. 保持客观和中立的语调

上下文信息：
${context}`;

    const template = `请根据上下文信息回答以下问题：

问题：{question}

答案：`;

    return await this.aiService.generateFromTemplate(
      template,
      { question },
      {
        systemMessage,
        temperature: options?.temperature || 0.3,
        maxTokens: options?.maxTokens || 500,
      }
    );
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(searchResults: SearchResult[], answer: string): number {
    if (searchResults.length === 0) return 0;

    // 基于搜索结果分数的置信度
    const avgScore = searchResults.reduce((sum, result) => sum + result.score, 0) / searchResults.length;

    // 基于答案长度和质量的调整
    let qualityFactor = 1;
    if (answer.includes('没有找到') || answer.includes('无法确定') || answer.includes('不确定')) {
      qualityFactor = 0.3;
    } else if (answer.length < 50) {
      qualityFactor = 0.7;
    }

    return Math.min(avgScore * qualityFactor, 1);
  }

  /**
   * 结合历史对话重新构造问题
   */
  private async rewriteQuestionWithHistory(
    question: string,
    history: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<string> {
    if (history.length === 0) return question;

    const conversationContext = history
      .slice(-4) // 只取最近的4轮对话
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    const template = `基于以下对话历史，请重新表述用户的最新问题，使其包含必要的上下文信息，以便进行独立的文档搜索。

对话历史：
{conversation_context}

用户的最新问题：{question}

重新表述的问题：`;

    return await this.aiService.generateFromTemplate(
      template,
      {
        conversation_context: conversationContext,
        question,
      },
      {
        temperature: 0.3,
        maxTokens: 200,
      }
    );
  }

  /**
   * 调整答案以适应对话上下文
   */
  private async adaptAnswerForConversation(
    answer: string,
    originalQuestion: string,
    history: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<string> {
    if (history.length === 0) return answer;

    const recentHistory = history.slice(-2).map(msg => `${msg.role}: ${msg.content}`).join('\n');

    const template = `请将以下答案调整为适合对话上下文的回复：

最近对话：
{recent_history}

用户问题：{question}
原始答案：{answer}

调整后的答案：`;

    return await this.aiService.generateFromTemplate(
      template,
      {
        recent_history: recentHistory,
        question: originalQuestion,
        answer,
      },
      {
        temperature: 0.3,
        maxTokens: 300,
      }
    );
  }

  /**
   * 分解复杂问题
   */
  private async decomposeQuestion(question: string): Promise<string[]> {
    const template = `请将以下复杂问题分解为2-4个简单的子问题，每个子问题都可以独立回答：

复杂问题：{question}

请以JSON数组格式返回子问题，例如：["子问题1", "子问题2", "子问题3"]

子问题：`;

    const response = await this.aiService.generateFromTemplate(
      template,
      { question },
      {
        temperature: 0.3,
        maxTokens: 300,
      }
    );

    try {
      const subQuestions = JSON.parse(response);
      return Array.isArray(subQuestions) ? subQuestions : [question];
    } catch (error) {
      this.logger.warn('Failed to parse sub-questions, using original question');
      return [question];
    }
  }

  /**
   * 综合子答案
   */
  private async synthesizeAnswers(originalQuestion: string, subAnswers: QAResult[]): Promise<string> {
    const subAnswersText = subAnswers
      .map((answer, index) => `子问题${index + 1}的答案：${answer.answer}`)
      .join('\n\n');

    const template = `请基于以下子问题的答案，为原始问题提供一个综合的、连贯的答案：

原始问题：{question}

{sub_answers}

综合答案：`;

    return await this.aiService.generateFromTemplate(
      template,
      {
        question: originalQuestion,
        sub_answers: subAnswersText,
      },
      {
        temperature: 0.3,
        maxTokens: 600,
      }
    );
  }

  /**
   * 去重来源
   */
  private deduplicateSources(sources: Array<{
    id: string;
    content: string;
    metadata: Record<string, any>;
    score: number;
  }>): Array<{
    id: string;
    content: string;
    metadata: Record<string, any>;
    score: number;
  }> {
    const seen = new Set<string>();
    return sources.filter(source => {
      if (seen.has(source.id)) {
        return false;
      }
      seen.add(source.id);
      return true;
    });
  }
}