import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatOpenAI } from '@langchain/openai';
import { ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate } from '@langchain/core/prompts';
import { AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages';

@Injectable()
export class AIService {
  private readonly logger = new Logger(AIService.name);
  private readonly chatModel: ChatOpenAI;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      this.logger.warn('OpenAI API key not found. AI features will be disabled.');
    }

    this.chatModel = new ChatOpenAI({
      openAIApiKey: apiKey,
      modelName: this.configService.get<string>('OPENAI_MODEL', 'gpt-3.5-turbo'),
      temperature: this.configService.get<number>('OPENAI_TEMPERATURE', 0.7),
      maxTokens: this.configService.get<number>('OPENAI_MAX_TOKENS', 2000),
      verbose: this.configService.get<string>('NODE_ENV') === 'development',
    });
  }

  /**
   * 生成文本补全
   */
  async generateCompletion(prompt: string, options?: {
    temperature?: number;
    maxTokens?: number;
    systemMessage?: string;
  }): Promise<string> {
    try {
      const messages = [];
      
      if (options?.systemMessage) {
        messages.push(new SystemMessage(options.systemMessage));
      }
      
      messages.push(new HumanMessage(prompt));

      const response = await this.chatModel.invoke(messages);

      return response.content.toString();
    } catch (error) {
      this.logger.error('Error generating completion:', error);
      throw new Error('Failed to generate AI completion');
    }
  }

  /**
   * 生成对话回复
   */
  async generateChatResponse(
    messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
    options?: {
      temperature?: number;
      maxTokens?: number;
    }
  ): Promise<string> {
    try {
      const langchainMessages = messages.map(msg => {
        switch (msg.role) {
          case 'system':
            return new SystemMessage(msg.content);
          case 'user':
            return new HumanMessage(msg.content);
          case 'assistant':
            return new AIMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      const response = await this.chatModel.invoke(langchainMessages);

      return response.content.toString();
    } catch (error) {
      this.logger.error('Error generating chat response:', error);
      throw new Error('Failed to generate chat response');
    }
  }

  /**
   * 使用模板生成回复
   */
  async generateFromTemplate(
    templateStr: string,
    variables: Record<string, any>,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemMessage?: string;
    }
  ): Promise<string> {
    try {
      const promptTemplate = ChatPromptTemplate.fromMessages([
        ...(options?.systemMessage 
          ? [SystemMessagePromptTemplate.fromTemplate(options.systemMessage)] 
          : []),
        HumanMessagePromptTemplate.fromTemplate(templateStr),
      ]);

      const prompt = await promptTemplate.formatMessages(variables);
      
      const response = await this.chatModel.invoke(prompt);

      return response.content.toString();
    } catch (error) {
      this.logger.error('Error generating from template:', error);
      throw new Error('Failed to generate from template');
    }
  }

  /**
   * 批量生成
   */
  async generateBatch(
    prompts: string[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemMessage?: string;
    }
  ): Promise<string[]> {
    try {
      const batchPromises = prompts.map(prompt => 
        this.generateCompletion(prompt, options)
      );

      return await Promise.all(batchPromises);
    } catch (error) {
      this.logger.error('Error in batch generation:', error);
      throw new Error('Failed to generate batch completions');
    }
  }

  /**
   * 流式生成（用于实时响应）
   */
  async *generateStream(
    prompt: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemMessage?: string;
    }
  ): AsyncGenerator<string, void, unknown> {
    try {
      const messages = [];
      
      if (options?.systemMessage) {
        messages.push(new SystemMessage(options.systemMessage));
      }
      
      messages.push(new HumanMessage(prompt));

      const stream = await this.chatModel.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          yield chunk.content.toString();
        }
      }
    } catch (error) {
      this.logger.error('Error in stream generation:', error);
      throw new Error('Failed to generate stream');
    }
  }

  /**
   * 检查AI服务是否可用
   */
  async isHealthy(): Promise<boolean> {
    try {
      const response = await this.generateCompletion('Hello', {
        maxTokens: 10,
        temperature: 0,
      });
      return !!response;
    } catch (error) {
      this.logger.error('AI service health check failed:', error);
      return false;
    }
  }

  /**
   * 获取模型信息
   */
  getModelInfo(): {
    model: string;
    provider: string;
    temperature: number;
    maxTokens: number;
  } {
    return {
      model: this.configService.get<string>('OPENAI_MODEL', 'gpt-3.5-turbo'),
      provider: 'OpenAI',
      temperature: this.configService.get<number>('OPENAI_TEMPERATURE', 0.7),
      maxTokens: this.configService.get<number>('OPENAI_MAX_TOKENS', 2000),
    };
  }
}