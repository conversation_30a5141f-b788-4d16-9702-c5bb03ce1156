import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { Document, DocumentType, DocumentStatus, AccessLevel } from '@/shared/entities/document.entity';
import { CreateDocumentDto } from '../dto/create-document.dto';
import { UpdateDocumentDto } from '../dto/update-document.dto';
import { DocumentQueryDto, DocumentStatsQueryDto } from '../dto/document-query.dto';
import { DocumentStatsDto } from '../dto/document-response.dto';
import { PaginatedResponseDto, PaginationMetaDto } from '@/shared/dto/pagination.dto';
import { CacheService } from '@/core/cache/cache.service';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Document)
    private documentsRepository: Repository<Document>,
    private cacheService: CacheService,
  ) {}

  // 创建文档
  async create(createDocumentDto: CreateDocumentDto, createdBy: string): Promise<Document> {
    const document = this.documentsRepository.create({
      ...createDocumentDto,
      createdBy,
      updatedBy: createdBy,
    });

    const savedDocument = await this.documentsRepository.save(document);
    await this.invalidateDocumentCaches();
    
    return savedDocument;
  }

  // 获取文档列表（分页）
  async findAll(query: DocumentQueryDto, currentUserId?: string, userRole?: string): Promise<PaginatedResponseDto<Document>> {
    const {
      page = 1,
      pageSize = 20,
      search,
      type,
      status,
      accessLevel,
      createdBy,
      tags,
      extension,
      minSize,
      maxSize,
      createdFrom,
      createdTo,
      updatedFrom,
      updatedTo,
      isSearchable,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    // 尝试从缓存获取
    const cacheKey = `documents:list:${JSON.stringify(query)}:${currentUserId}:${userRole}`;
    const cached = await this.cacheService.get<PaginatedResponseDto<Document>>(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.documentsRepository.createQueryBuilder('document')
      .leftJoinAndSelect('document.creator', 'creator');

    // 应用权限过滤
    this.applyAccessFilters(queryBuilder, currentUserId, userRole);

    // 应用搜索和过滤条件
    this.applyFilters(queryBuilder, {
      search,
      type,
      status,
      accessLevel,
      createdBy,
      tags,
      extension,
      minSize,
      maxSize,
      createdFrom,
      createdTo,
      updatedFrom,
      updatedTo,
      isSearchable,
    });

    // 排序
    queryBuilder.orderBy(`document.${sortBy}`, sortOrder);

    // 分页
    const skip = (page - 1) * pageSize;
    queryBuilder.skip(skip).take(pageSize);

    // 执行查询
    const [documents, total] = await queryBuilder.getManyAndCount();

    // 转换为安全对象
    const safeDocuments = documents.map(doc => doc.toSafeObject() as Document);

    const meta = new PaginationMetaDto(page, pageSize, total);
    const result = new PaginatedResponseDto(safeDocuments, meta);

    // 缓存结果（5分钟）
    await this.cacheService.set(cacheKey, result, 5 * 60 * 1000);

    return result;
  }

  // 根据ID获取文档
  async findOne(id: string, currentUserId?: string, userRole?: string): Promise<Document> {
    const document = await this.documentsRepository.findOne({
      where: { id },
      relations: ['creator', 'updater'],
    });

    if (!document) {
      throw new NotFoundException(`文档 ${id} 不存在`);
    }

    // 检查访问权限
    const isOwner = document.createdBy === currentUserId;
    if (!document.canAccess(userRole, isOwner)) {
      throw new ForbiddenException('没有权限访问此文档');
    }

    return document;
  }

  // 更新文档
  async update(
    id: string,
    updateDocumentDto: UpdateDocumentDto,
    currentUserId: string,
    userRole: string,
  ): Promise<Document> {
    const document = await this.findOne(id, currentUserId, userRole);

    // 检查编辑权限
    const isOwner = document.createdBy === currentUserId;
    if (!document.canEdit(userRole, isOwner)) {
      throw new ForbiddenException('没有权限编辑此文档');
    }

    // 更新字段
    Object.assign(document, updateDocumentDto);
    document.updatedBy = currentUserId;

    const updatedDocument = await this.documentsRepository.save(document);
    await this.invalidateDocumentCaches();

    return updatedDocument;
  }

  // 删除文档
  async remove(id: string, currentUserId: string, userRole: string): Promise<void> {
    const document = await this.findOne(id, currentUserId, userRole);

    // 检查删除权限
    const isOwner = document.createdBy === currentUserId;
    if (!document.canDelete(userRole, isOwner)) {
      throw new ForbiddenException('没有权限删除此文档');
    }

    await this.documentsRepository.remove(document);
    await this.invalidateDocumentCaches();
  }

  // 软删除文档
  async softDelete(id: string, currentUserId: string, userRole: string): Promise<Document> {
    const document = await this.findOne(id, currentUserId, userRole);

    const isOwner = document.createdBy === currentUserId;
    if (!document.canDelete(userRole, isOwner)) {
      throw new ForbiddenException('没有权限删除此文档');
    }

    document.status = DocumentStatus.DELETED;
    document.updatedBy = currentUserId;

    const updatedDocument = await this.documentsRepository.save(document);
    await this.invalidateDocumentCaches();

    return updatedDocument;
  }

  // 增加浏览次数
  async incrementViewCount(id: string): Promise<void> {
    await this.documentsRepository.increment({ id }, 'viewCount', 1);
    await this.invalidateDocumentCaches();
  }

  // 增加下载次数
  async incrementDownloadCount(id: string): Promise<void> {
    await this.documentsRepository.increment({ id }, 'downloadCount', 1);
    await this.invalidateDocumentCaches();
  }

  // 批量更新状态
  async batchUpdateStatus(
    ids: string[],
    status: DocumentStatus,
    currentUserId: string,
    userRole: string,
  ): Promise<void> {
    // 检查权限
    const documents = await this.documentsRepository.find({
      where: { id: In(ids) },
    });

    for (const document of documents) {
      const isOwner = document.createdBy === currentUserId;
      if (!document.canEdit(userRole, isOwner)) {
        throw new ForbiddenException(`没有权限修改文档 ${document.title}`);
      }
    }

    await this.documentsRepository.update(ids, {
      status,
      updatedBy: currentUserId,
    });

    await this.invalidateDocumentCaches();
  }

  // 批量删除
  async batchRemove(ids: string[], currentUserId: string, userRole: string): Promise<void> {
    const documents = await this.documentsRepository.find({
      where: { id: In(ids) },
    });

    for (const document of documents) {
      const isOwner = document.createdBy === currentUserId;
      if (!document.canDelete(userRole, isOwner)) {
        throw new ForbiddenException(`没有权限删除文档 ${document.title}`);
      }
    }

    await this.documentsRepository.delete(ids);
    await this.invalidateDocumentCaches();
  }

  // 获取文档统计信息
  async getStats(query: DocumentStatsQueryDto): Promise<DocumentStatsDto> {
    const { days = 30, groupBy = 'type' } = query;

    const cacheKey = `documents:stats:${days}:${groupBy}`;
    let stats = await this.cacheService.get<DocumentStatsDto>(cacheKey);

    if (!stats) {
      const queryBuilder = this.documentsRepository.createQueryBuilder('document');
      const dateThreshold = new Date();
      dateThreshold.setDate(dateThreshold.getDate() - days);

      const [
        totalDocuments,
        publishedDocuments,
        draftDocuments,
        newDocuments,
        totalFileSize,
        avgFileSize,
        totalViews,
        totalDownloads,
      ] = await Promise.all([
        // 总文档数
        queryBuilder.getCount(),

        // 已发布文档数
        queryBuilder.clone().where('document.status = :status', { status: DocumentStatus.PUBLISHED }).getCount(),

        // 草稿文档数  
        queryBuilder.clone().where('document.status = :status', { status: DocumentStatus.DRAFT }).getCount(),

        // 指定天数内新文档数
        queryBuilder.clone()
          .where('document.createdAt >= :date', { date: dateThreshold })
          .getCount(),

        // 总文件大小
        queryBuilder.clone()
          .select('SUM(document.fileSize)', 'totalSize')
          .getRawOne()
          .then(result => parseInt(result.totalSize) || 0),

        // 平均文件大小
        queryBuilder.clone()
          .select('AVG(document.fileSize)', 'avgSize')
          .getRawOne()
          .then(result => parseInt(result.avgSize) || 0),

        // 总浏览次数
        queryBuilder.clone()
          .select('SUM(document.viewCount)', 'totalViews')
          .getRawOne()
          .then(result => parseInt(result.totalViews) || 0),

        // 总下载次数
        queryBuilder.clone()
          .select('SUM(document.downloadCount)', 'totalDownloads')
          .getRawOne()
          .then(result => parseInt(result.totalDownloads) || 0),
      ]);

      // 分组统计
      let groupStats: Record<string, number> = {};
      if (groupBy) {
        const groupResults = await queryBuilder.clone()
          .select(`document.${groupBy}`, 'group')
          .addSelect('COUNT(*)', 'count')
          .groupBy(`document.${groupBy}`)
          .getRawMany();

        groupStats = groupResults.reduce((acc, item) => {
          acc[item.group] = parseInt(item.count);
          return acc;
        }, {});
      }

      stats = {
        totalDocuments,
        publishedDocuments,
        draftDocuments,
        newDocuments,
        totalFileSize,
        totalFileSizeFormatted: this.formatFileSize(totalFileSize),
        avgFileSize,
        totalViews,
        totalDownloads,
        groupStats,
      };

      // 缓存1小时
      await this.cacheService.set(cacheKey, stats, 60 * 60 * 1000);
    }

    return stats;
  }

  // 搜索文档
  async search(
    searchTerm: string,
    currentUserId?: string,
    userRole?: string,
    limit: number = 10,
  ): Promise<Document[]> {
    const queryBuilder = this.documentsRepository.createQueryBuilder('document')
      .leftJoinAndSelect('document.creator', 'creator');

    // 应用权限过滤
    this.applyAccessFilters(queryBuilder, currentUserId, userRole);

    // 全文搜索
    queryBuilder
      .where(
        '(document.title ILIKE :search OR document.description ILIKE :search OR document.content ILIKE :search OR array_to_string(document.tags, \' \') ILIKE :search)',
        { search: `%${searchTerm}%` }
      )
      .andWhere('document.isSearchable = :isSearchable', { isSearchable: true })
      .orderBy('document.viewCount', 'DESC')
      .addOrderBy('document.createdAt', 'DESC')
      .limit(limit);

    return queryBuilder.getMany();
  }

  // 私有方法：应用权限过滤
  private applyAccessFilters(
    queryBuilder: SelectQueryBuilder<Document>,
    currentUserId?: string,
    userRole?: string,
  ): void {
    if (!currentUserId || !userRole) {
      // 未登录用户只能看公开文档
      queryBuilder.andWhere('document.accessLevel = :publicLevel', {
        publicLevel: AccessLevel.PUBLIC,
      });
      return;
    }

    // 根据用户角色应用权限过滤
    if (userRole === 'ADMIN') {
      // 管理员可以看所有文档
      return;
    }

    const accessConditions = [];
    
    // 公开文档
    accessConditions.push('document.accessLevel = :publicLevel');
    
    // 内部文档（登录用户可见）
    if (['MANAGER', 'USER'].includes(userRole)) {
      accessConditions.push('document.accessLevel = :internalLevel');
    }
    
    // 机密文档（管理员和经理可见，或者是创建者）
    if (userRole === 'MANAGER') {
      accessConditions.push('document.accessLevel = :confidentialLevel');
    }
    
    // 私有文档（只有创建者可见）
    accessConditions.push('(document.accessLevel = :privateLevel AND document.createdBy = :currentUserId)');
    accessConditions.push('(document.accessLevel = :confidentialLevel AND document.createdBy = :currentUserId)');

    queryBuilder.andWhere(`(${accessConditions.join(' OR ')})`, {
      publicLevel: AccessLevel.PUBLIC,
      internalLevel: AccessLevel.INTERNAL,
      confidentialLevel: AccessLevel.CONFIDENTIAL,
      privateLevel: AccessLevel.PRIVATE,
      currentUserId,
    });
  }

  // 私有方法：应用查询过滤条件
  private applyFilters(
    queryBuilder: SelectQueryBuilder<Document>,
    filters: {
      search?: string;
      type?: DocumentType;
      status?: DocumentStatus;
      accessLevel?: AccessLevel;
      createdBy?: string;
      tags?: string;
      extension?: string;
      minSize?: number;
      maxSize?: number;
      createdFrom?: string;
      createdTo?: string;
      updatedFrom?: string;
      updatedTo?: string;
      isSearchable?: boolean;
    },
  ): void {
    const {
      search,
      type,
      status,
      accessLevel,
      createdBy,
      tags,
      extension,
      minSize,
      maxSize,
      createdFrom,
      createdTo,
      updatedFrom,
      updatedTo,
      isSearchable,
    } = filters;

    // 搜索过滤
    if (search) {
      queryBuilder.andWhere(
        '(document.title ILIKE :search OR document.description ILIKE :search OR document.fileName ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 类型过滤
    if (type) {
      queryBuilder.andWhere('document.type = :type', { type });
    }

    // 状态过滤
    if (status) {
      queryBuilder.andWhere('document.status = :status', { status });
    }

    // 访问级别过滤
    if (accessLevel) {
      queryBuilder.andWhere('document.accessLevel = :accessLevel', { accessLevel });
    }

    // 创建者过滤
    if (createdBy) {
      queryBuilder.andWhere('document.createdBy = :createdBy', { createdBy });
    }

    // 标签过滤
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      queryBuilder.andWhere('document.tags && :tags', { tags: tagArray });
    }

    // 文件扩展名过滤
    if (extension) {
      queryBuilder.andWhere('document.fileName ILIKE :extension', {
        extension: `%.${extension}`,
      });
    }

    // 文件大小过滤
    if (minSize !== undefined) {
      queryBuilder.andWhere('document.fileSize >= :minSize', { minSize });
    }
    if (maxSize !== undefined) {
      queryBuilder.andWhere('document.fileSize <= :maxSize', { maxSize });
    }

    // 创建时间过滤
    if (createdFrom) {
      queryBuilder.andWhere('document.createdAt >= :createdFrom', { createdFrom });
    }
    if (createdTo) {
      queryBuilder.andWhere('document.createdAt <= :createdTo', { createdTo });
    }

    // 更新时间过滤
    if (updatedFrom) {
      queryBuilder.andWhere('document.updatedAt >= :updatedFrom', { updatedFrom });
    }
    if (updatedTo) {
      queryBuilder.andWhere('document.updatedAt <= :updatedTo', { updatedTo });
    }

    // 可搜索过滤
    if (typeof isSearchable === 'boolean') {
      queryBuilder.andWhere('document.isSearchable = :isSearchable', { isSearchable });
    }
  }

  // 私有方法：格式化文件大小
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(size < 10 ? 1 : 0)} ${units[unitIndex]}`;
  }

  // 私有方法：清除文档相关缓存
  private async invalidateDocumentCaches(): Promise<void> {
    // 清除统计缓存
    const statsCachePattern = 'documents:stats:*';
    // 这里可以实现更精细的缓存清除策略
    // 暂时简单清除已知的缓存键
  }
}