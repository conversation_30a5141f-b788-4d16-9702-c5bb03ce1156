import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsDateString, IsString, IsArray, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationDto } from '@/shared/dto/pagination.dto';
import { DocumentType, DocumentStatus, AccessLevel } from '@/shared/entities/document.entity';

export class DocumentQueryDto extends PaginationDto {
  @ApiPropertyOptional({
    description: '文档类型筛选',
    enum: DocumentType,
  })
  @IsOptional()
  @IsEnum(DocumentType)
  type?: DocumentType;

  @ApiPropertyOptional({
    description: '文档状态筛选',
    enum: DocumentStatus,
  })
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @ApiPropertyOptional({
    description: '访问级别筛选',
    enum: AccessLevel,
  })
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @ApiPropertyOptional({
    description: '创建者ID筛选',
  })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiPropertyOptional({
    description: '标签筛选（多个标签用逗号分隔）',
    example: 'manual,guide',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({
    description: '文件扩展名筛选',
    example: 'pdf',
  })
  @IsOptional()
  @IsString()
  extension?: string;

  @ApiPropertyOptional({
    description: '最小文件大小（字节）',
    example: 1024,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minSize?: number;

  @ApiPropertyOptional({
    description: '最大文件大小（字节）',
    example: 10485760,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxSize?: number;

  @ApiPropertyOptional({
    description: '创建开始时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  createdFrom?: string;

  @ApiPropertyOptional({
    description: '创建结束时间',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  createdTo?: string;

  @ApiPropertyOptional({
    description: '更新开始时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  updatedFrom?: string;

  @ApiPropertyOptional({
    description: '更新结束时间',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  updatedTo?: string;

  @ApiPropertyOptional({
    description: '是否可搜索',
    type: Boolean,
  })
  @IsOptional()
  isSearchable?: boolean;

  @ApiPropertyOptional({
    description: '排序字段',
    enum: ['createdAt', 'updatedAt', 'title', 'fileSize', 'viewCount', 'downloadCount'],
    default: 'createdAt',
  })
  @IsOptional()
  @IsString()
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'fileSize' | 'viewCount' | 'downloadCount';
}

export class DocumentStatsQueryDto {
  @ApiPropertyOptional({
    description: '统计时间范围（天数）',
    default: 30,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  days?: number;

  @ApiPropertyOptional({
    description: '分组方式',
    enum: ['type', 'status', 'accessLevel', 'extension'],
    default: 'type',
  })
  @IsOptional()
  @IsEnum(['type', 'status', 'accessLevel', 'extension'])
  groupBy?: 'type' | 'status' | 'accessLevel' | 'extension';
}