import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';
import { MonitoringService } from '@/core/monitoring/monitoring.service';

@ApiTags('Common')
@Controller({ path: 'health', version: '1' })
export class HealthController {
  constructor(
    private readonly healthService: HealthService,
    private readonly monitoringService: MonitoringService,
  ) {}

  @Get()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({
    status: 200,
    description: '系统健康状态',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 123.456 },
        version: { type: 'string', example: '1.0.0' },
        environment: { type: 'string', example: 'production' },
        memory: {
          type: 'object',
          properties: {
            used: { type: 'string', example: '128MB' },
            total: { type: 'string', example: '512MB' },
          },
        },
      },
    },
  })
  async getHealth() {
    return this.healthService.getBasicHealth();
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({
    status: 200,
    description: '详细系统健康状态',
  })
  async getDetailedHealth() {
    return this.healthService.getDetailedHealth();
  }

  @Get('database')
  @ApiOperation({ summary: '数据库健康检查' })
  @ApiResponse({
    status: 200,
    description: '数据库连接状态',
  })
  async getDatabaseHealth() {
    return this.healthService.getDatabaseHealth();
  }

  @Get('redis')
  @ApiOperation({ summary: 'Redis健康检查' })
  @ApiResponse({
    status: 200,
    description: 'Redis连接状态',
  })
  async getRedisHealth() {
    return this.healthService.getRedisHealth();
  }

  @Get('monitoring')
  @ApiOperation({ summary: '监控系统健康检查' })
  @ApiResponse({
    status: 200,
    description: '监控系统健康状态',
  })
  async getMonitoringHealth() {
    return this.monitoringService.getHealthStatus();
  }

  @Get('metrics')
  @ApiOperation({ summary: '获取基础指标' })
  @ApiResponse({
    status: 200,
    description: '系统基础指标',
  })
  async getBasicMetrics() {
    const systemMetrics = await this.monitoringService.getSystemMetrics();
    const appMetrics = this.monitoringService.getApplicationMetrics();

    return {
      timestamp: new Date().toISOString(),
      system: {
        uptime: systemMetrics.uptime,
        cpu: systemMetrics.cpu.usage,
        memory: systemMetrics.memory.usagePercent,
      },
      application: {
        requests: appMetrics.requests.total,
        errors: appMetrics.errors.total,
        cacheHitRate: appMetrics.cache.hitRate,
        avgResponseTime: appMetrics.requests.averageResponseTime,
      },
    };
  }
}