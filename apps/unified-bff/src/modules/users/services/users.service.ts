import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { User } from '@/shared/entities/user.entity';
import { UserRole } from '@/shared/enums/user-role.enum';
import { UserStatus } from '@/shared/enums/user-status.enum';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { UserQueryDto } from '../dto/user-query.dto';
import { UserStatsDto } from '../dto/user-response.dto';
import { PaginatedResponseDto, PaginationMetaDto } from '@/shared/dto/pagination.dto';
import { CacheService } from '@/core/cache/cache.service';
import { AdvancedCacheService } from '@/core/cache/advanced-cache.service';
import { QueryOptimizationService } from '@/core/performance/services/query-optimization.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private cacheService: CacheService,
    private advancedCacheService: AdvancedCacheService,
    private queryOptimizationService: QueryOptimizationService,
  ) {}

  // 创建用户
  async create(createUserDto: CreateUserDto): Promise<User> {
    // 检查用户名和邮箱是否已存在
    await this.checkUniqueFields(createUserDto.username, createUserDto.email);

    const user = this.usersRepository.create({
      ...createUserDto,
      passwordHash: createUserDto.password, // 将在实体的beforeInsert中自动hash
    });

    const savedUser = await this.usersRepository.save(user);
    
    // 清除相关缓存
    await this.invalidateUserCaches();
    
    return savedUser;
  }

  // 获取用户列表（分页）
  async findAll(query: UserQueryDto): Promise<PaginatedResponseDto<User>> {
    const {
      page = 1,
      pageSize = 20,
      search,
      role,
      status,
      emailVerified,
      mobileVerified,
      createdFrom,
      createdTo,
      lastLoginFrom,
      lastLoginTo,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    // 尝试从缓存获取
    const cacheKey = `users:list:${JSON.stringify(query)}`;
    const cached = await this.cacheService.get<PaginatedResponseDto<User>>(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.usersRepository.createQueryBuilder('user');

    // 应用过滤条件
    this.applyFilters(queryBuilder, {
      search,
      role,
      status,
      emailVerified,
      mobileVerified,
      createdFrom,
      createdTo,
      lastLoginFrom,
      lastLoginTo,
    });

    // 排序
    queryBuilder.orderBy(`user.${sortBy}`, sortOrder);

    // 分页
    const skip = (page - 1) * pageSize;
    queryBuilder.skip(skip).take(pageSize);

    // 使用优化后的分页查询
    const optimizedResult = await this.queryOptimizationService.optimizedPagination(
      this.usersRepository,
      page,
      pageSize,
      queryBuilder,
      { enableQueryCache: true, cacheTimeout: 300000 }
    );

    const users = optimizedResult.data;
    const total = optimizedResult.total;

    // 转换为安全对象
    const safeUsers = users.map(user => user.toSafeObject() as User);

    const meta = new PaginationMetaDto(page, pageSize, total);
    const result = new PaginatedResponseDto(safeUsers, meta);

    // 缓存结果（5分钟）
    await this.cacheService.set(cacheKey, result, 5 * 60 * 1000);

    return result;
  }

  // 根据ID获取用户
  async findOne(id: string): Promise<User> {
    // 使用智能缓存
    return this.advancedCacheService.smartCache(
      { prefix: 'users', identifier: id },
      async () => {
        const user = await this.usersRepository.findOne({ where: { id } });
        if (!user) {
          throw new NotFoundException(`用户 ${id} 不存在`);
        }
        return user;
      },
      {
        ttl: 5 * 60 * 1000, // 5分钟
        tags: ['users'],
        refreshAhead: 2 * 60 * 1000, // 在过期前2分钟开始刷新
        lockTimeout: 3000, // 3秒锁超时
      }
    );
  }

  // 根据用户名或邮箱查找用户
  async findByUsernameOrEmail(usernameOrEmail: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: [
        { username: usernameOrEmail },
        { email: usernameOrEmail },
      ],
    });
  }

  // 更新用户
  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // 检查邮箱唯一性（如果更新了邮箱）
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      await this.checkEmailUnique(updateUserDto.email, id);
    }

    // 更新字段
    Object.assign(user, updateUserDto);
    
    const updatedUser = await this.usersRepository.save(user);
    
    // 更新缓存
    await this.cacheService.setUser(id, updatedUser);
    await this.invalidateUserCaches();
    
    return updatedUser;
  }

  // 删除用户
  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    
    await this.usersRepository.remove(user);
    
    // 清除缓存
    await this.cacheService.deleteUser(id);
    await this.invalidateUserCaches();
  }

  // 更新密码
  async updatePassword(id: string, newPassword: string): Promise<void> {
    const user = await this.findOne(id);
    
    await user.setPassword(newPassword);
    await this.usersRepository.save(user);
    
    // 更新缓存
    await this.cacheService.setUser(id, user);
  }

  // 获取用户统计信息
  async getStats(): Promise<UserStatsDto> {
    // 尝试从缓存获取
    const cacheKey = 'users:stats';
    let stats = await this.cacheService.get<UserStatsDto>(cacheKey);
    
    if (!stats) {
      const queryBuilder = this.usersRepository.createQueryBuilder('user');
      
      // 使用优化后的统计查询
      const [
        totalUsers,
        activeUsers,
        adminUsers,
        managerUsers,
        regularUsers,
        newUsers30d,
        activeUsers7d,
        emailVerifiedUsers,
        mobileVerifiedUsers,
      ] = await Promise.all([
        // 总用户数 - 使用优化的查询
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone(), 
          { enableQueryCache: true }
        ).getCount(),
        
        // 活跃用户数 - 利用状态索引
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone().where('user.status = :status', { status: UserStatus.ACTIVE }),
          { enableQueryCache: true }
        ).getCount(),
        
        // 管理员用户数 - 利用角色索引
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone().where('user.role = :role', { role: UserRole.ADMIN }),
          { enableQueryCache: true }
        ).getCount(),
        
        // 经理用户数
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone().where('user.role = :role', { role: UserRole.MANAGER }),
          { enableQueryCache: true }
        ).getCount(),
        
        // 普通用户数
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone().where('user.role = :role', { role: UserRole.USER }),
          { enableQueryCache: true }
        ).getCount(),
        
        // 30天内新用户数 - 利用创建时间索引
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone()
            .where('user.createdAt >= :date', { date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }),
          { enableQueryCache: true }
        ).getCount(),
        
        // 7天内活跃用户数 - 利用复合索引 (lastLoginAt, status)
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone()
            .where('user.lastLoginAt >= :date', { date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) })
            .andWhere('user.status = :status', { status: UserStatus.ACTIVE }),
          { enableQueryCache: true }
        ).getCount(),
        
        // 邮箱验证用户数
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone().where('user.emailVerified = :verified', { verified: true }),
          { enableQueryCache: true }
        ).getCount(),
        
        // 手机验证用户数
        this.queryOptimizationService.optimizeQueryBuilder(
          queryBuilder.clone().where('user.mobileVerified = :verified', { verified: true }),
          { enableQueryCache: true }
        ).getCount(),
      ]);

      stats = {
        totalUsers,
        activeUsers,
        adminUsers,
        managerUsers,
        regularUsers,
        newUsers30d,
        activeUsers7d,
        emailVerificationRate: totalUsers > 0 ? emailVerifiedUsers / totalUsers : 0,
        mobileVerificationRate: totalUsers > 0 ? mobileVerifiedUsers / totalUsers : 0,
      };

      // 缓存1小时
      await this.cacheService.set(cacheKey, stats, 60 * 60 * 1000);
    }

    return stats;
  }

  // 批量更新用户状态
  async batchUpdateStatus(ids: string[], status: UserStatus): Promise<void> {
    await this.usersRepository.update(ids, { status });
    
    // 清除相关缓存
    await this.invalidateUserCaches();
    for (const id of ids) {
      await this.cacheService.deleteUser(id);
    }
  }

  // 批量删除用户
  async batchRemove(ids: string[]): Promise<void> {
    await this.usersRepository.delete(ids);
    
    // 清除相关缓存
    await this.invalidateUserCaches();
    for (const id of ids) {
      await this.cacheService.deleteUser(id);
    }
  }

  // 私有方法：检查唯一字段
  private async checkUniqueFields(username: string, email: string, excludeId?: string): Promise<void> {
    const conditions = [];
    
    if (username) {
      conditions.push({ username });
    }
    
    if (email) {
      conditions.push({ email });
    }

    if (conditions.length === 0) return;

    const queryBuilder = this.usersRepository.createQueryBuilder('user');
    queryBuilder.where(conditions.map((_, index) => 
      index === 0 ? '(user.username = :username OR user.email = :email)' : ''
    ).filter(Boolean).join(' OR '), { username, email });

    if (excludeId) {
      queryBuilder.andWhere('user.id != :excludeId', { excludeId });
    }

    const existingUser = await queryBuilder.getOne();
    
    if (existingUser) {
      if (existingUser.username === username) {
        throw new ConflictException('用户名已存在');
      }
      if (existingUser.email === email) {
        throw new ConflictException('邮箱已存在');
      }
    }
  }

  // 私有方法：检查邮箱唯一性
  private async checkEmailUnique(email: string, excludeId?: string): Promise<void> {
    const queryBuilder = this.usersRepository.createQueryBuilder('user');
    queryBuilder.where('user.email = :email', { email });
    
    if (excludeId) {
      queryBuilder.andWhere('user.id != :excludeId', { excludeId });
    }

    const existingUser = await queryBuilder.getOne();
    if (existingUser) {
      throw new ConflictException('邮箱已存在');
    }
  }

  // 私有方法：应用查询过滤条件
  private applyFilters(
    queryBuilder: SelectQueryBuilder<User>,
    filters: {
      search?: string;
      role?: UserRole;
      status?: UserStatus;
      emailVerified?: boolean;
      mobileVerified?: boolean;
      createdFrom?: string;
      createdTo?: string;
      lastLoginFrom?: string;
      lastLoginTo?: string;
    },
  ): void {
    const {
      search,
      role,
      status,
      emailVerified,
      mobileVerified,
      createdFrom,
      createdTo,
      lastLoginFrom,
      lastLoginTo,
    } = filters;

    // 搜索过滤
    if (search) {
      queryBuilder.andWhere(
        '(user.username ILIKE :search OR user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 角色过滤
    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    // 状态过滤
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    // 邮箱验证过滤
    if (typeof emailVerified === 'boolean') {
      queryBuilder.andWhere('user.emailVerified = :emailVerified', { emailVerified });
    }

    // 手机验证过滤
    if (typeof mobileVerified === 'boolean') {
      queryBuilder.andWhere('user.mobileVerified = :mobileVerified', { mobileVerified });
    }

    // 创建时间过滤
    if (createdFrom) {
      queryBuilder.andWhere('user.createdAt >= :createdFrom', { createdFrom });
    }
    if (createdTo) {
      queryBuilder.andWhere('user.createdAt <= :createdTo', { createdTo });
    }

    // 最后登录时间过滤
    if (lastLoginFrom) {
      queryBuilder.andWhere('user.lastLoginAt >= :lastLoginFrom', { lastLoginFrom });
    }
    if (lastLoginTo) {
      queryBuilder.andWhere('user.lastLoginAt <= :lastLoginTo', { lastLoginTo });
    }
  }

  // 私有方法：清除用户相关缓存
  private async invalidateUserCaches(): Promise<void> {
    // 这里可以实现更精细的缓存清除策略
    // 暂时清除统计缓存
    await this.cacheService.del('users:stats');
  }
}