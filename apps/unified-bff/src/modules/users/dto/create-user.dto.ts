import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsOptional,
  MinLength,
  MaxLength,
  IsEnum,
  IsPhoneNumber,
  IsUrl,
  Matches,
} from 'class-validator';
import { UserRole } from '@/shared/enums/user-role.enum';
import { UserStatus } from '@/shared/enums/user-status.enum';

export class CreateUserDto {
  @ApiProperty({
    description: '用户名',
    minLength: 3,
    maxLength: 50,
    example: 'johndoe',
  })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: '用户名只能包含字母、数字和下划线',
  })
  username: string;

  @ApiProperty({
    description: '邮箱地址',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;

  @ApiProperty({
    description: '密码',
    minLength: 8,
    example: 'SecurePass123',
  })
  @IsString()
  @MinLength(8, { message: '密码至少8位' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: '密码必须包含大小写字母和数字',
  })
  password: string;

  @ApiPropertyOptional({
    description: '名字',
    maxLength: 50,
    example: 'John',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  firstName?: string;

  @ApiPropertyOptional({
    description: '姓氏',
    maxLength: 50,
    example: 'Doe',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  lastName?: string;

  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
  })
  @IsOptional()
  @IsUrl({}, { message: '请输入有效的URL' })
  avatarUrl?: string;

  @ApiPropertyOptional({
    description: '手机号码',
    example: '+8613800138000',
  })
  @IsOptional()
  @IsPhoneNumber('CN', { message: '请输入有效的手机号码' })
  phone?: string;

  @ApiPropertyOptional({
    description: '用户角色',
    enum: UserRole,
    default: UserRole.USER,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiPropertyOptional({
    description: '用户状态',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({
    description: '用户偏好设置',
    example: { theme: 'dark', language: 'zh-CN' },
  })
  @IsOptional()
  preferences?: Record<string, any>;
}