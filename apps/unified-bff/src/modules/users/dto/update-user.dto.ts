import { ApiPropertyOptional } from '@nestjs/swagger';
import { PartialType, OmitType } from '@nestjs/mapped-types';
import { CreateUserDto } from './create-user.dto';

export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['password', 'username'] as const)
) {
  @ApiPropertyOptional({
    description: '邮箱地址',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiPropertyOptional({
    description: '名字',
    example: 'NewFirstName',
  })
  firstName?: string;

  @ApiPropertyOptional({
    description: '姓氏',
    example: 'NewLastName',
  })
  lastName?: string;

  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/new-avatar.jpg',
  })
  avatarUrl?: string;

  @ApiPropertyOptional({
    description: '手机号码',
    example: '+8613800138001',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: '用户角色',
  })
  role?: string;

  @ApiPropertyOptional({
    description: '用户状态',
  })
  status?: string;

  @ApiPropertyOptional({
    description: '用户偏好设置',
    example: { theme: 'light', language: 'en-US' },
  })
  preferences?: Record<string, any>;
}