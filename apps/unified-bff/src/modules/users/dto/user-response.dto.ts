import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserRole } from '@/shared/enums/user-role.enum';
import { UserStatus } from '@/shared/enums/user-status.enum';

export class UserResponseDto {
  @ApiProperty({ description: '用户ID' })
  id!: string;

  @ApiProperty({ description: '用户名' })
  username!: string;

  @ApiProperty({ description: '邮箱地址' })
  email!: string;

  @ApiPropertyOptional({ description: '名字' })
  firstName?: string;

  @ApiPropertyOptional({ description: '姓氏' })
  lastName?: string;

  @ApiProperty({ description: '全名' })
  fullName!: string;

  @ApiPropertyOptional({ description: '头像URL' })
  avatarUrl?: string;

  @ApiPropertyOptional({ description: '手机号码' })
  phone?: string;

  @ApiProperty({ 
    description: '用户角色',
    enum: UserRole,
  })
  role!: UserRole;

  @ApiProperty({ 
    description: '用户状态',
    enum: UserStatus,
  })
  status!: UserStatus;

  @ApiProperty({ description: '是否为活跃用户' })
  isActive!: boolean;

  @ApiProperty({ description: '是否为管理员' })
  isAdmin!: boolean;

  @ApiProperty({ description: '邮箱是否已验证' })
  emailVerified!: boolean;

  @ApiProperty({ description: '手机是否已验证' })
  mobileVerified!: boolean;

  @ApiPropertyOptional({ description: '最后登录时间' })
  lastLoginAt?: Date;

  @ApiPropertyOptional({ description: '微信OpenID' })
  wechatOpenid?: string;

  @ApiProperty({ 
    description: '用户偏好设置',
    example: { theme: 'dark', language: 'zh-CN' },
  })
  preferences!: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createdAt!: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt!: Date;
}

export class UserStatsDto {
  @ApiProperty({ description: '总用户数' })
  totalUsers!: number;

  @ApiProperty({ description: '活跃用户数' })
  activeUsers!: number;

  @ApiProperty({ description: '管理员用户数' })
  adminUsers!: number;

  @ApiProperty({ description: '经理用户数' })
  managerUsers!: number;

  @ApiProperty({ description: '普通用户数' })
  regularUsers!: number;

  @ApiProperty({ description: '30天内新用户数' })
  newUsers30d!: number;

  @ApiProperty({ description: '7天内活跃用户数' })
  activeUsers7d!: number;

  @ApiProperty({ description: '邮箱验证率' })
  emailVerificationRate!: number;

  @ApiProperty({ description: '手机验证率' })
  mobileVerificationRate!: number;
}