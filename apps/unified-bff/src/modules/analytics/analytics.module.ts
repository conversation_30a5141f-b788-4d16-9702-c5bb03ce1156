import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsService } from './services/analytics.service';
import { AdminAnalyticsController } from './controllers/admin-analytics.controller';
import { 
  AnalyticsEvent, 
  AnalyticsDailyStats 
} from '@/shared/entities/analytics.entity';
import { CacheModule } from '@/core/cache/cache.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AnalyticsEvent,
      AnalyticsDailyStats,
    ]),
    CacheModule,
  ],
  controllers: [
    AdminAnalyticsController,
  ],
  providers: [
    AnalyticsService,
  ],
  exports: [
    AnalyticsService,
  ],
})
export class AnalyticsModule {}