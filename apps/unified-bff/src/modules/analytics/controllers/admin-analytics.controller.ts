import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse,
} from '@nestjs/swagger';
import { AnalyticsService } from '../services/analytics.service';
import { 
  AnalyticsQueryDto, 
  DashboardQueryDto 
} from '../dto/analytics-query.dto';
import { 
  DashboardStatsDto,
  EventStatsDto,
  AnalyticsEventDto,
  CreateEventDto,
} from '../dto/analytics-response.dto';
import { BaseResponseDto } from '@/shared/dto/base-response.dto';

@ApiTags('Admin Analytics')
@Controller('analytics')
export class AdminAnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Post('events')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建分析事件' })
  @ApiResponse({ 
    status: 201, 
    description: '事件创建成功',
    type: BaseResponseDto,
  })
  async trackEvent(
    @Body() createEventDto: CreateEventDto,
    @Req() request: any,
  ): Promise<BaseResponseDto<AnalyticsEventDto>> {
    const ipAddress = request.ip;
    const userAgent = request.get('User-Agent');
    
    const event = await this.analyticsService.trackEvent(
      createEventDto,
      ipAddress,
      userAgent,
    );

    return {
      success: true,
      message: '事件创建成功',
      data: {
        id: event.id,
        eventType: event.eventType,
        userId: event.userId,
        sessionId: event.sessionId,
        ipAddress: event.ipAddress,
        metadata: event.metadata,
        createdAt: event.createdAt,
      },
    };
  }

  @Get('dashboard')
  @ApiOperation({ summary: '获取仪表盘统计数据' })
  @ApiResponse({ 
    status: 200, 
    description: '仪表盘数据获取成功',
    type: DashboardStatsDto,
  })
  async getDashboardStats(
    @Query() query: DashboardQueryDto,
  ): Promise<BaseResponseDto<DashboardStatsDto>> {
    const stats = await this.analyticsService.getDashboardStats(query);

    return {
      success: true,
      message: '仪表盘数据获取成功',
      data: stats,
    };
  }

  @Get('events/stats')
  @ApiOperation({ summary: '获取事件统计数据' })
  @ApiResponse({ 
    status: 200, 
    description: '事件统计数据获取成功',
    type: [EventStatsDto],
  })
  async getEventStats(
    @Query() query: AnalyticsQueryDto,
  ): Promise<BaseResponseDto<EventStatsDto[]>> {
    const stats = await this.analyticsService.getEventStats(query);

    return {
      success: true,
      message: '事件统计数据获取成功',
      data: stats,
    };
  }

  @Get('user-activity-trend')
  @ApiOperation({ summary: '获取用户活动趋势' })
  @ApiResponse({ 
    status: 200, 
    description: '用户活动趋势数据获取成功',
  })
  async getUserActivityTrend(
    @Query() query: DashboardQueryDto,
  ): Promise<BaseResponseDto<any[]>> {
    const { days = 30 } = query;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 调用服务中的私有方法需要通过 getDashboardStats 获取
    const dashboardStats = await this.analyticsService.getDashboardStats(query);

    return {
      success: true,
      message: '用户活动趋势数据获取成功',
      data: dashboardStats.userActivityTrend,
    };
  }

  @Get('content-engagement')
  @ApiOperation({ summary: '获取内容参与度数据' })
  @ApiResponse({ 
    status: 200, 
    description: '内容参与度数据获取成功',
  })
  async getContentEngagement(
    @Query() query: DashboardQueryDto,
  ): Promise<BaseResponseDto<any[]>> {
    const dashboardStats = await this.analyticsService.getDashboardStats(query);

    return {
      success: true,
      message: '内容参与度数据获取成功',
      data: dashboardStats.contentEngagement,
    };
  }

  @Get('system-usage')
  @ApiOperation({ summary: '获取系统使用情况' })
  @ApiResponse({ 
    status: 200, 
    description: '系统使用情况数据获取成功',
  })
  async getSystemUsage(
    @Query() query: DashboardQueryDto,
  ): Promise<BaseResponseDto<any[]>> {
    const dashboardStats = await this.analyticsService.getDashboardStats(query);

    return {
      success: true,
      message: '系统使用情况数据获取成功',
      data: dashboardStats.systemUsage,
    };
  }

  @Get('top-content')
  @ApiOperation({ summary: '获取热门内容' })
  @ApiResponse({ 
    status: 200, 
    description: '热门内容数据获取成功',
  })
  async getTopContent(
    @Query() query: DashboardQueryDto,
  ): Promise<BaseResponseDto<any[]>> {
    const dashboardStats = await this.analyticsService.getDashboardStats(query);

    return {
      success: true,
      message: '热门内容数据获取成功',
      data: dashboardStats.topContent,
    };
  }

  @Get('performance-metrics')
  @ApiOperation({ summary: '获取性能指标' })
  @ApiResponse({ 
    status: 200, 
    description: '性能指标获取成功',
  })
  async getPerformanceMetrics(
    @Query() query: DashboardQueryDto,
  ): Promise<BaseResponseDto<any>> {
    const dashboardStats = await this.analyticsService.getDashboardStats(query);

    const performanceMetrics = {
      totalPageViews: dashboardStats.totalPageViews,
      uniqueVisitors: dashboardStats.uniqueVisitors,
      avgPageViewsPerVisitor: dashboardStats.uniqueVisitors > 0 
        ? dashboardStats.totalPageViews / dashboardStats.uniqueVisitors 
        : 0,
      userEngagementRate: dashboardStats.totalUsers > 0 
        ? (dashboardStats.activeUsers / dashboardStats.totalUsers) * 100 
        : 0,
      contentUtilizationRate: dashboardStats.totalDocuments > 0
        ? (dashboardStats.topContent.length / dashboardStats.totalDocuments) * 100
        : 0,
      systemGrowthRate: {
        users: dashboardStats.newUsers,
        documents: dashboardStats.newDocuments,
        pageViews: dashboardStats.totalPageViews,
      },
    };

    return {
      success: true,
      message: '性能指标获取成功',
      data: performanceMetrics,
    };
  }
}