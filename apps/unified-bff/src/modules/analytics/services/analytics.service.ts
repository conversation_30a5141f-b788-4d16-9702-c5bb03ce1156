import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { 
  AnalyticsEvent, 
  AnalyticsDailyStats, 
  AnalyticsEventType 
} from '@/shared/entities/analytics.entity';
import { 
  DashboardStatsDto, 
  UserActivityTrend, 
  ContentEngagement, 
  SystemUsage, 
  TopContent,
  EventStatsDto,
  CreateEventDto,
} from '../dto/analytics-response.dto';
import { AnalyticsQueryDto, DashboardQueryDto } from '../dto/analytics-query.dto';
import { CacheService } from '@/core/cache/cache.service';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(AnalyticsEvent)
    private analyticsEventRepository: Repository<AnalyticsEvent>,
    @InjectRepository(AnalyticsDailyStats)
    private dailyStatsRepository: Repository<AnalyticsDailyStats>,
    private cacheService: CacheService,
  ) {}

  // 记录分析事件
  async trackEvent(
    eventDto: CreateEventDto,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AnalyticsEvent> {
    const event = this.analyticsEventRepository.create({
      ...eventDto,
      ipAddress,
      userAgent,
    });

    const savedEvent = await this.analyticsEventRepository.save(event);
    
    // 异步更新每日统计
    this.updateDailyStats(eventDto.eventType, new Date()).catch(console.error);
    
    return savedEvent;
  }

  // 获取仪表盘统计数据
  async getDashboardStats(query: DashboardQueryDto): Promise<DashboardStatsDto> {
    const { days = 30 } = query;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 尝试从缓存获取
    const cacheKey = `dashboard:stats:${days}`;
    let stats = null;
    
    try {
      stats = await this.cacheService.get<DashboardStatsDto>(cacheKey);
    } catch (error) {
      console.warn('Cache get error:', error.message);
      // 缓存错误不影响业务逻辑
    }

    if (!stats) {
      const [
        userActivityTrend,
        contentEngagement,
        systemUsage,
        topContent,
        totalUsers,
        activeUsers,
        newUsers,
        totalDocuments,
        newDocuments,
        totalPageViews,
        uniqueVisitors,
      ] = await Promise.all([
        this.getUserActivityTrend(startDate, endDate),
        this.getContentEngagement(startDate, endDate),
        this.getSystemUsage(startDate, endDate),
        this.getTopContent(days),
        this.getTotalUsers(),
        this.getActiveUsers(days),
        this.getNewUsers(days),
        this.getTotalDocuments(),
        this.getNewDocuments(days),
        this.getTotalPageViews(days),
        this.getUniqueVisitors(days),
      ]);

      stats = {
        totalUsers,
        activeUsers,
        newUsers,
        totalDocuments,
        newDocuments,
        totalPageViews,
        uniqueVisitors,
        userActivityTrend,
        contentEngagement,
        systemUsage,
        topContent,
        timeRange: {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          days,
        },
      };

      // 缓存30分钟
      try {
        await this.cacheService.set(cacheKey, stats, 30 * 60 * 1000);
      } catch (error) {
        console.warn('Cache set error:', error.message);
        // 缓存错误不影响业务逻辑
      }
    }

    return stats;
  }

  // 获取事件统计
  async getEventStats(query: AnalyticsQueryDto): Promise<EventStatsDto[]> {
    const { 
      startDate, 
      endDate, 
      days = 30, 
      eventType, 
      groupBy = 'day' 
    } = query;

    let queryStartDate: Date;
    let queryEndDate: Date;

    if (startDate && endDate) {
      queryStartDate = new Date(startDate);
      queryEndDate = new Date(endDate);
    } else {
      queryEndDate = new Date();
      queryStartDate = new Date();
      queryStartDate.setDate(queryStartDate.getDate() - days);
    }

    const cacheKey = `event:stats:${eventType || 'all'}:${queryStartDate.toISOString()}:${queryEndDate.toISOString()}:${groupBy}`;
    let stats = null;
    
    try {
      stats = await this.cacheService.get<EventStatsDto[]>(cacheKey);
    } catch (error) {
      console.warn('Cache get error:', error.message);
      // 缓存错误不影响业务逻辑
    }

    if (!stats) {
      const queryBuilder = this.analyticsEventRepository.createQueryBuilder('event')
        .where('event.createdAt >= :startDate', { startDate: queryStartDate })
        .andWhere('event.createdAt <= :endDate', { endDate: queryEndDate });

      if (eventType) {
        queryBuilder.andWhere('event.eventType = :eventType', { eventType });
      }

      // 获取事件类型统计
      const eventTypeStats = await queryBuilder
        .select('event.eventType', 'eventType')
        .addSelect('COUNT(*)', 'totalEvents')
        .addSelect('COUNT(DISTINCT event.userId)', 'uniqueUsers')
        .groupBy('event.eventType')
        .getRawMany();

      stats = await Promise.all(
        eventTypeStats.map(async (stat) => {
          // 获取时间趋势
          const timeTrend = await this.getEventTimeTrend(
            stat.eventType,
            queryStartDate,
            queryEndDate,
            groupBy,
          );

          return {
            eventType: stat.eventType,
            totalEvents: parseInt(stat.totalEvents),
            uniqueUsers: parseInt(stat.uniqueUsers),
            avgEventsPerUser: parseInt(stat.uniqueUsers) > 0 
              ? parseInt(stat.totalEvents) / parseInt(stat.uniqueUsers) 
              : 0,
            timeTrend,
          };
        })
      );

      // 缓存15分钟
      try {
        await this.cacheService.set(cacheKey, stats, 15 * 60 * 1000);
      } catch (error) {
        console.warn('Cache set error:', error.message);
        // 缓存错误不影响业务逻辑
      }
    }

    return stats;
  }

  // 私有方法：获取用户活动趋势
  private async getUserActivityTrend(startDate: Date, endDate: Date): Promise<UserActivityTrend[]> {
    const stats = await this.dailyStatsRepository
      .createQueryBuilder('stats')
      .where('stats.date >= :startDate', { startDate })
      .andWhere('stats.date <= :endDate', { endDate })
      .orderBy('stats.date', 'ASC')
      .getMany();

    return stats.map(stat => ({
      date: stat.date.toISOString().split('T')[0],
      activeUsers: stat.activeUsers,
      newUsers: stat.newUsers,
      totalSessions: stat.uniqueVisitors, // 临时使用uniqueVisitors作为sessions
    }));
  }

  // 私有方法：获取内容参与度
  private async getContentEngagement(startDate: Date, endDate: Date): Promise<ContentEngagement[]> {
    const stats = await this.dailyStatsRepository
      .createQueryBuilder('stats')
      .where('stats.date >= :startDate', { startDate })
      .andWhere('stats.date <= :endDate', { endDate })
      .orderBy('stats.date', 'ASC')
      .getMany();

    return stats.map(stat => ({
      date: stat.date.toISOString().split('T')[0],
      pageViews: stat.totalPageViews,
      documentViews: stat.documentViews,
      documentDownloads: stat.documentDownloads,
      searchQueries: stat.searchQueries,
    }));
  }

  // 私有方法：获取系统使用情况
  private async getSystemUsage(startDate: Date, endDate: Date): Promise<SystemUsage[]> {
    const stats = await this.dailyStatsRepository
      .createQueryBuilder('stats')
      .where('stats.date >= :startDate', { startDate })
      .andWhere('stats.date <= :endDate', { endDate })
      .orderBy('stats.date', 'ASC')
      .getMany();

    return stats.map(stat => ({
      date: stat.date.toISOString().split('T')[0],
      uniqueVisitors: stat.uniqueVisitors,
      totalDocuments: stat.totalDocuments,
      newDocuments: stat.newDocuments,
      errorCount: stat.errorCount,
    }));
  }

  // 私有方法：获取热门内容
  private async getTopContent(days: number): Promise<TopContent[]> {
    // 这里需要与文档服务集成，暂时返回模拟数据
    return [
      {
        contentId: '1',
        title: '系统使用手册',
        views: 150,
        downloads: 45,
        lastAccessed: new Date(),
      },
      {
        contentId: '2',
        title: '项目需求文档',
        views: 120,
        downloads: 30,
        lastAccessed: new Date(),
      },
    ];
  }

  // 私有方法：获取事件时间趋势
  private async getEventTimeTrend(
    eventType: AnalyticsEventType,
    startDate: Date,
    endDate: Date,
    groupBy: string,
  ): Promise<Array<{ date: string; count: number }>> {
    let dateFormat: string;
    switch (groupBy) {
      case 'week':
        dateFormat = 'YYYY-"W"WW';
        break;
      case 'month':
        dateFormat = 'YYYY-MM';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
    }

    const results = await this.analyticsEventRepository
      .createQueryBuilder('event')
      .select(`TO_CHAR(event.createdAt, '${dateFormat}')`, 'date')
      .addSelect('COUNT(*)', 'count')
      .where('event.eventType = :eventType', { eventType })
      .andWhere('event.createdAt >= :startDate', { startDate })
      .andWhere('event.createdAt <= :endDate', { endDate })
      .groupBy(`TO_CHAR(event.createdAt, '${dateFormat}')`)
      .orderBy(`TO_CHAR(event.createdAt, '${dateFormat}')`, 'ASC')
      .getRawMany();

    return results.map(result => ({
      date: result.date,
      count: parseInt(result.count),
    }));
  }

  // 私有方法：更新每日统计
  private async updateDailyStats(eventType: AnalyticsEventType, date: Date): Promise<void> {
    const dateStr = date.toISOString().split('T')[0];
    const statsDate = new Date(dateStr);

    let dailyStats = await this.dailyStatsRepository.findOne({
      where: { date: statsDate },
    });

    if (!dailyStats) {
      dailyStats = this.dailyStatsRepository.create({ date: statsDate });
    }

    // 根据事件类型更新相应统计
    switch (eventType) {
      case AnalyticsEventType.PAGE_VIEW:
        dailyStats.incrementMetric('totalPageViews');
        break;
      case AnalyticsEventType.DOCUMENT_VIEW:
        dailyStats.incrementMetric('documentViews');
        break;
      case AnalyticsEventType.DOCUMENT_DOWNLOAD:
        dailyStats.incrementMetric('documentDownloads');
        break;
      case AnalyticsEventType.SEARCH:
        dailyStats.incrementMetric('searchQueries');
        break;
      case AnalyticsEventType.ERROR:
        dailyStats.incrementMetric('errorCount');
        break;
    }

    await this.dailyStatsRepository.save(dailyStats);
  }

  // 简化的统计方法（实际应该查询真实数据）
  private async getTotalUsers(): Promise<number> {
    // 这里应该查询用户表
    return 150;
  }

  private async getActiveUsers(days: number): Promise<number> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const result = await this.analyticsEventRepository
      .createQueryBuilder('event')
      .select('COUNT(DISTINCT event.userId)', 'count')
      .where('event.createdAt >= :startDate', { startDate })
      .andWhere('event.userId IS NOT NULL')
      .getRawOne();

    return parseInt(result.count) || 0;
  }

  private async getNewUsers(days: number): Promise<number> {
    // 这里应该查询用户表的创建时间
    return 25;
  }

  private async getTotalDocuments(): Promise<number> {
    // 这里应该查询文档表
    return 450;
  }

  private async getNewDocuments(days: number): Promise<number> {
    // 这里应该查询文档表的创建时间
    return 15;
  }

  private async getTotalPageViews(days: number): Promise<number> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const result = await this.analyticsEventRepository
      .createQueryBuilder('event')
      .select('COUNT(*)', 'count')
      .where('event.eventType = :eventType', { eventType: AnalyticsEventType.PAGE_VIEW })
      .andWhere('event.createdAt >= :startDate', { startDate })
      .getRawOne();

    return parseInt(result.count) || 0;
  }

  private async getUniqueVisitors(days: number): Promise<number> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const result = await this.analyticsEventRepository
      .createQueryBuilder('event')
      .select('COUNT(DISTINCT event.sessionId)', 'count')
      .where('event.createdAt >= :startDate', { startDate })
      .andWhere('event.sessionId IS NOT NULL')
      .getRawOne();

    return parseInt(result.count) || 0;
  }
}