import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { ServeStaticModule } from '@nestjs/serve-static';
import { BullModule } from '@nestjs/bull';
import { join } from 'path';
import * as redisStore from 'cache-manager-redis-store';

// Core modules
import { DatabaseConfig } from '@/config/database.config';
import { RedisConfig } from '@/config/redis.config';
import { authConfig } from '@/config/auth.config';
import { appConfig } from '@/config/app.config';

// API modules  
import { AdminApiModule } from '@/api/admin/admin-api.module';
import { MobileApiModule } from '@/api/mobile/mobile-api.module';
import { CommonApiModule } from '@/api/common/common-api.module';

// Core services
import { AuthModule } from '@/core/auth/auth.module';
import { CacheService } from '@/core/cache/cache.service';
import { StorageModule } from '@/core/storage/storage.module';

// Health check
import { HealthModule } from '@/modules/health/health.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, authConfig],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfig,
    }),

    // Redis缓存模块
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: () => ({
        store: redisStore,
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
        ttl: 300, // 5分钟默认TTL
      }),
    }),

    // 任务队列模块
    BullModule.forRootAsync({
      useClass: RedisConfig,
    }),

    // 限流模块
    ThrottlerModule.forRoot([
      {
        name: 'short',
        ttl: 1000, // 1秒
        limit: 10, // 10次请求
      },
      {
        name: 'medium',
        ttl: 60000, // 1分钟
        limit: 100, // 100次请求
      },
      {
        name: 'long',
        ttl: 900000, // 15分钟
        limit: 1000, // 1000次请求
      },
    ]),

    // 静态文件服务
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      serveRoot: '/static',
    }),

    // 核心模块
    AuthModule,
    StorageModule,

    // API模块
    AdminApiModule,
    MobileApiModule,
    CommonApiModule,

    // 健康检查
    HealthModule,
  ],
  providers: [CacheService],
})
export class AppModule {}