import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { BullModule } from '@nestjs/bull';
import { join } from 'path';
import * as redisStore from 'cache-manager-redis-store';

// Core modules
import { DatabaseConfig } from '@/config/database.config';
import { RedisConfig } from '@/config/redis.config';
import { authConfig } from '@/config/auth.config';
import { appConfig } from '@/config/app.config';
import { securityConfig } from '@/config/security.config';

// API modules  
import { AdminApiModule } from '@/api/admin/admin-api.module';
import { MobileApiModule } from '@/api/mobile/mobile-api.module';
import { CommonApiModule } from '@/api/common/common-api.module';

// Core services
import { AuthModule } from '@/core/auth/auth.module';
import { SecurityModule } from '@/core/security/security.module';
import { CacheService } from '@/core/cache/cache.service';
import { CacheModule as CoreCacheModule } from '@/core/cache/cache.module';
import { StorageModule } from '@/core/storage/storage.module';
import { PerformanceModule } from '@/core/performance/performance.module';
import { LoggingModule } from '@/core/logging/logging.module';

// Health check
import { HealthModule } from '@/modules/health/health.module';

// AI module
import { AIModule } from './ai/ai.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, authConfig, securityConfig],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfig,
    }),

    // Redis缓存模块 - 移至核心模块

    // 任务队列模块
    BullModule.forRootAsync({
      useClass: RedisConfig,
    }),

    // 限流模块
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1分钟
        limit: 100, // 100次请求
      },
    ]),

    // 静态文件服务已通过Fastify插件处理

    // 核心模块
    LoggingModule,
    CoreCacheModule,
    AuthModule,
    SecurityModule,
    StorageModule,
    PerformanceModule,

    // API模块
    AdminApiModule,
    MobileApiModule,
    CommonApiModule,

    // 健康检查
    HealthModule,

    // AI功能
    AIModule,
  ],
  providers: [CacheService],
})
export class AppModule {}