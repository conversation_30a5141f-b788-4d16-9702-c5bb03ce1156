#!/bin/bash

# MasteryOS 监控系统启动脚本
echo "🚀 Starting MasteryOS Monitoring Stack..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install it first."
    exit 1
fi

# 创建必要的目录
echo "📁 Creating necessary directories..."
mkdir -p prometheus/data
mkdir -p grafana/data
mkdir -p alertmanager/data

# 设置权限
echo "🔐 Setting up permissions..."
sudo chown -R 472:472 grafana/data || echo "Warning: Could not set Grafana permissions"
sudo chown -R 65534:65534 prometheus/data || echo "Warning: Could not set Prometheus permissions"
sudo chown -R 65534:65534 alertmanager/data || echo "Warning: Could not set Alertmanager permissions"

# 启动监控栈
echo "🏗️  Starting monitoring stack..."
docker-compose up -d

# 等待服务启动
echo "⏳ Waiting for services to start..."
sleep 10

# 检查服务状态
echo "🔍 Checking service status..."

# 检查Prometheus
if curl -s http://localhost:9090/-/healthy > /dev/null; then
    echo "✅ Prometheus is running at http://localhost:9090"
else
    echo "❌ Prometheus is not responding"
fi

# 检查Grafana
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Grafana is running at http://localhost:3000"
    echo "   Default credentials: admin/admin123"
else
    echo "❌ Grafana is not responding"
fi

# 检查Alertmanager
if curl -s http://localhost:9093/-/healthy > /dev/null; then
    echo "✅ Alertmanager is running at http://localhost:9093"
else
    echo "❌ Alertmanager is not responding"
fi

# 检查Node Exporter
if curl -s http://localhost:9100/metrics > /dev/null; then
    echo "✅ Node Exporter is running at http://localhost:9100"
else
    echo "❌ Node Exporter is not responding"
fi

# 检查cAdvisor
if curl -s http://localhost:8080/metrics > /dev/null; then
    echo "✅ cAdvisor is running at http://localhost:8080"
else
    echo "❌ cAdvisor is not responding"
fi

echo ""
echo "🎉 Monitoring stack setup complete!"
echo ""
echo "📊 Access URLs:"
echo "   • Prometheus: http://localhost:9090"
echo "   • Grafana: http://localhost:3000 (admin/admin123)"
echo "   • Alertmanager: http://localhost:9093"
echo "   • Node Exporter: http://localhost:9100"
echo "   • cAdvisor: http://localhost:8080"
echo ""
echo "📈 API Metrics:"
echo "   • Metrics endpoint: http://localhost:3001/api/v1/metrics"
echo "   • Health check: http://localhost:3001/api/v1/health"
echo "   • Monitoring dashboard: http://localhost:3001/api/v1/admin/monitoring/health"
echo ""
echo "🛠️  Useful commands:"
echo "   • View logs: docker-compose logs -f [service-name]"
echo "   • Stop stack: docker-compose down"
echo "   • Restart: docker-compose restart [service-name]"
echo ""
echo "📝 Next steps:"
echo "   1. Start your MasteryOS API server"
echo "   2. Import Grafana dashboards from ./grafana/dashboards/"
echo "   3. Configure alert notifications in Alertmanager"
echo "   4. Customize Prometheus scrape targets in ./prometheus/prometheus.yml"