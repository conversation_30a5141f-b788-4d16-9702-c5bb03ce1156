# MasteryOS 监控系统

这是MasteryOS统一BFF服务的完整监控解决方案，基于Prometheus + Grafana + Alertmanager构建。

## 🚀 快速开始

### 1. 启动监控栈

```bash
cd monitoring
./start-monitoring.sh
```

### 2. 启动API服务

```bash
cd ../
pnpm run start:dev
```

### 3. 访问监控界面

- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **Alertmanager**: http://localhost:9093

## 📊 监控指标

### HTTP指标
- `masteryos_http_requests_total` - HTTP请求总数
- `masteryos_http_request_duration_seconds` - HTTP请求响应时间
- `masteryos_http_requests_in_flight` - 正在处理的HTTP请求数

### 系统指标
- `masteryos_system_cpu_usage_percent` - CPU使用率
- `masteryos_system_memory_usage_bytes` - 内存使用情况
- `masteryos_system_uptime_seconds` - 系统运行时间

### 应用指标
- `masteryos_active_users_total` - 活跃用户数
- `masteryos_database_connections_total` - 数据库连接数
- `masteryos_cache_hit_rate` - 缓存命中率

### 业务指标
- `masteryos_user_registrations_total` - 用户注册数
- `masteryos_document_uploads_total` - 文档上传数
- `masteryos_authentication_attempts_total` - 认证尝试数

### 错误指标
- `masteryos_error_rate` - 错误率
- `masteryos_database_errors_total` - 数据库错误数
- `masteryos_cache_errors_total` - 缓存错误数

## 🎯 访问端点

### API指标端点
- `GET /api/v1/metrics` - Prometheus格式指标
- `GET /api/v1/metrics/json` - JSON格式指标
- `GET /api/v1/metrics/health` - 指标系统健康检查
- `GET /api/v1/metrics/stats` - 指标统计信息

### 健康检查端点
- `GET /api/v1/health` - 基础健康检查
- `GET /api/v1/health/monitoring` - 监控系统健康检查
- `GET /api/v1/health/metrics` - 基础指标

### 管理员监控端点
- `GET /api/v1/admin/monitoring/health` - 系统健康状态
- `GET /api/v1/admin/monitoring/metrics/system` - 系统指标
- `GET /api/v1/admin/monitoring/metrics/application` - 应用指标
- `GET /api/v1/admin/monitoring/diagnostics` - 系统诊断

## 📈 Grafana仪表板

### 主要仪表板
- **MasteryOS - API Overview** - API整体概览
  - HTTP请求率和响应时间
  - 系统资源使用情况
  - 错误率和缓存命中率
  - 业务指标趋势

### 导入仪表板
1. 登录Grafana (admin/admin123)
2. 导航到 "+" → "Import"
3. 上传 `grafana/dashboards/masteryos-overview.json`

## 🚨 告警规则

### 关键告警
- **APIDown** - API服务不可用
- **HighErrorRate** - 错误率超过5%
- **HighResponseTime** - 95%响应时间超过2秒
- **HighCPUUsage** - CPU使用率超过80%
- **HighMemoryUsage** - 内存使用率超过85%

### 告警通知
告警通知通过以下方式发送：
- Email (需要配置SMTP)
- Slack (需要配置Webhook)
- Webhook (发送到API端点)

## 🔧 配置

### Prometheus配置
编辑 `prometheus/prometheus.yml` 来修改：
- 抓取目标
- 抓取间隔
- 告警规则

### Grafana配置
- 数据源配置: `grafana/provisioning/datasources/`
- 仪表板配置: `grafana/provisioning/dashboards/`

### Alertmanager配置
编辑 `alertmanager/alertmanager.yml` 来配置：
- 通知渠道
- 告警路由
- 告警抑制规则

## 🐳 Docker服务

### 服务列表
- **prometheus** - 指标收集和存储
- **grafana** - 可视化界面
- **alertmanager** - 告警管理
- **node-exporter** - 系统指标收集
- **cadvisor** - 容器指标收集

### 常用命令
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f prometheus

# 重启服务
docker-compose restart grafana

# 停止监控栈
docker-compose down

# 停止并清理数据
docker-compose down -v
```

## 📝 自定义指标

### 在代码中添加指标
```typescript
// 注入PrometheusService
constructor(private prometheusService: PrometheusService) {}

// 记录业务事件
this.prometheusService.recordUserRegistration('web');
this.prometheusService.recordDocumentUpload('pdf', 'admin');

// 创建自定义指标
const customCounter = this.prometheusService.createCounter(
  'custom_events_total',
  'Total custom events',
  ['event_type']
);

customCounter.inc({ event_type: 'user_action' });
```

### 自定义告警规则
在 `prometheus/rules/` 目录下创建新的 `.yml` 文件：

```yaml
groups:
  - name: custom.alerts
    rules:
      - alert: CustomAlert
        expr: custom_metric > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Custom metric exceeded threshold"
```

## 🔍 故障排除

### 常见问题

1. **指标端点不可访问**
   - 检查API服务是否运行
   - 确认端口3001未被占用
   - 检查防火墙设置

2. **Grafana无法连接Prometheus**
   - 检查Prometheus服务状态
   - 验证网络连接
   - 确认数据源配置

3. **告警不工作**
   - 检查Alertmanager配置
   - 验证告警规则语法
   - 确认通知渠道配置

### 日志查看
```bash
# API服务日志
pnpm run start:dev

# Prometheus日志
docker-compose logs prometheus

# Grafana日志
docker-compose logs grafana

# Alertmanager日志
docker-compose logs alertmanager
```

## 📚 扩展阅读

- [Prometheus官方文档](https://prometheus.io/docs/)
- [Grafana官方文档](https://grafana.com/docs/)
- [NestJS监控最佳实践](https://docs.nestjs.com/techniques/performance)
- [Alertmanager配置指南](https://prometheus.io/docs/alerting/configuration/)

## 🤝 贡献

如需添加新的指标或改进监控系统，请：
1. 创建feature分支
2. 添加相关指标和测试
3. 更新文档
4. 提交Pull Request

## 📄 许可证

本监控系统配置遵循项目主许可证。