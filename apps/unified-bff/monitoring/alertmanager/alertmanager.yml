global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'severity', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      repeat_interval: 1h
    
    - match:
        service: api
      receiver: 'api-team'
      group_wait: 10s
      
    - match:
        service: database
      receiver: 'db-team'
      group_wait: 10s

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://host.docker.internal:3001/api/v1/webhooks/alerts'
        send_resolved: true
        http_config:
          basic_auth:
            username: 'webhook'
            password: 'secret'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] MasteryOS Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Started: {{ .StartsAt }}
          {{ if .Labels.runbook_url }}Runbook: {{ .Labels.runbook_url }}{{ end }}
          {{ end }}
    webhook_configs:
      - url: 'http://host.docker.internal:3001/api/v1/webhooks/alerts'
        send_resolved: true
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts-critical'
        title: 'MasteryOS Critical Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          {{ if .Labels.runbook_url }}*Runbook:* {{ .Labels.runbook_url }}{{ end }}
          {{ end }}

  - name: 'api-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[API] MasteryOS Alert: {{ .GroupLabels.alertname }}'
        body: |
          API服务告警通知:
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          严重程度: {{ .Labels.severity }}
          开始时间: {{ .StartsAt }}
          {{ if .Labels.runbook_url }}处理手册: {{ .Labels.runbook_url }}{{ end }}
          {{ end }}
          
          请及时处理！

  - name: 'db-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[DATABASE] MasteryOS Alert: {{ .GroupLabels.alertname }}'
        body: |
          数据库服务告警通知:
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          严重程度: {{ .Labels.severity }}
          开始时间: {{ .StartsAt }}
          {{ if .Labels.runbook_url }}处理手册: {{ .Labels.runbook_url }}{{ end }}
          {{ end }}
          
          请立即检查数据库状态！

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service']

  - source_match:
      alertname: 'APIDown'
    target_match_re:
      alertname: 'High.*'
    equal: ['service']