groups:
  - name: masteryos.api.alerts
    rules:
      # API可用性告警
      - alert: APIDown
        expr: up{job="masteryos-api"} == 0
        for: 1m
        labels:
          severity: critical
          service: api
        annotations:
          summary: "MasteryOS API is down"
          description: "MasteryOS API has been down for more than 1 minute"
          runbook_url: "https://docs.masteryos.com/runbooks/api-down"

      # 高错误率告警
      - alert: HighErrorRate
        expr: (rate(masteryos_http_requests_total{status_code=~"5.."}[5m]) / rate(masteryos_http_requests_total[5m])) > 0.05
        for: 5m
        labels:
          severity: warning
          service: api
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          runbook_url: "https://docs.masteryos.com/runbooks/high-error-rate"

      # 响应时间过长告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(masteryos_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: api
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes"
          runbook_url: "https://docs.masteryos.com/runbooks/high-response-time"

      # 请求量异常告警
      - alert: RequestVolumeSpike
        expr: rate(masteryos_http_requests_total[5m]) > 100
        for: 2m
        labels:
          severity: warning
          service: api
        annotations:
          summary: "Unusual request volume spike"
          description: "Request rate is {{ $value }} req/s, which is unusually high"
          runbook_url: "https://docs.masteryos.com/runbooks/request-volume-spike"

      # 认证失败率过高
      - alert: HighAuthFailureRate
        expr: (rate(masteryos_authentication_attempts_total{result="failure"}[5m]) / rate(masteryos_authentication_attempts_total[5m])) > 0.2
        for: 3m
        labels:
          severity: warning
          service: auth
        annotations:
          summary: "High authentication failure rate"
          description: "Authentication failure rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          runbook_url: "https://docs.masteryos.com/runbooks/high-auth-failure"

  - name: masteryos.system.alerts
    rules:
      # CPU使用率过高
      - alert: HighCPUUsage
        expr: masteryos_system_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% for the last 5 minutes"
          runbook_url: "https://docs.masteryos.com/runbooks/high-cpu-usage"

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (masteryos_system_memory_usage_bytes{type="used"} / masteryos_system_memory_usage_bytes{type="total"}) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% for the last 5 minutes"
          runbook_url: "https://docs.masteryos.com/runbooks/high-memory-usage"

      # 内存使用率严重过高
      - alert: CriticalMemoryUsage
        expr: (masteryos_system_memory_usage_bytes{type="used"} / masteryos_system_memory_usage_bytes{type="total"}) * 100 > 95
        for: 1m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Critical memory usage"
          description: "Memory usage is {{ $value }}% - system may become unstable"
          runbook_url: "https://docs.masteryos.com/runbooks/critical-memory-usage"

  - name: masteryos.database.alerts
    rules:
      # 数据库连接数过高
      - alert: HighDatabaseConnections
        expr: masteryos_database_connections_total{state="active"} > 15
        for: 3m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "High number of database connections"
          description: "Active database connections: {{ $value }}"
          runbook_url: "https://docs.masteryos.com/runbooks/high-db-connections"

      # 数据库错误率过高
      - alert: DatabaseErrors
        expr: rate(masteryos_database_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Database errors detected"
          description: "Database error rate: {{ $value }} errors/sec"
          runbook_url: "https://docs.masteryos.com/runbooks/database-errors"

  - name: masteryos.cache.alerts
    rules:
      # 缓存命中率过低
      - alert: LowCacheHitRate
        expr: masteryos_cache_hit_rate < 0.7
        for: 10m
        labels:
          severity: warning
          service: cache
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value | humanizePercentage }} for the last 10 minutes"
          runbook_url: "https://docs.masteryos.com/runbooks/low-cache-hit-rate"

      # 缓存错误
      - alert: CacheErrors
        expr: rate(masteryos_cache_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: cache
        annotations:
          summary: "Cache errors detected"
          description: "Cache error rate: {{ $value }} errors/sec"
          runbook_url: "https://docs.masteryos.com/runbooks/cache-errors"

  - name: masteryos.business.alerts
    rules:
      # 用户注册异常
      - alert: UnusualUserRegistrations
        expr: rate(masteryos_user_registrations_total[1h]) > 10
        for: 5m
        labels:
          severity: info
          service: business
        annotations:
          summary: "Unusual user registration activity"
          description: "User registration rate: {{ $value }} registrations/hour"
          runbook_url: "https://docs.masteryos.com/runbooks/unusual-registrations"

      # 文档上传异常
      - alert: HighDocumentUploadRate
        expr: rate(masteryos_document_uploads_total[5m]) > 5
        for: 3m
        labels:
          severity: info
          service: business
        annotations:
          summary: "High document upload rate"
          description: "Document upload rate: {{ $value }} uploads/sec"
          runbook_url: "https://docs.masteryos.com/runbooks/high-upload-rate"