global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'masteryos'
    environment: 'development'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # MasteryOS Unified BFF API
  - job_name: 'masteryos-api'
    static_configs:
      - targets: ['host.docker.internal:3001']  # 假设API运行在3001端口
    metrics_path: '/api/v1/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # Prometheus自监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 系统指标收集
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # 容器指标收集
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

  # Redis 指标 (如果有redis_exporter)
  - job_name: 'redis'
    static_configs:
      - targets: ['host.docker.internal:9121']
    scrape_interval: 15s

  # PostgreSQL 指标 (如果有postgres_exporter)
  - job_name: 'postgres'
    static_configs:
      - targets: ['host.docker.internal:9187']
    scrape_interval: 15s

  # 健康检查端点
  - job_name: 'masteryos-health'
    static_configs:
      - targets: ['host.docker.internal:3001']
    metrics_path: '/api/v1/health'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Blackbox探针 - API端点可用性检查
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://host.docker.internal:3001/api/v1/health
        - http://host.docker.internal:3001/api/v1/health/database
        - http://host.docker.internal:3001/api/v1/health/redis
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# 远程写入配置 (可选)
# remote_write:
#   - url: "http://remote-storage:9201/api/v1/write"
#     queue_config:
#       max_samples_per_send: 10000
#       max_shards: 200
#       capacity: 20000

# 远程读取配置 (可选)
# remote_read:
#   - url: "http://remote-storage:9201/api/v1/read"