node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist
coverage
.nyc_output

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md

# Test files
test
*.spec.ts
*.test.ts
jest.config.js

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Uploads (will be created at runtime)
uploads