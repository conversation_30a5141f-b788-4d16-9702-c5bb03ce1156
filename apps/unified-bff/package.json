{"name": "masteryos-unified-bff", "version": "1.0.0", "description": "MasteryOS 统一 BFF API - 支持管理端和移动端", "author": "MasteryOS Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "nest": "nest", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/config/database.config.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/config/database.config.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/config/database.config.ts", "seed": "ts-node src/database/seeds/run-seeds.ts"}, "dependencies": {"@fastify/compress": "^8.1.0", "@fastify/helmet": "^13.0.1", "@fastify/multipart": "^8.0.0", "@fastify/static": "^8.2.0", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.25", "@langchain/openai": "^0.6.3", "@langchain/textsplitters": "^0.1.0", "@nestjs/bull": "^11.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.1.5", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cache-manager": "^6.4.3", "cache-manager-redis-yet": "^5.1.5", "chromadb": "^3.0.10", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.1", "fastify": "^5.2.0", "langchain": "^0.3.30", "mammoth": "^1.10.0", "minio": "^8.0.5", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pg": "^8.16.3", "prom-client": "^15.1.3", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "typeorm": "^0.3.25", "uuid": "^11.1.0", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^11.1.5", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/node": "^22.17.0", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pdf-parse": "^1.1.5", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-prettier": "^5.5.3", "jest": "^29.7.0", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.4", "ts-jest": "^29.4.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}