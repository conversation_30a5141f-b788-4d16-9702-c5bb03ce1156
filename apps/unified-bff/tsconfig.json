{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": false, "strictNullChecks": false, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": false, "noImplicitThis": false, "noUncheckedIndexedAccess": false, "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/shared/*": ["src/shared/*"], "@/modules/*": ["src/modules/*"], "@/config/*": ["src/config/*"]}}}