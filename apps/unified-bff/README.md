# MasteryOS 统一BFF服务

## 概述

MasteryOS 统一 BFF (Backend for Frontend) 服务，整合了管理端和移动端的API需求，提供统一的后端服务接口。

## 特性

- 🔧 **统一架构**: 支持管理端和移动端的API访问
- 🚀 **高性能**: Redis缓存、连接池优化、响应时间监控
- 🔐 **安全认证**: JWT认证、角色权限控制、API限流
- 📊 **可观测性**: 结构化日志、健康检查、性能指标
- 🔄 **API版本控制**: 支持多版本API共存
- 📚 **API文档**: 自动生成的Swagger文档

## 技术栈

- **框架**: NestJS 10.x
- **数据库**: PostgreSQL + TypeORM
- **缓存**: Redis + Bull队列
- **存储**: MinIO对象存储
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **容器**: Docker + Docker Compose

## API结构

```
/api/v1/admin/        # 管理端API
├── users/            # 用户管理
├── documents/        # 文档管理
└── analytics/        # 数据分析

/api/v1/mobile/       # 移动端API
├── profile/          # 个人资料
├── learning/         # 学习模块
└── documents/        # 文档浏览

/api/v1/common/       # 通用API
├── auth/             # 认证服务
└── health/           # 健康检查
```

## 快速开始

### 环境要求

- Node.js 18+
- pnpm 8+
- PostgreSQL 15+
- Redis 7+

### 安装依赖

```bash
pnpm install
```

### 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 开发启动

```bash
# 开发模式
pnpm run start:dev

# 调试模式
pnpm run start:debug
```

### 生产构建

```bash
# 构建应用
pnpm run build

# 生产启动
pnpm run start:prod
```

## Docker部署

### 构建镜像

```bash
docker build -t masteryos/unified-bff:latest .
```

### 运行容器

```bash
docker run -d \
  --name unified-bff \
  -p 3100:3100 \
  -e DATABASE_URL=postgresql://user:pass@localhost:5432/db \
  -e REDIS_URL=redis://localhost:6379 \
  masteryos/unified-bff:latest
```

## 开发指南

### 项目结构

```
src/
├── core/                 # 核心模块
│   ├── auth/            # 认证授权
│   ├── cache/           # 缓存服务
│   └── storage/         # 文件存储
├── shared/              # 共享模块
│   ├── dto/             # 数据传输对象
│   ├── entities/        # 数据库实体
│   ├── filters/         # 异常过滤器
│   └── interceptors/    # 拦截器
├── modules/             # 业务模块
│   ├── users/           # 用户管理
│   └── health/          # 健康检查
├── api/                 # API路由
│   ├── admin/           # 管理端API
│   ├── mobile/          # 移动端API
│   └── common/          # 通用API
└── config/              # 配置文件
```

### 添加新模块

1. 在 `src/modules/` 创建模块目录
2. 创建实体、DTO、服务、控制器
3. 在对应的API模块中注册
4. 更新文档和测试

### API设计原则

- RESTful设计风格
- 统一响应格式
- 错误码标准化
- 分页参数一致
- 版本控制支持

## 测试

```bash
# 单元测试
pnpm run test

# 集成测试
pnpm run test:e2e

# 测试覆盖率
pnpm run test:cov
```

## 监控

### 健康检查

- **基础健康**: `GET /health`
- **详细健康**: `GET /api/v1/health/detailed`
- **数据库健康**: `GET /api/v1/health/database`
- **Redis健康**: `GET /api/v1/health/redis`

### API文档

访问 `http://localhost:3100/api/docs` 查看完整的API文档。

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `PORT` | 服务端口 | `3100` |
| `NODE_ENV` | 运行环境 | `development` |
| `DATABASE_URL` | 数据库连接 | - |
| `REDIS_URL` | Redis连接 | `redis://localhost:6379` |
| `JWT_SECRET` | JWT密钥 | - |

### 缓存策略

- **用户信息**: 30分钟
- **API响应**: 5分钟  
- **统计数据**: 1小时
- **验证码**: 5分钟

## 安全

- JWT认证与刷新机制
- 基于角色的访问控制(RBAC)
- API请求频率限制
- 输入验证与清理
- SQL注入防护
- XSS防护

## 性能优化

- Redis缓存层
- 数据库连接池
- 查询优化与索引
- 响应压缩
- 静态资源缓存

## 故障排除

### 常见问题

1. **数据库连接失败**: 检查DATABASE_URL配置
2. **Redis连接失败**: 检查REDIS_URL配置  
3. **JWT验证失败**: 检查JWT_SECRET配置
4. **端口冲突**: 修改PORT环境变量

### 日志查看

```bash
# Docker日志
docker logs unified-bff

# 应用日志
tail -f logs/application.log
```

## 贡献

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

Private License - MasteryOS Team

---

**端口说明**: 本服务使用3100端口，避免与端口3000的其他应用冲突。