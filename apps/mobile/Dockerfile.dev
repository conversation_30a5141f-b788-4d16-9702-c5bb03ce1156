# Flutter 开发环境 Docker 配置
# 基于 Ubuntu 20.04，安装 Flutter 3.32.1
FROM ubuntu:20.04

# 设置时区和编码
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    openjdk-11-jdk \
    wget \
    file \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 设置 Java 环境变量
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

# 创建开发用户
RUN useradd -m -s /bin/bash developer && \
    usermod -aG sudo developer

# 切换到开发用户
USER developer
WORKDIR /home/<USER>

# 下载并安装 Flutter 3.32.1
ENV FLUTTER_VERSION=3.32.1
ENV FLUTTER_HOME=/home/<USER>/flutter
ENV PATH="$FLUTTER_HOME/bin:/home/<USER>/.pub-cache/bin:$PATH"

RUN git clone https://github.com/flutter/flutter.git $FLUTTER_HOME && \
    cd $FLUTTER_HOME && \
    git checkout $FLUTTER_VERSION && \
    flutter --version && \
    flutter doctor

# 预下载依赖
RUN flutter precache --web --android --ios

# 配置 Flutter Web 支持
RUN flutter config --enable-web

# 设置工作目录
WORKDIR /app

# 复制 pubspec 文件
COPY --chown=developer:developer pubspec.yaml pubspec.lock* ./

# 安装依赖
RUN flutter pub get

# 复制应用代码
COPY --chown=developer:developer . .

# 暴露端口
EXPOSE 8080

# 设置启动命令
CMD ["flutter", "run", "-d", "web-server", "--web-port=8080", "--web-hostname=0.0.0.0"]