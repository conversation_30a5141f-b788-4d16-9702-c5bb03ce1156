import 'package:flutter_test/flutter_test.dart';
import 'package:masteryos_mobile/core/app/app.dart';
import 'package:masteryos_mobile/core/app/app_config.dart';

void main() {
  testWidgets('App loads successfully', (WidgetTester tester) async {
    // 创建测试配置
    const config = AppConfig(
      appName: 'MasteryOS Mobile Test',
      baseUrl: 'http://localhost:3101',
      environment: AppEnvironment.development,
    );

    // 构建应用
    await tester.pumpWidget(MasteryOSApp(config: config));

    // 验证应用能够正常加载
    expect(find.text('欢迎来到 MasteryOS'), findsOneWidget);
  });
}