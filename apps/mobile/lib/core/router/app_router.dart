import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:masteryos_mobile/features/auth/presentation/pages/login_page.dart';
import 'package:masteryos_mobile/features/auth/presentation/pages/register_page.dart';
import 'package:masteryos_mobile/features/home/<USER>/pages/home_page.dart';
import 'package:masteryos_mobile/features/home/<USER>/pages/main_navigation_page.dart';
import 'package:masteryos_mobile/features/documents/presentation/pages/documents_page.dart';
import 'package:masteryos_mobile/features/learning/presentation/pages/learning_page.dart';
import 'package:masteryos_mobile/features/profile/presentation/pages/profile_page.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    routes: [
      // 认证路由
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      
      // 主应用路由 - 使用 ShellRoute 实现底部导航
      ShellRoute(
        builder: (context, state, child) => MainNavigationPage(child: child),
        routes: [
          // 首页
          GoRoute(
            path: '/',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          
          // 文档管理
          GoRoute(
            path: '/documents',
            name: 'documents',
            builder: (context, state) => const DocumentsPage(),
          ),
          
          // 学习中心
          GoRoute(
            path: '/learning',
            name: 'learning',
            builder: (context, state) => const LearningPage(),
          ),
          
          // 个人资料
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
        ],
      ),
    ],
    initialLocation: '/',
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('页面未找到: ${state.error}'),
      ),
    ),
  );
}