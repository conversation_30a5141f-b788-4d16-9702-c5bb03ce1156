enum AppEnvironment {
  development,
  staging,
  production,
}

class AppConfig {
  final String appName;
  final String baseUrl;
  final AppEnvironment environment;

  const AppConfig({
    required this.appName,
    required this.baseUrl,
    required this.environment,
  });

  bool get isDevelopment => environment == AppEnvironment.development;
  bool get isStaging => environment == AppEnvironment.staging;
  bool get isProduction => environment == AppEnvironment.production;

  String get apiUrl => '$baseUrl/api';
  String get authUrl => '$baseUrl/auth';
  String get documentsUrl => '$baseUrl/documents';
  
  // API endpoints
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String refreshTokenEndpoint = '/auth/refresh';
  static const String profileEndpoint = '/users/profile';
  static const String documentsEndpoint = '/documents';
  static const String learningSessionsEndpoint = '/learning-sessions';
  static const String skillsEndpoint = '/skills';
}