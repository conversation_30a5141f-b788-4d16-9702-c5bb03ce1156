# MasteryOS Mobile App

基于Flutter 3.32.1开发的智能技能发展移动应用。

## 🚀 快速开始

### 环境要求

- Flutter 3.32.1 (通过FVM管理)
- Dart 3.8.1+
- VS Code + Flutter扩展

### 安装和运行

```bash
# 1. 安装依赖
fvm flutter pub get

# 2. 运行开发服务器 (Web)
fvm flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0

# 3. 运行移动端 (需要模拟器或真机)
fvm flutter run

# 4. 构建生产版本
fvm flutter build web --release
fvm flutter build apk --release
fvm flutter build ios --release
```

## 🏗️ 项目架构

项目采用Clean Architecture + BLoC模式：

```
lib/
├── core/                   # 核心功能
│   ├── app/               # 应用配置
│   ├── theme/             # 主题系统
│   ├── router/            # 路由配置
│   ├── constants/         # 常量定义
│   ├── utils/             # 工具函数
│   └── network/           # 网络层
├── features/              # 功能模块
│   ├── auth/              # 认证模块
│   │   ├── data/          # 数据层
│   │   ├── domain/        # 业务逻辑层
│   │   └── presentation/  # 表现层
│   ├── documents/         # 文档管理
│   ├── learning/          # 学习功能
│   └── profile/           # 个人资料
└── shared/                # 共享组件
    ├── widgets/           # 通用组件
    ├── models/            # 数据模型
    └── services/          # 共享服务
```

## 📱 功能模块

### 已实现
- ✅ 基础应用架构
- ✅ 主题系统 (支持深色/浅色)
- ✅ 路由配置
- ✅ 基础页面结构

### 待开发
- 🔄 用户认证 (JWT)
- 🔄 文档上传和浏览
- 🔄 PDF阅读和标注
- 🔄 学习进度追踪
- 🔄 AI学习助手
- 🔄 个人资料管理

## 🛠️ 开发工具

### VS Code 配置

项目已配置FVM支持，确保VS Code使用正确的Flutter版本：

```json
{
  "flutter.sdkPath": ".fvm/flutter_sdk",
  "dart.flutterSdkPath": ".fvm/flutter_sdk"
}
```

### 推荐扩展

- Flutter (Dart-Code.flutter)
- Dart (Dart-Code.dart-code)
- Bracket Pair Colorizer
- GitLens

### 代码生成

```bash
# 生成模型类
fvm dart run build_runner build

# 生成Retrofit API类
fvm dart run build_runner build --delete-conflicting-outputs
```

## 🔧 开发规范

### 代码风格
- 使用flutter_lints规则
- 遵循Dart编码约定
- 组件名称使用PascalCase
- 文件名使用snake_case

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建过程或辅助工具
```

## 📦 主要依赖

### UI & Navigation
- go_router: 路由管理
- flutter_bloc: 状态管理

### HTTP & API
- dio: HTTP客户端
- retrofit: API生成器
- json_annotation: JSON序列化

### Local Storage
- shared_preferences: 本地存储
- flutter_secure_storage: 安全存储

### PDF & Documents
- syncfusion_flutter_pdfviewer: PDF查看器

## 🚀 部署

### Web部署
```bash
fvm flutter build web --release
# 将 build/web 目录部署到Web服务器
```

### Android部署
```bash
fvm flutter build apk --release
# APK文件位于 build/app/outputs/flutter-apk/
```

### iOS部署
```bash
fvm flutter build ios --release
# 需要在Xcode中配置签名和发布
```

## 🐛 故障排除

### 常见问题

1. **FVM版本错误**
   ```bash
   fvm use 3.32.1
   fvm flutter --version
   ```

2. **依赖冲突**
   ```bash
   fvm flutter clean
   fvm flutter pub get
   ```

3. **Web编译失败**
   ```bash
   fvm flutter clean
   fvm flutter pub get
   fvm flutter build web --verbose
   ```

### 性能优化

- 使用const构造函数
- 避免不必要的重建
- 使用ListView.builder处理长列表
- 图片使用缓存和压缩

## 📝 开发笔记

### API集成
- 基础URL: http://localhost:3101
- 认证: JWT Bearer Token
- 错误处理: 统一错误响应格式

### 状态管理
- 使用BLoC模式管理状态
- 每个功能模块独立的BLoC
- 共享状态使用GlobalBloc

### 主题系统
- 支持系统深色/浅色模式
- Material Design 3.0
- 自定义品牌色彩

---

**开发团队**: MasteryOS Team  
**更新时间**: 2025年7月14日  
**Flutter版本**: 3.32.1