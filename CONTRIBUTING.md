# 贡献指南

感谢您对 MasteryOS (1w) 项目的关注和贡献！本指南将帮助您了解如何参与项目开发。

## 🤝 贡献方式

我们欢迎以下形式的贡献：

- 🐛 **Bug 报告**: 发现并报告项目中的问题
- ✨ **功能建议**: 提出新功能或改进建议
- 💻 **代码贡献**: 修复 Bug 或实现新功能
- 📝 **文档改进**: 完善项目文档
- 🧪 **测试**: 编写或改进测试用例
- 🌍 **本地化**: 帮助翻译和国际化

## 🚀 快速开始

### 1. 环境准备

确保您的开发环境满足要求：

- **Node.js**: ≥ 22.18.0 LTS
- **pnpm**: ≥ 8.0.0
- **Docker**: ≥ 20.10.0
- **Git**: ≥ 2.30.0

### 2. Fork 和 Clone

```bash
# 1. Fork 项目到您的 GitHub 账户
# 2. Clone 到本地
git clone https://github.com/YOUR_USERNAME/1w.git
cd 1w

# 3. 添加上游仓库
git remote add upstream https://github.com/changxiaoyangbrain/1w.git
```

### 3. 安装依赖

```bash
# 安装项目依赖
pnpm install

# 启动开发环境
./scripts/1w-db.sh start
```

## 🔄 开发工作流

### 1. 创建分支

为您的贡献创建一个描述性的分支：

```bash
# 创建并切换到新分支
git checkout -b feature/user-authentication
git checkout -b fix/database-connection-issue
git checkout -b docs/update-installation-guide
```

分支命名规范：
- `feature/` - 新功能
- `fix/` - Bug 修复
- `docs/` - 文档更新
- `refactor/` - 代码重构
- `test/` - 测试相关

### 2. 开发和测试

```bash
# 运行代码质量检查
pnpm run lint
pnpm run typecheck

# 运行测试
pnpm run test

# Flutter 开发
cd apps/mobile
fvm flutter analyze
fvm flutter test
```

### 3. 提交代码

我们使用 [约定式提交](https://www.conventionalcommits.org/) 规范：

```bash
git add .
git commit -m "feat: add user authentication system"
git commit -m "fix: resolve database connection timeout"
git commit -m "docs: update installation guide"
```

提交消息格式：
```
<类型>[可选 范围]: <描述>

[可选 正文]

[可选 脚注]
```

常用类型：
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式
- `refactor`: 重构
- `test`: 测试
- `chore`: 构建过程或工具变动

### 4. 推送和创建 PR

```bash
# 推送分支
git push origin feature/user-authentication

# 在 GitHub 上创建 Pull Request
```

## 📋 代码规范

### 代码风格

- **TypeScript**: 使用严格模式，完整的类型注解
- **ESLint**: 遵循项目 ESLint 配置
- **Prettier**: 统一代码格式化
- **注释**: 复杂逻辑必须添加注释

### 示例

```typescript
/**
 * 用户认证服务
 * 处理用户登录、注册和权限验证
 */
export class AuthService {
  /**
   * 用户登录
   * @param credentials 登录凭据
   * @returns 认证结果
   */
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    // 实现登录逻辑
  }
}
```

### Flutter 代码规范

```dart
/// 用户资料页面
/// 
/// 显示用户基本信息和学习统计
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
      ),
      body: const _ProfileContent(),
    );
  }
}
```

## 🧪 测试指南

### 单元测试

```bash
# 运行所有测试
pnpm run test

# 运行特定测试
pnpm run test -- auth.service.spec.ts

# 测试覆盖率
pnpm run test:cov
```

### Flutter 测试

```bash
cd apps/mobile

# 运行所有测试
fvm flutter test

# 运行特定测试
fvm flutter test test/auth_test.dart
```

### 测试要求

- 新功能必须包含单元测试
- 测试覆盖率应保持在 80% 以上
- 重要功能需要集成测试

## 📝 文档规范

### 文档更新

- API 变更必须更新相关文档
- 新功能需要添加使用说明
- 重要变更需要更新 CHANGELOG

### 文档格式

```markdown
# 文档标题

**版本**: 1.0  
**更新时间**: 2025-08-04  
**维护者**: 负责人姓名  
**状态**: ✅ 稳定 | 🚧 开发中 | ⚠️ 待优化

## 概述

简要描述文档内容...

## 详细内容

具体的使用方法和示例...
```

## 🚫 提交前检查

在提交 PR 之前，请确保：

- [ ] 代码通过所有测试
- [ ] 符合代码规范 (ESLint + Prettier)
- [ ] TypeScript 类型检查通过
- [ ] 添加必要的测试用例
- [ ] 更新相关文档
- [ ] 提交信息符合规范
- [ ] 解决所有合并冲突

## 🔍 代码审查

### 审查流程

1. **自动检查**: GitHub Actions 运行测试和质量检查
2. **团队审查**: 至少一名团队成员审查
3. **合并**: 审查通过后合并到主分支

### 审查标准

- 功能是否正确实现
- 代码质量和可读性
- 测试覆盖率
- 文档完整性
- 性能影响

## 🏷️ Issue 报告

### Bug 报告

使用 Bug 报告模板，包含：

- 问题描述
- 重现步骤
- 预期行为
- 实际行为
- 环境信息
- 相关日志

### 功能请求

使用功能请求模板，包含：

- 功能描述
- 使用场景
- 解决的问题
- 实现建议

## 🎉 认可贡献者

我们会在以下方式认可贡献者：

- README 中的贡献者列表
- Release Notes 中的致谢
- GitHub Contributors 页面

## 📞 获取帮助

- **技术问题**: [GitHub Issues](https://github.com/changxiaoyangbrain/1w/issues)
- **讨论交流**: [GitHub Discussions](https://github.com/changxiaoyangbrain/1w/discussions)
- **实时沟通**: [待补充]

## 📄 许可证

通过向本项目贡献代码，您同意您的贡献将按照 [MIT 许可证](LICENSE) 进行许可。

---

**感谢您的贡献！** 🙏

您的参与让 MasteryOS 变得更好！