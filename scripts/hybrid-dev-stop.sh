#!/bin/bash

echo "🛑 停止 MasteryOS 混合架构开发环境"
echo "====================================="

# 停止所有服务
echo "🔄 停止所有容器..."
docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml down

echo ""
echo "📊 检查剩余容器..."
remaining=$(docker ps -a --filter "name=masteryos" --format "table {{.Names}}\t{{.Status}}" | grep -v "NAMES")

if [ -n "$remaining" ]; then
    echo "⚠️  发现剩余的MasteryOS容器:"
    echo "$remaining"
    echo ""
    read -p "是否要强制清理这些容器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker ps -a --filter "name=masteryos" --format "{{.Names}}" | xargs -r docker rm -f
        echo "✅ 剩余容器已清理"
    fi
else
    echo "✅ 所有容器已停止"
fi

echo ""
echo "🌐 检查端口占用..."
ports=(3100 3101 3102 5432 6379 8080 8081 8182 9000 9101 11434)
occupied_ports=()

for port in "${ports[@]}"; do
    if lsof -i :$port > /dev/null 2>&1; then
        process=$(lsof -i :$port | tail -1 | awk '{print $1}')
        occupied_ports+=("$port ($process)")
    fi
done

if [ ${#occupied_ports[@]} -gt 0 ]; then
    echo "⚠️  以下端口仍被占用:"
    for port_info in "${occupied_ports[@]}"; do
        echo "   🔌 $port_info"
    done
    echo ""
    echo "💡 如需释放端口，请手动停止相关进程"
else
    echo "✅ 所有相关端口已释放"
fi

echo ""
echo "💾 数据卷状态:"
volumes=$(docker volume ls --filter "name=masteryos-hybrid-dev" --format "{{.Name}}")
if [ -n "$volumes" ]; then
    echo "📁 保留的数据卷:"
    echo "$volumes" | sed 's/^/   /'
    echo ""
    echo "💡 数据卷已保留，重新启动时数据不会丢失"
    echo "   如需清理数据，请运行: ./scripts/hybrid-dev-clean.sh"
else
    echo "✅ 无数据卷需要管理"
fi

echo ""
echo "✅ MasteryOS 混合架构开发环境已停止"
echo ""
echo "🚀 重新启动环境: ./scripts/hybrid-dev-start.sh"
echo "🧹 完全清理环境: ./scripts/hybrid-dev-clean.sh"