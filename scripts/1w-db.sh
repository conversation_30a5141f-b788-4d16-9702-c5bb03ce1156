#!/bin/bash
# 1w 项目数据库管理脚本
# MasteryOS 技能学习平台数据库服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
COMPOSE_FILE="${PROJECT_ROOT}/infrastructure/docker/docker-compose.database-only.yml"
PROJECT_NAME="1w"

echo -e "${BLUE}🚀 1w 项目数据库管理${NC}"

# 显示帮助信息
show_help() {
    echo
    echo -e "${YELLOW}用法: $0 [命令]${NC}"
    echo
    echo -e "${YELLOW}命令:${NC}"
    echo "  start     启动数据库服务 (PostgreSQL + Redis)"
    echo "  stop      停止数据库服务"
    echo "  restart   重启数据库服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  connect   连接到 PostgreSQL 数据库"
    echo "  redis     连接到 Redis"
    echo "  clean     清理所有数据 (谨慎使用)"
    echo "  help      显示帮助信息"
    echo
    echo -e "${YELLOW}连接信息:${NC}"
    echo "  PostgreSQL: localhost:8182 (masteryos/masteryos123)"
    echo "  Redis: localhost:8183 (密码: masteryos123)"
}

# 启动服务
start_services() {
    echo -e "${GREEN}🔧 启动 1w 项目数据库服务...${NC}"
    docker-compose -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" up -d
    
    echo -e "${GREEN}✅ 服务启动完成${NC}"
    echo
    echo -e "${YELLOW}📋 连接信息:${NC}"
    echo "  PostgreSQL: localhost:8182"
    echo "  Redis: localhost:8183"
}

# 停止服务
stop_services() {
    echo -e "${YELLOW}🛑 停止 1w 项目数据库服务...${NC}"
    docker-compose -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" down
    echo -e "${GREEN}✅ 服务已停止${NC}"
}

# 重启服务
restart_services() {
    echo -e "${BLUE}🔄 重启 1w 项目数据库服务...${NC}"
    stop_services
    sleep 2
    start_services
}

# 查看状态
check_status() {
    echo -e "${BLUE}📊 1w 项目服务状态:${NC}"
    echo
    docker ps --filter "name=1w" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo
    
    # 检查健康状态
    if docker exec 1w-postgres pg_isready -U masteryos -d masteryos >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PostgreSQL: 健康${NC}"
    else
        echo -e "${RED}❌ PostgreSQL: 不健康${NC}"
    fi
    
    if docker exec 1w-redis redis-cli -a masteryos123 ping >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis: 健康${NC}"
    else
        echo -e "${RED}❌ Redis: 不健康${NC}"
    fi
}

# 查看日志
view_logs() {
    echo -e "${BLUE}📋 查看服务日志:${NC}"
    docker-compose -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" logs -f
}

# 连接到 PostgreSQL
connect_postgres() {
    echo -e "${GREEN}🔌 连接到 PostgreSQL 数据库...${NC}"
    docker exec -it 1w-postgres psql -U masteryos -d masteryos
}

# 连接到 Redis
connect_redis() {
    echo -e "${GREEN}🔌 连接到 Redis...${NC}"
    docker exec -it 1w-redis redis-cli -a masteryos123
}

# 清理数据
clean_data() {
    echo -e "${RED}⚠️  警告: 这将删除所有数据!${NC}"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🧹 清理 1w 项目数据...${NC}"
        docker-compose -f "${COMPOSE_FILE}" -p "${PROJECT_NAME}" down -v
        echo -e "${GREEN}✅ 数据清理完成${NC}"
    else
        echo -e "${BLUE}ℹ️  操作已取消${NC}"
    fi
}

# 主入口
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        check_status
        ;;
    logs)
        view_logs
        ;;
    connect|psql)
        connect_postgres
        ;;
    redis)
        connect_redis
        ;;
    clean)
        clean_data
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}❌ 未知命令: $1${NC}"
        show_help
        exit 1
        ;;
esac