#!/bin/bash

echo "🧹 清理 MasteryOS 开发环境..."
echo ""
echo "⚠️  警告: 这将删除以下内容:"
echo "  📦 所有容器"
echo "  💾 所有数据卷 (数据库数据、缓存等)"
echo "  🖼️  构建的镜像"
echo "  🌐 网络配置"
echo ""
echo "💡 注意: 这不会删除源代码文件"
echo ""

read -p "确定要继续清理吗？(y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 取消清理"
    exit 0
fi

echo ""
echo "🛑 停止所有容器..."
docker-compose -f docker-compose.independent.yml down

echo "🗑️  删除容器和网络..."
docker-compose -f docker-compose.independent.yml down --remove-orphans

echo "🧹 删除数据卷..."
docker volume rm masteryos-postgres-dev-data 2>/dev/null && echo "  ✅ 删除 PostgreSQL 数据卷" || echo "  ⚠️  PostgreSQL 数据卷不存在或已删除"
docker volume rm masteryos-redis-dev-data 2>/dev/null && echo "  ✅ 删除 Redis 数据卷" || echo "  ⚠️  Redis 数据卷不存在或已删除"
docker volume rm masteryos-minio-dev-data 2>/dev/null && echo "  ✅ 删除 MinIO 数据卷" || echo "  ⚠️  MinIO 数据卷不存在或已删除"
docker volume rm masteryos-docling-dev-cache 2>/dev/null && echo "  ✅ 删除 Docling 缓存卷" || echo "  ⚠️  Docling 缓存卷不存在或已删除"
docker volume rm masteryos-node-modules-cache 2>/dev/null && echo "  ✅ 删除 Node.js 模块缓存卷" || echo "  ⚠️  Node.js 模块缓存卷不存在或已删除"
docker volume rm masteryos-pnpm-cache 2>/dev/null && echo "  ✅ 删除 pnpm 缓存卷" || echo "  ⚠️  pnpm 缓存卷不存在或已删除"

echo "🖼️  删除构建的镜像..."
docker rmi 1w-dev 2>/dev/null && echo "  ✅ 删除开发镜像" || echo "  ⚠️  开发镜像不存在或已删除"

echo "🌐 删除网络..."
docker network rm masteryos-dev-network 2>/dev/null && echo "  ✅ 删除开发网络" || echo "  ⚠️  开发网络不存在或已删除"

echo "🧼 清理悬空资源..."
docker system prune -f > /dev/null 2>&1

echo ""
echo "✅ 清理完成！"
echo ""
echo "📊 清理结果:"
echo "  🗑️  所有开发容器已删除"
echo "  💾 所有数据卷已删除"
echo "  🖼️  构建镜像已删除"
echo "  🌐 网络配置已删除"
echo ""
echo "🚀 重新开始:"
echo "  ./scripts/dev-start.sh  # 重新创建开发环境"
echo ""
echo "💡 提示: 源代码文件未受影响，重新启动后需要重新安装依赖"