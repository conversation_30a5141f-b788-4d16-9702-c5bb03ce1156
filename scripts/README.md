# MasteryOS 开发脚本说明

> 📅 更新日期：2025年1月14日

## 🔄 脚本演进说明

### 旧版本 (已弃用)
- `setup-dev.sh.backup` - 基于本地安装的开发环境设置脚本 ❌

### 新版本 (推荐使用)
基于完全容器化的独立开发环境：

| 脚本 | 功能 | 使用场景 |
|------|------|----------|
| `dev-start.sh` | 🚀 启动完整开发环境 | 开始开发时执行 |
| `dev-enter.sh` | 🐳 进入开发容器 | 需要在容器内操作时 |
| `dev-status.sh` | 📊 检查环境状态 | 排查问题或查看状态 |
| `dev-logs.sh` | 📋 查看服务日志 | 调试和监控 |
| `dev-stop.sh` | 🛑 停止开发环境 | 停止开发时执行 |
| `dev-clean.sh` | 🧹 清理开发环境 | 重置环境或清理资源 |

## 🚀 快速开始

```bash
# 1. 启动开发环境
./scripts/dev-start.sh

# 2. 进入开发容器
./scripts/dev-enter.sh

# 3. 在容器内安装依赖
pnpm install

# 4. 开始开发
pnpm run dev
```

## 📊 常用操作

```bash
# 检查环境状态
./scripts/dev-status.sh

# 查看所有服务日志
./scripts/dev-logs.sh

# 查看特定服务日志
./scripts/dev-logs.sh postgres

# 停止环境
./scripts/dev-stop.sh

# 完全清理环境（删除所有数据）
./scripts/dev-clean.sh
```

## 🔧 故障排除

### 问题：容器启动失败
```bash
# 查看详细日志
./scripts/dev-logs.sh

# 重新构建并启动
./scripts/dev-stop.sh
./scripts/dev-clean.sh
./scripts/dev-start.sh
```

### 问题：端口冲突
```bash
# 检查端口占用
./scripts/dev-status.sh

# 停止冲突服务
docker ps
docker stop <容器名称>
```

### 问题：数据库连接失败
```bash
# 检查数据库状态
./scripts/dev-logs.sh postgres

# 重启数据库
./scripts/dev-stop.sh
./scripts/dev-start.sh
```

## ⚡ 性能优化

### 容器资源限制
编辑 `docker-compose.independent.yml` 中的资源配置：

```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
```

### 缓存优化
- Node.js 模块缓存：`masteryos-node-modules-cache`
- pnpm 缓存：`masteryos-pnpm-cache`
- Docling 缓存：`masteryos-docling-dev-cache`

## 📝 开发流程

1. **每日开始**：`./scripts/dev-start.sh`
2. **开发编码**：`./scripts/dev-enter.sh` → 容器内工作
3. **调试问题**：`./scripts/dev-logs.sh [service]`
4. **每日结束**：`./scripts/dev-stop.sh`
5. **重置环境**：`./scripts/dev-clean.sh` (仅在必要时)

## 🎯 最佳实践

- ✅ 始终使用新的脚本体系
- ✅ 定期备份重要开发数据
- ✅ 监控容器资源使用情况
- ✅ 使用 `dev-status.sh` 排查问题
- ❌ 不要直接修改容器内系统配置
- ❌ 不要在容器外安装项目依赖