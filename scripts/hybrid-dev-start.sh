#!/bin/bash

echo "🚀 启动 MasteryOS 混合架构开发环境"
echo "======================================"
echo "📅 环境版本: 2025年7月"
echo "🏗️  架构模式: Mobile BFF + Admin BFF + Admin SPA"
echo ""

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker Desktop"
    exit 1
fi

# 检查必要的目录结构
echo "🔍 检查项目结构..."
required_dirs=("mobile-bff" "admin-bff" "admin-spa" "flutter-app")
missing_dirs=()

for dir in "${required_dirs[@]}"; do
    if [ ! -d "$dir" ]; then
        missing_dirs+=("$dir")
    fi
done

if [ ${#missing_dirs[@]} -gt 0 ]; then
    echo "⚠️  缺少以下目录结构，将创建基础目录:"
    for dir in "${missing_dirs[@]}"; do
        echo "   📁 $dir"
        mkdir -p "$dir"
    done
    echo ""
    echo "💡 提示: 请按照架构文档创建各服务的代码结构"
fi

# 创建配置文件
echo "📝 创建开发配置文件..."
mkdir -p config scripts

# Redis 配置
cat > config/redis.conf << 'EOF'
# Redis 开发环境配置
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
EOF

# 创建数据库初始化脚本
cat > scripts/init-hybrid-db.sql << 'EOF'
-- MasteryOS 混合架构数据库初始化脚本
-- 2025年7月版本

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建组织表 (多租户核心)
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    role VARCHAR(50) DEFAULT 'learner' CHECK (role IN ('learner', 'trainer', 'admin', 'super_admin')),
    department VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    metadata JSONB DEFAULT '{}',
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建技能表
CREATE TABLE IF NOT EXISTS skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    target_hours INTEGER DEFAULT 10000,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档表
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    page_count INTEGER,
    processing_status VARCHAR(50) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    processed_content TEXT,
    metadata JSONB DEFAULT '{}',
    uploaded_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档块表 (向量搜索)
CREATE TABLE IF NOT EXISTS document_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI embeddings
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建学习路径表
CREATE TABLE IF NOT EXISTS learning_paths (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    skills UUID[] DEFAULT '{}', -- 关联的技能ID数组
    documents UUID[] DEFAULT '{}', -- 关联的文档ID数组
    estimated_hours INTEGER,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    is_required BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建学习记录表
CREATE TABLE IF NOT EXISTS learning_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    skill_id UUID REFERENCES skills(id) ON DELETE SET NULL,
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    learning_path_id UUID REFERENCES learning_paths(id) ON DELETE SET NULL,
    duration_minutes INTEGER NOT NULL,
    progress_data JSONB DEFAULT '{}',
    notes TEXT,
    session_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档标注表
CREATE TABLE IF NOT EXISTS document_annotations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    page_number INTEGER NOT NULL,
    annotation_type VARCHAR(50) NOT NULL CHECK (annotation_type IN ('highlight', 'note', 'ink', 'bookmark')),
    coordinates JSONB, -- 标注位置信息
    content TEXT,
    style_data JSONB DEFAULT '{}', -- 颜色、字体等样式
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建任务队列表
CREATE TABLE IF NOT EXISTS job_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(100) NOT NULL,
    payload JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    error_message TEXT,
    scheduled_for TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建必要的索引
CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_skills_organization_id ON skills(organization_id);
CREATE INDEX IF NOT EXISTS idx_documents_organization_id ON documents(organization_id);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(processing_status);
CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_learning_paths_organization_id ON learning_paths(organization_id);
CREATE INDEX IF NOT EXISTS idx_learning_sessions_user_id ON learning_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_sessions_organization_id ON learning_sessions(organization_id);
CREATE INDEX IF NOT EXISTS idx_learning_sessions_date ON learning_sessions(session_date);
CREATE INDEX IF NOT EXISTS idx_annotations_document_id ON document_annotations(document_id);
CREATE INDEX IF NOT EXISTS idx_annotations_user_id ON document_annotations(user_id);
CREATE INDEX IF NOT EXISTS idx_job_queue_status ON job_queue(status);
CREATE INDEX IF NOT EXISTS idx_job_queue_scheduled ON job_queue(scheduled_for);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
DROP TRIGGER IF EXISTS update_organizations_updated_at ON organizations;
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_skills_updated_at ON skills;
CREATE TRIGGER update_skills_updated_at BEFORE UPDATE ON skills FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_documents_updated_at ON documents;
CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_learning_paths_updated_at ON learning_paths;
CREATE TRIGGER update_learning_paths_updated_at BEFORE UPDATE ON learning_paths FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_annotations_updated_at ON document_annotations;
CREATE TRIGGER update_annotations_updated_at BEFORE UPDATE ON document_annotations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;
EOF

# 创建演示数据脚本
cat > scripts/seed-demo-data.sql << 'EOF'
-- 演示数据插入脚本
-- 仅在开发环境使用

-- 插入演示组织
INSERT INTO organizations (id, name, slug) VALUES 
('00000000-0000-0000-0000-000000000001', '演示企业', 'demo-corp'),
('00000000-0000-0000-0000-000000000002', '测试公司', 'test-company')
ON CONFLICT (id) DO NOTHING;

-- 插入演示用户
INSERT INTO users (id, organization_id, email, password_hash, name, role, department) VALUES 
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '<EMAIL>', '$2b$10$demo.hash.placeholder', '管理员', 'admin', '管理部'),
('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', '<EMAIL>', '$2b$10$demo.hash.placeholder', '培训师', 'trainer', '培训部'),
('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000001', '<EMAIL>', '$2b$10$demo.hash.placeholder', '学习者', 'learner', '技术部')
ON CONFLICT (email) DO NOTHING;

-- 插入演示技能
INSERT INTO skills (id, organization_id, name, description, category, target_hours) VALUES 
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Flutter开发', '学习Flutter移动应用开发技能', '技术', 10000),
('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', '项目管理', '掌握项目管理方法和工具', '管理', 5000)
ON CONFLICT (id) DO NOTHING;

COMMIT;
EOF

echo "✅ 配置文件创建完成"

# 启动选项
echo "🛠️  选择启动模式:"
echo "1) 🚀 完整环境 (推荐)"
echo "2) 🔧 调试模式 (包含管理工具)"
echo "3) 📱 仅移动端开发"
echo "4) 🌐 仅Web端开发"
echo "5) 🤖 包含本地AI服务"

read -p "请选择启动模式 (1-5): " choice

case $choice in
    1)
        echo "🚀 启动完整开发环境..."
        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml up -d
        ;;
    2)
        echo "🔧 启动调试模式 (包含Redis Commander + pgAdmin)..."
        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml --profile debug-tools up -d
        ;;
    3)
        echo "📱 启动移动端开发环境..."
        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml up -d db redis minio mobile-bff
        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml --profile flutter-dev up -d flutter-dev
        ;;
    4)
        echo "🌐 启动Web端开发环境..."
        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml up -d db db-readonly redis minio docling admin-bff admin-spa
        ;;
    5)
        echo "🤖 启动包含本地AI的完整环境..."
        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml --profile ai-local up -d
        ;;
    *)
        echo "❌ 无效选择，启动默认完整环境..."
        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml up -d
        ;;
esac

echo ""
echo "⏳ 等待服务启动..."
sleep 15

echo ""
echo "🔍 检查服务状态..."
docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml ps

echo ""
echo "✅ MasteryOS 混合架构开发环境启动完成！"
echo ""
echo "🌐 Web 服务访问地址:"
echo "  管理后台:        http://localhost:3100"
echo "  移动端API:       http://localhost:3101"
echo "  管理端API:       http://localhost:3102"
echo "  MinIO控制台:     http://localhost:9101 (minioadmin/minioadmin123)"
echo "  Docling服务:     http://localhost:8080"

if [[ $choice == "2" ]]; then
echo "  Redis Commander: http://localhost:8081"
echo "  pgAdmin:         http://localhost:8182 (<EMAIL>/admin123)"
fi

if [[ $choice == "5" ]]; then
echo "  本地AI服务:      http://localhost:11434"
fi

echo ""
echo "🗄️  数据库连接信息:"
echo "  主数据库:        localhost:5432 (postgres/password/masteryos)"
echo "  Redis:           localhost:6379"
echo ""
echo "🛠️  开发命令:"
echo "  查看日志:        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml logs -f [服务名]"
echo "  重启服务:        docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml restart [服务名]"
echo "  停止环境:        ./scripts/hybrid-dev-stop.sh"
echo "  清理环境:        ./scripts/hybrid-dev-clean.sh"
echo ""
echo "📚 下一步:"
echo "  1. 根据架构文档创建各服务的代码结构"
echo "  2. 配置IDE连接到数据库和Redis"
echo "  3. 开始开发移动端和管理端功能"