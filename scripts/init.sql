-- =================================
-- MasteryOS 数据库初始化脚本
-- =================================

-- 创建数据库和用户（如果不存在）
DO $$
BEGIN
    -- 创建用户（如果不存在）
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'masteryos') THEN
        CREATE USER masteryos WITH PASSWORD 'masteryos_prod_password';
    END IF;
    
    -- 授予权限
    GRANT ALL PRIVILEGES ON DATABASE masteryos TO masteryos;
    GRANT ALL PRIVILEGES ON SCHEMA public TO masteryos;
    ALTER USER masteryos CREATEDB;
END
$$;

-- 连接到masteryos数据库
\c masteryos;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    avatar_url TEXT,
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'USER' CHECK (role IN ('ADMIN', 'MANAGER', 'USER')),
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING')),
    email_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 创建文档表
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL DEFAULT 0,
    mime_type VARCHAR(100),
    type VARCHAR(50) DEFAULT 'OTHER' CHECK (type IN ('CONTRACT', 'REPORT', 'MANUAL', 'PRESENTATION', 'SPREADSHEET', 'IMAGE', 'VIDEO', 'AUDIO', 'OTHER')),
    status VARCHAR(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED', 'DELETED')),
    access_level VARCHAR(20) DEFAULT 'PRIVATE' CHECK (access_level IN ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'PRIVATE')),
    tags TEXT[],
    metadata JSONB DEFAULT '{}',
    version INTEGER DEFAULT 1,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档索引
CREATE INDEX IF NOT EXISTS idx_documents_title ON documents(title);
CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(type);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_access_level ON documents(access_level);
CREATE INDEX IF NOT EXISTS idx_documents_created_by ON documents(created_by);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);
CREATE INDEX IF NOT EXISTS idx_documents_tags ON documents USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_documents_metadata ON documents USING GIN(metadata);

-- 创建分析事件表
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('USER_LOGIN', 'USER_LOGOUT', 'DOCUMENT_VIEW', 'DOCUMENT_DOWNLOAD', 'DOCUMENT_UPLOAD', 'PAGE_VIEW', 'SEARCH', 'ERROR', 'CUSTOM')),
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建分析事件索引
CREATE INDEX IF NOT EXISTS idx_analytics_events_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);

-- 创建每日统计表
CREATE TABLE IF NOT EXISTS analytics_daily_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    total_documents INTEGER DEFAULT 0,
    new_documents INTEGER DEFAULT 0,
    total_page_views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date)
);

-- 创建每日统计索引
CREATE INDEX IF NOT EXISTS idx_analytics_daily_stats_date ON analytics_daily_stats(date);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建更新时间触发器
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_documents_updated_at ON documents;
CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_analytics_daily_stats_updated_at ON analytics_daily_stats;
CREATE TRIGGER update_analytics_daily_stats_updated_at 
    BEFORE UPDATE ON analytics_daily_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入管理员用户（默认密码: admin123）
INSERT INTO users (
    username, 
    email, 
    password_hash, 
    first_name, 
    last_name, 
    role, 
    status,
    email_verified
) VALUES (
    'admin',
    '<EMAIL>',
    crypt('admin123', gen_salt('bf')),
    'System',
    'Administrator',
    'ADMIN',
    'ACTIVE',
    TRUE
) ON CONFLICT (username) DO NOTHING;

-- 插入测试用户
INSERT INTO users (
    username, 
    email, 
    password_hash, 
    first_name, 
    last_name, 
    role, 
    status,
    email_verified
) VALUES 
(
    'manager',
    '<EMAIL>',
    crypt('manager123', gen_salt('bf')),
    'Test',
    'Manager',
    'MANAGER',
    'ACTIVE',
    TRUE
),
(
    'user',
    '<EMAIL>',
    crypt('user123', gen_salt('bf')),
    'Test',
    'User',
    'USER',
    'ACTIVE',
    TRUE
) ON CONFLICT (username) DO NOTHING;

-- 插入示例文档
INSERT INTO documents (
    title,
    description,
    file_name,
    file_path,
    file_size,
    mime_type,
    type,
    status,
    access_level,
    tags,
    created_by,
    updated_by
) VALUES 
(
    '系统使用手册',
    'MasteryOS系统的详细使用说明文档',
    'system-manual.pdf',
    '/documents/system-manual.pdf',
    2048576,
    'application/pdf',
    'MANUAL',
    'PUBLISHED',
    'INTERNAL',
    ARRAY['manual', 'system', 'guide'],
    (SELECT id FROM users WHERE username = 'admin'),
    (SELECT id FROM users WHERE username = 'admin')
),
(
    '项目需求文档',
    '项目的详细需求说明和技术规格',
    'project-requirements.docx',
    '/documents/project-requirements.docx',
    1024000,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'REPORT',
    'PUBLISHED',
    'CONFIDENTIAL',
    ARRAY['requirements', 'project', 'specs'],
    (SELECT id FROM users WHERE username = 'admin'),
    (SELECT id FROM users WHERE username = 'admin')
),
(
    '数据分析报告',
    '2024年第一季度数据分析报告',
    'q1-2024-analytics.xlsx',
    '/documents/q1-2024-analytics.xlsx',
    512000,
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'SPREADSHEET',
    'PUBLISHED',
    'INTERNAL',
    ARRAY['analytics', 'report', '2024', 'q1'],
    (SELECT id FROM users WHERE username = 'manager'),
    (SELECT id FROM users WHERE username = 'manager')
) ON CONFLICT DO NOTHING;

-- 插入示例分析事件
DO $$
DECLARE
    admin_id UUID;
    manager_id UUID;
    user_id UUID;
    i INTEGER;
BEGIN
    -- 获取用户ID
    SELECT id INTO admin_id FROM users WHERE username = 'admin';
    SELECT id INTO manager_id FROM users WHERE username = 'manager';
    SELECT id INTO user_id FROM users WHERE username = 'user';
    
    -- 插入过去30天的示例事件
    FOR i IN 1..30 LOOP
        -- 登录事件
        INSERT INTO analytics_events (event_type, user_id, session_id, ip_address, created_at)
        VALUES 
        ('USER_LOGIN', admin_id, 'session_admin_' || i, '*************', CURRENT_TIMESTAMP - INTERVAL '1 day' * i),
        ('USER_LOGIN', manager_id, 'session_manager_' || i, '*************', CURRENT_TIMESTAMP - INTERVAL '1 day' * i),
        ('USER_LOGIN', user_id, 'session_user_' || i, '*************', CURRENT_TIMESTAMP - INTERVAL '1 day' * i);
        
        -- 页面浏览事件
        INSERT INTO analytics_events (event_type, user_id, session_id, metadata, created_at)
        VALUES 
        ('PAGE_VIEW', admin_id, 'session_admin_' || i, '{"page": "/dashboard"}', CURRENT_TIMESTAMP - INTERVAL '1 day' * i),
        ('PAGE_VIEW', manager_id, 'session_manager_' || i, '{"page": "/users"}', CURRENT_TIMESTAMP - INTERVAL '1 day' * i),
        ('PAGE_VIEW', user_id, 'session_user_' || i, '{"page": "/documents"}', CURRENT_TIMESTAMP - INTERVAL '1 day' * i);
        
        -- 文档查看事件
        INSERT INTO analytics_events (event_type, user_id, metadata, created_at)
        VALUES 
        ('DOCUMENT_VIEW', admin_id, '{"document_id": "' || (SELECT id FROM documents LIMIT 1) || '"}', CURRENT_TIMESTAMP - INTERVAL '1 day' * i);
    END LOOP;
END $$;

-- 初始化每日统计数据
DO $$
DECLARE
    i INTEGER;
    current_date DATE;
BEGIN
    -- 生成过去30天的统计数据
    FOR i IN 0..29 LOOP
        current_date := CURRENT_DATE - INTERVAL '1 day' * i;
        
        INSERT INTO analytics_daily_stats (
            date,
            total_users,
            active_users,
            new_users,
            total_documents,
            new_documents,
            total_page_views,
            unique_visitors
        ) VALUES (
            current_date,
            3, -- 总用户数
            3, -- 活跃用户数
            CASE WHEN i = 29 THEN 3 ELSE 0 END, -- 新用户数（只在第一天）
            3, -- 总文档数
            CASE WHEN i = 29 THEN 3 ELSE 0 END, -- 新文档数（只在第一天）
            9, -- 页面浏览数
            3  -- 独立访客数
        ) ON CONFLICT (date) DO NOTHING;
    END LOOP;
END $$;

-- 创建数据库清理函数（清理旧的分析事件）
CREATE OR REPLACE FUNCTION cleanup_old_analytics_events(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM analytics_events 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建统计视图
CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE status = 'ACTIVE') as active_users,
    COUNT(*) FILTER (WHERE role = 'ADMIN') as admin_users,
    COUNT(*) FILTER (WHERE role = 'MANAGER') as manager_users,
    COUNT(*) FILTER (WHERE role = 'USER') as regular_users,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d,
    COUNT(*) FILTER (WHERE last_login_at >= CURRENT_DATE - INTERVAL '7 days') as active_users_7d
FROM users;

CREATE OR REPLACE VIEW document_statistics AS
SELECT 
    COUNT(*) as total_documents,
    COUNT(*) FILTER (WHERE status = 'PUBLISHED') as published_documents,
    COUNT(*) FILTER (WHERE status = 'DRAFT') as draft_documents,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_documents_30d,
    SUM(file_size) as total_file_size,
    AVG(file_size) as avg_file_size
FROM documents;

-- 授予权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO masteryos;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO masteryos;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO masteryos;

-- 完成消息
DO $$
BEGIN
    RAISE NOTICE '====================================';
    RAISE NOTICE 'MasteryOS 数据库初始化完成！';
    RAISE NOTICE '====================================';
    RAISE NOTICE '默认管理员账户:';
    RAISE NOTICE '  用户名: admin';
    RAISE NOTICE '  密码: admin123';
    RAISE NOTICE '  邮箱: <EMAIL>';
    RAISE NOTICE '';
    RAISE NOTICE '测试账户:';
    RAISE NOTICE '  管理员: manager / manager123';
    RAISE NOTICE '  普通用户: user / user123';
    RAISE NOTICE '====================================';
END $$;