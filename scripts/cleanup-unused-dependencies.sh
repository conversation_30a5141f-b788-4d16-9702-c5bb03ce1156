#!/bin/bash

# MasteryOS 项目依赖清理脚本
# 移除已被 Flutter Web 替代的 React Admin SPA

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🧹 MasteryOS 项目依赖清理${NC}"
echo -e "${BLUE}==============================${NC}"

# 检查 Flutter Web 管理后台是否已实现
if [ ! -d "$PROJECT_ROOT/apps/admin-web" ]; then
    echo -e "${RED}❌ Flutter Web 管理后台未找到，请先完成迁移后再执行清理${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 清理计划:${NC}"
echo "  1. 备份 React Admin SPA"
echo "  2. 移除 admin-spa 目录"
echo "  3. 更新 workspace 配置"
echo "  4. 清理启动脚本"
echo "  5. 重新安装依赖"
echo

read -p "确认继续？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "取消清理操作"
    exit 0
fi

# 1. 备份 React Admin SPA（如果存在）
if [ -d "$PROJECT_ROOT/apps/admin-spa" ]; then
    echo -e "${YELLOW}📦 备份 React Admin SPA...${NC}"
    BACKUP_DIR="$PROJECT_ROOT/.archive/admin-spa-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp -r "$PROJECT_ROOT/apps/admin-spa" "$BACKUP_DIR/"
    echo -e "${GREEN}✅ 备份完成: $BACKUP_DIR${NC}"
fi

# 2. 移除 admin-spa 目录
if [ -d "$PROJECT_ROOT/apps/admin-spa" ]; then
    echo -e "${YELLOW}🗑️  移除 React Admin SPA...${NC}"
    rm -rf "$PROJECT_ROOT/apps/admin-spa"
    echo -e "${GREEN}✅ admin-spa 目录已删除${NC}"
fi

# 3. 更新 pnpm-workspace.yaml
echo -e "${YELLOW}📝 更新 workspace 配置...${NC}"
if [ -f "$PROJECT_ROOT/pnpm-workspace.yaml" ]; then
    # 备份原文件
    cp "$PROJECT_ROOT/pnpm-workspace.yaml" "$PROJECT_ROOT/pnpm-workspace.yaml.backup"
    
    # 移除 admin-spa 引用
    sed -i '' '/admin-spa/d' "$PROJECT_ROOT/pnpm-workspace.yaml"
    echo -e "${GREEN}✅ pnpm-workspace.yaml 已更新${NC}"
fi

# 4. 清理 package.json 脚本
echo -e "${YELLOW}📝 更新启动脚本...${NC}"
if [ -f "$PROJECT_ROOT/package.json" ]; then
    # 备份原文件
    cp "$PROJECT_ROOT/package.json" "$PROJECT_ROOT/package.json.backup"
    
    # 更新 dev:status 脚本，移除 Admin SPA 引用
    sed -i '' 's/Admin SPA: http:\/\/localhost:3100.*$//' "$PROJECT_ROOT/package.json"
    
    echo -e "${GREEN}✅ package.json 脚本已更新${NC}"
fi

# 5. 更新启动脚本
echo -e "${YELLOW}📝 更新启动脚本...${NC}"
if [ -f "$PROJECT_ROOT/scripts/start-all-services.sh" ]; then
    # 备份原文件
    cp "$PROJECT_ROOT/scripts/start-all-services.sh" "$PROJECT_ROOT/scripts/start-all-services.sh.backup"
    
    # 注释掉 Admin SPA 相关部分
    sed -i '' 's/.*Admin SPA.*/# &/' "$PROJECT_ROOT/scripts/start-all-services.sh"
    sed -i '' 's/.*admin-spa.*/# &/' "$PROJECT_ROOT/scripts/start-all-services.sh"
    
    echo -e "${GREEN}✅ 启动脚本已更新${NC}"
fi

# 6. 重新安装依赖
echo -e "${YELLOW}📦 重新安装依赖...${NC}"
cd "$PROJECT_ROOT"
pnpm install

# 7. 显示结果
echo
echo -e "${GREEN}🎉 依赖清理完成！${NC}"
echo
echo -e "${BLUE}📊 清理结果:${NC}"
echo "  ✅ React Admin SPA 已移除"
echo "  ✅ workspace 配置已更新"
echo "  ✅ 启动脚本已更新"
echo "  ✅ 依赖已重新安装"
echo
echo -e "${YELLOW}💡 下一步:${NC}"
echo "  1. 测试 Flutter Web 管理后台: ./scripts/flutter-admin-web-start.sh"
echo "  2. 测试 NestJS API 服务: pnpm run dev"
echo "  3. 确认功能正常后可删除备份文件"
echo
echo -e "${BLUE}📁 备份位置:${NC}"
if [ -d "$BACKUP_DIR" ]; then
    echo "  $BACKUP_DIR"
fi
echo "  $PROJECT_ROOT/pnpm-workspace.yaml.backup"
echo "  $PROJECT_ROOT/package.json.backup"
echo "  $PROJECT_ROOT/scripts/start-all-services.sh.backup"