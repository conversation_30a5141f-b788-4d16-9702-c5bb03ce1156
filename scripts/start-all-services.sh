#!/bin/bash

# MasteryOS 一键启动所有服务脚本
# 作者: MasteryOS Team
# 日期: 2025-08-04

set -e

# 优雅退出处理
cleanup() {
    echo -e "\n${YELLOW}🛑 正在停止所有服务...${NC}"
    
    # 停止后台进程
    for pidfile in /tmp/mobile-bff.pid /tmp/admin-bff.pid /tmp/flutter-web.pid; do
        if [ -f "$pidfile" ]; then
            pid=$(cat "$pidfile")
            if kill -0 "$pid" 2>/dev/null; then
                echo "停止进程 $pid"
                kill "$pid" 2>/dev/null || true
                rm "$pidfile" 2>/dev/null || true
            fi
        fi
    done
    
    echo -e "${GREEN}✅ 所有服务已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🚀 MasteryOS 一键启动脚本${NC}"
echo -e "${BLUE}================================${NC}"

# 函数: 检测端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 函数: 停止已存在的项目服务
stop_existing_services() {
    echo -e "${YELLOW}🔍 检查并停止已存在的服务...${NC}"
    
    # 停止之前的进程
    for pidfile in /tmp/mobile-bff.pid /tmp/admin-bff.pid /tmp/flutter-web.pid; do
        if [ -f "$pidfile" ]; then
            pid=$(cat "$pidfile")
            if kill -0 "$pid" 2>/dev/null; then
                service_name=$(basename "$pidfile" .pid)
                echo -e "${BLUE}停止已存在的 $service_name 服务 (PID: $pid)${NC}"
                kill "$pid" 2>/dev/null || true
                # 等待进程完全停止
                sleep 1
            fi
            rm "$pidfile" 2>/dev/null || true
        fi
    done
    
    # 检查并终止占用项目端口的进程
    for port in 3101 3102 8080; do
        if check_port $port; then
            pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
            if [ -n "$pid" ]; then
                echo -e "${BLUE}发现端口 $port 被进程 $pid 占用，正在停止...${NC}"
                kill "$pid" 2>/dev/null || true
                sleep 1
            fi
        fi
    done
    
    echo -e "${GREEN}✅ 清理已存在的服务完成${NC}"
}

# 函数: 启动数据库服务
start_database() {
    echo -e "${YELLOW}📊 检查数据库服务状态...${NC}"
    
    cd "$PROJECT_ROOT"
    
    # 检查数据库容器状态
    if ! docker ps | grep -q "1w-postgres"; then
        echo -e "${YELLOW}🔧 启动数据库服务...${NC}"
        ./scripts/1w-db.sh start
        sleep 3
    else
        echo -e "${GREEN}✅ 数据库服务已运行${NC}"
    fi
}

# 函数: 启动后端服务
start_backend() {
    echo -e "${YELLOW}🖥️  启动后端服务...${NC}"
    
    # 启动 Mobile BFF
    cd "$PROJECT_ROOT/apps/mobile-bff"
    echo -e "${BLUE}启动 Mobile BFF (端口 3101)...${NC}"
    pnpm run start:dev > /tmp/mobile-bff.log 2>&1 &
    echo $! > /tmp/mobile-bff.pid
    
    # 启动 Admin BFF
    cd "$PROJECT_ROOT/apps/admin-bff"
    echo -e "${BLUE}启动 Admin BFF (端口 3102)...${NC}"
    pnpm run start:dev > /tmp/admin-bff.log 2>&1 &
    echo $! > /tmp/admin-bff.pid
}

# 函数: 启动前端服务
start_frontend() {
    echo -e "${YELLOW}🎨 启动前端服务...${NC}"
    
    # 注意：React Admin SPA 已被 Flutter Web 管理后台替代
    # 如需启动 Flutter Web 管理后台，请使用: ./scripts/flutter-admin-web-start.sh
    
    # 启动 Flutter Web
    cd "$PROJECT_ROOT/apps/mobile"
    echo -e "${BLUE}启动 Flutter Web (端口 8080)...${NC}"
    fvm flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0 > /tmp/flutter-web.log 2>&1 &
    echo $! > /tmp/flutter-web.pid
}

# 函数: 等待服务启动
wait_for_services() {
    echo -e "${YELLOW}⏳ 等待服务启动完成...${NC}"
    sleep 8
    
    echo -e "${BLUE}📋 服务状态检查:${NC}"
    echo "================================"
    
    # 检查数据库
    if docker ps | grep -q "1w-postgres" && docker ps | grep "1w-postgres" | grep -q "healthy"; then
        echo -e "${GREEN}✅ PostgreSQL: 正常运行 (端口 8182)${NC}"
    else
        echo -e "${RED}❌ PostgreSQL: 启动失败${NC}"
    fi
    
    if docker ps | grep -q "1w-redis" && docker ps | grep "1w-redis" | grep -q "healthy"; then
        echo -e "${GREEN}✅ Redis: 正常运行 (端口 8183)${NC}"
    else
        echo -e "${RED}❌ Redis: 启动失败${NC}"
    fi
    
    # 检查后端服务
    if check_port 3101; then
        echo -e "${GREEN}✅ Mobile BFF: 正常运行 (端口 3101)${NC}"
    else
        echo -e "${RED}❌ Mobile BFF: 启动失败${NC}"
    fi
    
    if check_port 3102; then
        echo -e "${GREEN}✅ Admin BFF: 正常运行 (端口 3102)${NC}"
    else
        echo -e "${RED}❌ Admin BFF: 启动失败${NC}"
    fi
    
    # 注意：Admin SPA 已被 Flutter Web 管理后台替代
    echo -e "${YELLOW}💡 Flutter Web 管理后台: 使用 ./scripts/flutter-admin-web-start.sh 启动${NC}"
    
    if check_port 8080; then
        echo -e "${GREEN}✅ Flutter Web: 正常运行 (端口 8080)${NC}"
    else
        echo -e "${RED}❌ Flutter Web: 启动失败${NC}"
    fi
}

# 函数: 显示访问地址
show_urls() {
    echo
    echo -e "${BLUE}🌐 服务访问地址:${NC}"
    echo "================================"
    echo -e "${GREEN}📱 Flutter Web:${NC}     http://localhost:8080"
    echo -e "${GREEN}🖥️  Flutter Admin Web:${NC} http://localhost:3200 (需单独启动)"
    echo -e "${GREEN}📡 Mobile BFF API:${NC}   http://localhost:3101"
    echo -e "${GREEN}📡 Admin BFF API:${NC}    http://localhost:3102"
    echo -e "${GREEN}📚 API 文档:${NC}"
    echo "   - Mobile BFF: http://localhost:3101/docs"
    echo "   - Admin BFF:  http://localhost:3102/docs"
    echo -e "${GREEN}🗄️  数据库:${NC}"
    echo "   - PostgreSQL: localhost:8182"
    echo "   - Redis:      localhost:8183"
}

# 函数: 创建停止脚本
create_stop_script() {
    cat > "$PROJECT_ROOT/scripts/stop-all-services.sh" << 'EOF'
#!/bin/bash

# MasteryOS 停止所有服务脚本

echo "🛑 停止所有 MasteryOS 服务..."

# 停止前端和后端进程
for pidfile in /tmp/mobile-bff.pid /tmp/admin-bff.pid /tmp/flutter-web.pid; do
    if [ -f "$pidfile" ]; then
        pid=$(cat "$pidfile")
        if kill -0 "$pid" 2>/dev/null; then
            echo "停止进程 $pid"
            kill "$pid"
            rm "$pidfile"
        fi
    fi
done

# 停止数据库服务
cd "$(dirname "${BASH_SOURCE[0]}")/.."
./scripts/1w-db.sh stop

echo "✅ 所有服务已停止"
EOF
    chmod +x "$PROJECT_ROOT/scripts/stop-all-services.sh"
}

# 函数: 显示聚合日志
show_aggregated_logs() {
    # 创建日志文件标识符的前缀
    local log_files=()
    
    # 检查哪些日志文件存在并添加到数组中
    for service in mobile-bff admin-bff flutter-web; do
        if [ -f "/tmp/$service.log" ]; then
            log_files+=("/tmp/$service.log")
        fi
    done
    
    if [ ${#log_files[@]} -eq 0 ]; then
        echo -e "${RED}❌ 没有找到日志文件${NC}"
        while true; do sleep 1; done
    fi
    
    # 使用 tail -f 显示所有日志文件，并添加服务名称前缀
    {
        for logfile in "${log_files[@]}"; do
            service_name=$(basename "$logfile" .log)
            echo -e "${BLUE}[$service_name]${NC} 开始监听日志..."
        done
        echo "================================"
        
        # 使用 tail -f 同时监听多个日志文件
        tail -f "${log_files[@]}" | while IFS= read -r line; do
            # 检测是哪个服务的日志
            case "$line" in
                *"==> /tmp/mobile-bff.log <==" )
                    echo -e "\n${GREEN}[Mobile BFF]${NC} ================"
                    ;;
                *"==> /tmp/admin-bff.log <==" )
                    echo -e "\n${BLUE}[Admin BFF]${NC} ================"
                    ;;
                *"==> /tmp/flutter-web.log <==" )
                    echo -e "\n${CYAN}[Flutter Web]${NC} =============="
                    ;;
                "" )
                    # 跳过空行
                    ;;
                * )
                    echo "$line"
                    ;;
            esac
        done
    } 2>/dev/null
}

# 主执行流程
main() {
    # 检查必要命令
    for cmd in docker pnpm fvm; do
        if ! command -v $cmd &> /dev/null; then
            echo -e "${RED}❌ 命令 '$cmd' 未找到，请先安装${NC}"
            exit 1
        fi
    done
    
    # 清理已存在的服务
    stop_existing_services
    
    # 启动服务
    start_database
    start_backend
    start_frontend
    wait_for_services
    show_urls
    create_stop_script
    
    echo
    echo -e "${GREEN}🎉 所有服务启动完成！${NC}"
    echo -e "${BLUE}💡 提示:${NC}"
    echo "  - 按 Ctrl+C 停止所有服务并退出"
    echo "  - 数据库管理: ./scripts/1w-db.sh status|start|stop|connect"
    echo
    echo -e "${YELLOW}📊 服务正在运行中，日志输出如下：${NC}"
    echo "================================"
    
    # 等待日志文件创建
    sleep 2
    
    # 启动日志聚合显示
    show_aggregated_logs
}

# 执行主函数
main "$@"