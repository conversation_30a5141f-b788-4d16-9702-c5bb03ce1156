#!/bin/bash

echo "🚀 启动 MasteryOS 移动端开发环境"
echo "====================================="

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

echo "📦 启动后端服务 (API + 数据库)..."
docker-compose -f docker-compose.mobile-dev.yml up -d api db redis minio

echo "⏳ 等待服务启动..."
sleep 10

echo "🔍 检查服务状态..."
docker-compose -f docker-compose.mobile-dev.yml ps

echo ""
echo "✅ 后端服务启动完成！"
echo ""
echo "📱 启动Flutter开发环境："
echo "  docker-compose -f docker-compose.mobile-dev.yml --profile flutter-dev up flutter"
echo ""
echo "🔗 服务访问地址："
echo "  API服务:      http://localhost:3000"
echo "  数据库:       localhost:5432 (postgres/password)"
echo "  Redis:        localhost:6379"
echo "  MinIO控制台:  http://localhost:9001 (minioadmin/minioadmin)"
echo ""
echo "🛠️ 常用开发命令："
echo "  进入API容器:  docker-compose -f docker-compose.mobile-dev.yml exec api bash"
echo "  查看API日志:  docker-compose -f docker-compose.mobile-dev.yml logs -f api"
echo "  重启API:      docker-compose -f docker-compose.mobile-dev.yml restart api"
echo ""
echo "🎯 下一步："
echo "  1. 等待数据库初始化完成"
echo "  2. 创建Flutter项目结构"
echo "  3. 开始移动端开发"