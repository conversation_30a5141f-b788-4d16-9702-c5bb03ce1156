#!/bin/bash

# MasteryOS 停止所有服务脚本

echo "🛑 停止所有 MasteryOS 服务..."

# 停止前端和后端进程
for pidfile in /tmp/mobile-bff.pid /tmp/admin-bff.pid /tmp/admin-spa.pid /tmp/flutter-web.pid; do
    if [ -f "$pidfile" ]; then
        pid=$(cat "$pidfile")
        if kill -0 "$pid" 2>/dev/null; then
            echo "停止进程 $pid"
            kill "$pid"
            rm "$pidfile"
        fi
    fi
done

# 停止数据库服务
cd "$(dirname "${BASH_SOURCE[0]}")/.."
./scripts/1w-db.sh stop

echo "✅ 所有服务已停止"
