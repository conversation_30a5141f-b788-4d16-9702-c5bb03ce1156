#!/bin/bash

echo "🐳 进入 MasteryOS 开发容器..."

# 检查容器是否运行
if ! docker ps | grep -q masteryos-dev; then
    echo "❌ 开发容器未运行，请先执行 ./scripts/dev-start.sh"
    exit 1
fi

# 检查容器是否健康
if ! docker ps --format "table {{.Names}}\t{{.Status}}" | grep masteryos-dev | grep -q "Up"; then
    echo "⚠️  开发容器状态异常，正在检查..."
    docker-compose -f docker-compose.independent.yml logs dev | tail -20
    echo ""
    echo "请检查容器日志，或尝试重启：./scripts/dev-stop.sh && ./scripts/dev-start.sh"
    exit 1
fi

echo "✅ 正在连接到开发容器..."
echo ""
echo "📋 容器内可用命令："
echo "  pnpm install          # 安装依赖"
echo "  pnpm run dev          # 启动开发服务器"
echo "  pnpm run lint         # 代码检查"
echo "  pnpm run typecheck    # 类型检查"
echo "  psql -h postgres -U masteryos -d masteryos  # 连接数据库"
echo "  redis-cli -h redis -a masteryos123          # 连接Redis"
echo ""
echo "进入容器后，工作目录是 /workspace"
echo "============================================="

# 进入开发容器
docker exec -it masteryos-dev bash