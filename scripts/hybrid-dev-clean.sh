#!/bin/bash

echo "🧹 清理 MasteryOS 混合架构开发环境"
echo "====================================="
echo ""
echo "⚠️  警告: 这将删除以下内容:"
echo "  📦 所有相关容器"
echo "  💾 所有数据卷 (数据库数据、文件等)"
echo "  🖼️  构建的镜像"
echo "  🌐 网络配置"
echo ""
echo "💡 注意: 源代码文件不会被删除"
echo ""

read -p "确定要继续清理吗？(y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 取消清理操作"
    exit 0
fi

echo ""
echo "🛑 停止所有服务..."
docker-compose -f docker-compose.hybrid-dev.yml down --remove-orphans

echo ""
echo "🗑️  删除容器..."
# 强制删除所有相关容器
container_pattern="masteryos"
containers=$(docker ps -a --filter "name=$container_pattern" --format "{{.Names}}")
if [ -n "$containers" ]; then
    echo "$containers" | xargs docker rm -f
    echo "✅ 容器已删除"
else
    echo "ℹ️  没有找到相关容器"
fi

echo ""
echo "🧹 删除数据卷..."
# 删除项目相关的数据卷
volumes_to_delete=(
    "masteryos-hybrid-dev_postgres_data"
    "masteryos-hybrid-dev_postgres_readonly_data"
    "masteryos-hybrid-dev_redis_data"
    "masteryos-hybrid-dev_minio_data"
    "masteryos-hybrid-dev_docling_cache"
    "masteryos-hybrid-dev_ollama_data"
    "masteryos-hybrid-dev_mobile_node_modules"
    "masteryos-hybrid-dev_admin_node_modules"
    "masteryos-hybrid-dev_admin_spa_node_modules"
    "masteryos-hybrid-dev_flutter_cache"
    "masteryos-hybrid-dev_flutter_pub_cache"
    "masteryos-hybrid-dev_pgadmin_data"
)

for volume in "${volumes_to_delete[@]}"; do
    if docker volume ls --format "{{.Name}}" | grep -q "^$volume$"; then
        docker volume rm "$volume" 2>/dev/null && echo "  ✅ 删除 $volume" || echo "  ⚠️  $volume 删除失败或不存在"
    else
        echo "  ⚠️  $volume 不存在"
    fi
done

# 删除其他可能的数据卷
other_volumes=$(docker volume ls --filter "name=masteryos" --format "{{.Name}}")
if [ -n "$other_volumes" ]; then
    echo ""
    echo "🔍 发现其他相关数据卷:"
    echo "$other_volumes" | sed 's/^/   /'
    read -p "是否也要删除这些数据卷? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "$other_volumes" | xargs docker volume rm
        echo "✅ 其他数据卷已删除"
    fi
fi

echo ""
echo "🖼️  删除构建的镜像..."
# 删除项目构建的镜像
images_to_delete=(
    "masteryos-hybrid-dev-mobile-bff"
    "masteryos-hybrid-dev-admin-bff"
    "masteryos-hybrid-dev-admin-spa"
    "masteryos-hybrid-dev-flutter-dev"
)

for image in "${images_to_delete[@]}"; do
    if docker images --format "{{.Repository}}" | grep -q "^$image$"; then
        docker rmi "$image" 2>/dev/null && echo "  ✅ 删除 $image" || echo "  ⚠️  $image 删除失败"
    else
        echo "  ⚠️  $image 不存在"
    fi
done

echo ""
echo "🌐 删除网络..."
docker network rm masteryos-hybrid-dev 2>/dev/null && echo "  ✅ 删除 masteryos-hybrid-dev 网络" || echo "  ⚠️  masteryos-hybrid-dev 网络不存在或已删除"

echo ""
echo "🧼 清理悬空资源..."
docker system prune -f > /dev/null 2>&1

echo ""
echo "🧹 清理开发配置文件..."
config_files=(
    "config/redis.conf"
    "scripts/init-hybrid-db.sql"
    "scripts/seed-demo-data.sql"
)

for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        rm "$file" && echo "  ✅ 删除 $file"
    fi
done

# 清理空目录
if [ -d "config" ] && [ -z "$(ls -A config)" ]; then
    rmdir config && echo "  ✅ 删除空的 config 目录"
fi

echo ""
echo "✅ 清理完成！"
echo ""
echo "📊 清理结果:"
echo "  🗑️  所有开发容器已删除"
echo "  💾 所有数据卷已删除"
echo "  🖼️  构建镜像已删除"
echo "  🌐 网络配置已删除"
echo "  📝 配置文件已删除"
echo ""
echo "🚀 重新开始:"
echo "  ./scripts/hybrid-dev-start.sh  # 重新创建开发环境"
echo ""
echo "💡 提示: 源代码目录结构保持不变，重新启动后需要重新安装依赖"
echo ""
echo "📁 保留的目录结构:"
if [ -d "mobile-bff" ]; then echo "   📱 mobile-bff/"; fi
if [ -d "admin-bff" ]; then echo "   🔧 admin-bff/"; fi
if [ -d "admin-spa" ]; then echo "   🌐 admin-spa/"; fi
if [ -d "flutter-app" ]; then echo "   📱 flutter-app/"; fi