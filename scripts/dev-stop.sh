#!/bin/bash

echo "🛑 停止 MasteryOS 独立开发环境..."

# 停止所有服务
docker-compose -f docker-compose.independent.yml down

echo "✅ 开发环境已停止"
echo ""
echo "📊 停止的服务："
echo "  🖥️  开发容器: masteryos-dev"
echo "  🗄️  PostgreSQL: masteryos-postgres-dev"
echo "  🔴 Redis: masteryos-redis-dev"
echo "  💾 MinIO: masteryos-minio-dev"
echo "  📄 Docling: masteryos-docling-dev"
echo "  ⚖️  Nginx: masteryos-nginx-dev"
echo ""
echo "💡 提示："
echo "  重新启动: ./scripts/dev-start.sh"
echo "  完全清理: ./scripts/dev-clean.sh"
echo ""
echo "ℹ️  注意: 数据已保存在Docker卷中，重启后数据仍然存在"