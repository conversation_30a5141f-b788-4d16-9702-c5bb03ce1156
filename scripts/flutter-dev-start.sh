#!/bin/bash

echo "🚀 启动 Flutter 移动应用开发服务器"
echo "================================="

# 检查 FVM 是否可用
if ! command -v fvm &> /dev/null; then
    echo "❌ FVM 未找到，请先安装 FVM"
    echo "   安装命令: dart pub global activate fvm"
    exit 1
fi

# 进入 Flutter 应用目录
cd apps/mobile

# 检查 Flutter 环境
echo "🔍 检查 Flutter 环境..."
export PATH="$PATH:$HOME/.pub-cache/bin"
fvm flutter doctor

# 获取依赖
echo ""
echo "📦 获取 Flutter 依赖..."
fvm flutter pub get

# 启动 Web 开发服务器
echo ""
echo "🌐 启动 Flutter Web 开发服务器..."
echo "   访问地址: http://localhost:8080"
echo ""
echo "🔧 可用命令:"
echo "   热重载: 按 'r'"
echo "   热重启: 按 'R'"
echo "   退出: 按 'q'"
echo ""

fvm flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0