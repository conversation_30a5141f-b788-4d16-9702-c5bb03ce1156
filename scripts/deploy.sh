#!/bin/bash

# =================================
# MasteryOS 生产部署脚本
# =================================

set -e  # 遇到任何错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_env() {
    log_info "检查环境变量..."
    
    required_vars=(
        "DB_PASSWORD"
        "JWT_SECRET"
        "MINIO_ACCESS_KEY"
        "MINIO_SECRET_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 $var 未设置"
            exit 1
        fi
    done
    
    log_success "环境变量检查通过"
}

# 检查Docker和Docker Compose
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 守护进程未运行"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p nginx/ssl
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/minio
    mkdir -p logs
    
    log_success "目录创建完成"
}

# 生成SSL证书（自签名，生产环境请使用真实证书）
generate_ssl_cert() {
    log_info "检查SSL证书..."
    
    if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/key.pem" ]; then
        log_warning "SSL证书不存在，生成自签名证书..."
        
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=CN/ST=Beijing/L=Beijing/O=MasteryOS/CN=localhost"
        
        log_success "SSL证书生成完成"
    else
        log_success "SSL证书已存在"
    fi
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建admin-bff镜像
    log_info "构建admin-bff镜像..."
    docker build -t masteryos/admin-bff:latest ./apps/admin-bff/
    
    # 构建admin-web镜像
    log_info "构建admin-web镜像..."
    docker build -t masteryos/admin-web:latest ./apps/admin-web/
    
    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止现有服务
    docker-compose -f docker-compose.prod.yml down
    
    # 启动服务
    docker-compose -f docker-compose.prod.yml up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待数据库启动
    log_info "等待PostgreSQL启动..."
    timeout=60
    while ! docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U masteryos &>/dev/null; do
        sleep 1
        timeout=$((timeout - 1))
        if [ $timeout -le 0 ]; then
            log_error "PostgreSQL启动超时"
            exit 1
        fi
    done
    log_success "PostgreSQL启动成功"
    
    # 等待Redis启动
    log_info "等待Redis启动..."
    timeout=30
    while ! docker-compose -f docker-compose.prod.yml exec -T redis redis-cli ping &>/dev/null; do
        sleep 1
        timeout=$((timeout - 1))
        if [ $timeout -le 0 ]; then
            log_error "Redis启动超时"
            exit 1
        fi
    done
    log_success "Redis启动成功"
    
    # 等待API服务启动
    log_info "等待Admin BFF API启动..."
    timeout=60
    while ! curl -sf http://localhost:3102/health &>/dev/null; do
        sleep 2
        timeout=$((timeout - 1))
        if [ $timeout -le 0 ]; then
            log_error "Admin BFF API启动超时"
            exit 1
        fi
    done
    log_success "Admin BFF API启动成功"
    
    # 等待Web服务启动
    log_info "等待Admin Web启动..."
    timeout=30
    while ! curl -sf http://localhost:80/health &>/dev/null; do
        sleep 2
        timeout=$((timeout - 1))
        if [ $timeout -le 0 ]; then
            log_error "Admin Web启动超时"
            exit 1
        fi
    done
    log_success "Admin Web启动成功"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待API服务完全启动
    sleep 10
    
    # 这里可以添加数据库迁移命令
    # docker-compose -f docker-compose.prod.yml exec admin-bff npm run migration:run
    
    log_success "数据库迁移完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查所有服务状态
    services=("postgres" "redis" "minio" "admin-bff" "admin-web" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.prod.yml ps | grep -q "$service.*Up"; then
            log_success "$service 服务运行正常"
        else
            log_error "$service 服务运行异常"
            exit 1
        fi
    done
    
    # API健康检查
    api_health=$(curl -s http://localhost:3102/health | jq -r '.status' 2>/dev/null || echo "error")
    if [ "$api_health" = "ok" ]; then
        log_success "API健康检查通过"
    else
        log_error "API健康检查失败"
        exit 1
    fi
    
    log_success "所有健康检查通过"
}

# 显示部署信息
show_deployment_info() {
    log_success "=================="
    log_success "部署完成！"
    log_success "=================="
    echo ""
    log_info "服务访问地址："
    log_info "  • 管理后台: http://localhost (或配置的域名)"
    log_info "  • API文档: http://localhost:3102/api/docs"
    log_info "  • MinIO控制台: http://localhost:9001"
    echo ""
    log_info "默认登录信息："
    log_info "  • MinIO: minioadmin / minioadmin123"
    echo ""
    log_info "有用的命令："
    log_info "  • 查看日志: docker-compose -f docker-compose.prod.yml logs -f"
    log_info "  • 停止服务: docker-compose -f docker-compose.prod.yml down"
    log_info "  • 重启服务: docker-compose -f docker-compose.prod.yml restart"
    echo ""
}

# 清理函数
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "部署过程中发生错误，正在清理..."
        docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    fi
}

# 设置清理函数
trap cleanup EXIT

# 主函数
main() {
    log_info "开始MasteryOS生产部署..."
    
    check_env
    check_docker
    create_directories
    generate_ssl_cert
    build_images
    start_services
    wait_for_services
    run_migrations
    health_check
    show_deployment_info
    
    log_success "部署成功完成！"
}

# 如果直接运行此脚本，则执行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi