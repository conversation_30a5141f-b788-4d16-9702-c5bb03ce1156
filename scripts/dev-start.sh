#!/bin/bash

echo "🚀 启动 MasteryOS 独立开发环境..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 创建必要的目录
mkdir -p nginx logs scripts

# 创建 nginx 配置文件（如果不存在）
if [ ! -f nginx/nginx.dev.conf ]; then
    echo "📝 创建 Nginx 配置文件..."
    cat > nginx/nginx.dev.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server dev:8180;
    }
    
    upstream api {
        server dev:8181;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # 前端路由
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # API 路由
        location /api/ {
            proxy_pass http://api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # GraphQL 路由
        location /graphql {
            proxy_pass http://api/graphql;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
EOF
fi

# 创建数据库初始化脚本（如果不存在）
if [ ! -f scripts/init-db.sql ]; then
    echo "📝 创建数据库初始化脚本..."
    cat > scripts/init-db.sql << 'EOF'
-- MasteryOS 数据库初始化脚本
-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 设置时区
SET timezone = 'UTC';

-- 创建基础用户角色枚举
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('user', 'premium', 'enterprise', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建技能等级枚举
DO $$ BEGIN
    CREATE TYPE skill_level AS ENUM ('beginner', 'advanced', 'expert', 'professional', 'master');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 输出初始化完成信息
SELECT 'MasteryOS 数据库初始化完成' as status;
EOF
fi

# 启动独立开发环境
echo "📦 启动所有服务..."
docker-compose -f docker-compose.independent.yml up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.independent.yml ps

# 等待健康检查完成
echo "🔍 等待健康检查完成..."
for i in {1..30}; do
    if docker-compose -f docker-compose.independent.yml ps | grep -q "healthy"; then
        break
    fi
    echo "等待中... ($i/30)"
    sleep 2
done

# 显示访问信息
echo ""
echo "✅ 开发环境启动完成！"
echo ""
echo "📊 服务访问地址："
echo "  🖥️  开发容器: docker exec -it masteryos-dev bash"
echo "  🗄️  PostgreSQL: localhost:8182 (用户名/密码: masteryos/masteryos123)"
echo "  🔴 Redis: localhost:8183 (密码: masteryos123)"
echo "  📄 Docling: http://localhost:8184"
echo "  💾 MinIO API: http://localhost:8185"
echo "  🌐 MinIO Console: http://localhost:8186 (用户名/密码: masteryos/masteryos123)"
echo "  ⚖️  Nginx: http://localhost:8080"
echo ""
echo "🛠️  开发命令："
echo "  进入开发容器: ./scripts/dev-enter.sh"
echo "  查看日志: ./scripts/dev-logs.sh"
echo "  停止环境: ./scripts/dev-stop.sh"
echo "  清理环境: ./scripts/dev-clean.sh"
echo ""
echo "📚 快速开始："
echo "  1. ./scripts/dev-enter.sh"
echo "  2. pnpm install (在容器内)"
echo "  3. pnpm run dev (在容器内)"
echo ""
echo "💡 提示: 如果某些服务未就绪，请等待几分钟后重试"