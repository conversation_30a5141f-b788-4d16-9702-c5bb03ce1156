#!/bin/bash

# Flutter Web 管理后台启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
ADMIN_WEB_DIR="$PROJECT_ROOT/apps/admin-web"

echo -e "${BLUE}🚀 启动 Flutter Web 管理后台${NC}"
echo -e "${BLUE}================================${NC}"

# 检查 Flutter 是否安装
if ! command -v flutter &> /dev/null; then
    echo -e "${RED}❌ Flutter 未安装，请先安装 Flutter SDK${NC}"
    exit 1
fi

# 检查项目目录
if [ ! -d "$ADMIN_WEB_DIR" ]; then
    echo -e "${RED}❌ 找不到管理后台项目目录: $ADMIN_WEB_DIR${NC}"
    exit 1
fi

cd "$ADMIN_WEB_DIR"

# 检查并安装依赖
echo -e "${YELLOW}📦 检查项目依赖...${NC}"
if [ ! -d "build" ] || [ ! -d ".dart_tool" ]; then
    echo -e "${YELLOW}📦 安装项目依赖...${NC}"
    flutter pub get
fi

# 设置 API 地址（可通过环境变量覆盖）
API_BASE_URL="${API_BASE_URL:-http://localhost:3101}"

echo -e "${GREEN}✅ 配置信息:${NC}"
echo -e "   API 地址: $API_BASE_URL"
echo -e "   Web 端口: 3200"
echo

# 启动开发服务器
echo -e "${BLUE}🌐 启动 Flutter Web 开发服务器...${NC}"
echo -e "${YELLOW}提示: 首次启动可能需要较长时间编译${NC}"
echo

flutter run -d chrome \
    --web-port=3200 \
    --web-hostname=0.0.0.0 \
    --dart-define=API_BASE_URL=$API_BASE_URL

# 如果需要构建生产版本，使用以下命令：
# flutter build web --release --dart-define=API_BASE_URL=$API_BASE_URL