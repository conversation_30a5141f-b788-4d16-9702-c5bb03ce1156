#!/bin/bash

echo "📊 MasteryOS 混合架构开发环境状态检查"
echo "========================================"
echo "📅 检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 检查 Docker 是否运行
echo "🐳 Docker 状态:"
if docker info > /dev/null 2>&1; then
    echo "  ✅ Docker 运行正常"
    echo "  🔧 版本: $(docker --version)"
else
    echo "  ❌ Docker 未运行或无法访问"
    echo ""
    echo "💡 请先启动 Docker Desktop 再重新检查"
    exit 1
fi

echo ""
echo "📦 容器状态:"
if [ -f "docker-compose.hybrid-dev.yml" ]; then
    # 使用自定义格式显示容器状态
    echo "┌─────────────────────┬──────────────────────┬─────────────────────────┬──────────────┐"
    echo "│ 服务名称            │ 状态                 │ 端口                    │ 健康状态     │"
    echo "├─────────────────────┼──────────────────────┼─────────────────────────┼──────────────┤"
    
    services=("mobile-bff:移动端API" "admin-bff:管理端API" "admin-spa:管理前端" "db:主数据库" "db-readonly:只读数据库" "redis:缓存队列" "minio:文件存储" "docling:PDF服务" "ai-service:AI服务" "redis-commander:Redis管理" "pgadmin:数据库管理" "flutter-dev:Flutter开发")
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service display_name <<< "$service_info"
        
        # 检查容器是否存在
        if docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml ps "$service" > /dev/null 2>&1; then
            status=$(docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml ps "$service" --format "table {{.Status}}" | tail -n +2)
            ports=$(docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml ps "$service" --format "table {{.Ports}}" | tail -n +2 | tr '\n' ' ')
            
            # 获取健康状态
            container_name=$(docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml ps -q "$service" 2>/dev/null)
            if [ -n "$container_name" ]; then
                health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
                if [ "$health" = "none" ]; then
                    if [[ "$status" == *"Up"* ]]; then
                        health="运行中"
                    else
                        health="未运行"
                    fi
                elif [ "$health" = "healthy" ]; then
                    health="✅ 健康"
                elif [ "$health" = "unhealthy" ]; then
                    health="❌ 异常"
                elif [ "$health" = "starting" ]; then
                    health="🔄 启动中"
                fi
            else
                health="未运行"
            fi
            
            # 格式化状态
            if [[ "$status" == *"Up"* ]]; then
                status_icon="🟢"
            else
                status_icon="🔴"
            fi
            
            printf "│ %-19s │ %s %-17s │ %-23s │ %-12s │\n" "$display_name" "$status_icon" "${status:0:17}" "${ports:0:23}" "$health"
        else
            printf "│ %-19s │ 🔴 %-17s │ %-23s │ %-12s │\n" "$display_name" "未启动" "-" "未运行"
        fi
    done
    
    echo "└─────────────────────┴──────────────────────┴─────────────────────────┴──────────────┘"
else
    echo "  ⚠️  docker-compose.hybrid-dev.yml 文件不存在"
fi

echo ""
echo "🌐 网络状态:"
if docker network ls | grep -q masteryos-hybrid-dev; then
    echo "  ✅ masteryos-hybrid-dev 网络存在"
    # 显示网络中的容器数量
    container_count=$(docker network inspect masteryos-hybrid-dev --format '{{len .Containers}}' 2>/dev/null || echo "0")
    echo "  📊 连接的容器数量: $container_count"
else
    echo "  ⚠️  masteryos-hybrid-dev 网络不存在"
fi

echo ""
echo "💾 数据卷状态:"
volumes=(
    "postgres_data:PostgreSQL数据"
    "postgres_readonly_data:只读数据库数据"
    "redis_data:Redis数据"
    "minio_data:MinIO文件存储"
    "docling_cache:Docling缓存"
    "ollama_data:AI模型数据"
    "mobile_node_modules:移动端依赖"
    "admin_node_modules:管理端依赖"
    "admin_spa_node_modules:前端依赖"
    "flutter_cache:Flutter缓存"
    "flutter_pub_cache:Pub缓存"
    "pgadmin_data:pgAdmin数据"
)

for volume_info in "${volumes[@]}"; do
    IFS=':' read -r volume_name display_name <<< "$volume_info"
    full_volume_name="masteryos-hybrid-dev_$volume_name"
    
    if docker volume ls --format "{{.Name}}" | grep -q "^$full_volume_name$"; then
        size=$(docker system df -v 2>/dev/null | grep "$full_volume_name" | awk '{print $3}' || echo "未知")
        echo "  ✅ $display_name ($size)"
    else
        echo "  ⚠️  $display_name (不存在)"
    fi
done

echo ""
echo "🔌 端口使用状态:"
ports=(3100 3101 3102 5432 6379 8080 8081 8182 9000 9101 11434)
port_services=(
    "3100:管理前端"
    "3101:移动端API"
    "3102:管理端API"
    "5432:PostgreSQL"
    "6379:Redis"
    "8080:Docling/Flutter"
    "8081:Redis Commander"
    "8182:pgAdmin"
    "9000:MinIO API"
    "9101:MinIO Console"
    "11434:Ollama AI"
)

echo "┌──────┬──────────────────┬─────────────────────┐"
echo "│ 端口 │ 服务             │ 状态                │"
echo "├──────┼──────────────────┼─────────────────────┤"

for port_info in "${port_services[@]}"; do
    IFS=':' read -r port service_name <<< "$port_info"
    
    if lsof -i :$port > /dev/null 2>&1; then
        process=$(lsof -i :$port | tail -1 | awk '{print $1}')
        printf "│ %-4s │ %-16s │ 🟢 被 %-12s 占用 │\n" "$port" "$service_name" "$process"
    else
        printf "│ %-4s │ %-16s │ ⚪ 空闲              │\n" "$port" "$service_name"
    fi
done

echo "└──────┴──────────────────┴─────────────────────┘"

echo ""
echo "🩺 健康检查详情:"
# 检查各服务的健康状态
health_checks=(
    "db:数据库连接:pg_isready -h localhost -p 5432 -U postgres"
    "redis:Redis连接:redis-cli -h localhost -p 6379 ping"
    "minio:MinIO服务:curl -f http://localhost:9000/minio/health/live"
)

for check_info in "${health_checks[@]}"; do
    IFS=':' read -r service_name display_name check_command <<< "$check_info"
    
    echo "  🔍 $display_name:"
    if eval "$check_command" > /dev/null 2>&1; then
        echo "    ✅ 连接正常"
    else
        echo "    ❌ 连接失败"
        echo "    💡 建议: 检查容器是否正常运行"
    fi
done

echo ""
echo "💽 系统资源使用:"
if command -v docker > /dev/null 2>&1; then
    echo "  Docker 资源使用情况:"
    docker system df 2>/dev/null | sed 's/^/    /'
fi

echo ""
echo "======================================"
echo "🎯 快速操作指南:"
echo ""
echo "📊 监控相关:"
echo "  查看实时日志: docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml logs -f [服务名]"
echo "  查看资源使用: docker stats"
echo ""
echo "🔧 服务管理:"
echo "  启动环境:     ./scripts/hybrid-dev-start.sh"
echo "  停止环境:     ./scripts/hybrid-dev-stop.sh"
echo "  重启服务:     docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml restart [服务名]"
echo "  进入容器:     docker-compose -f infrastructure/docker/docker-compose.hybrid-dev.yml exec [服务名] bash"
echo ""
echo "🧹 环境管理:"
echo "  清理环境:     ./scripts/hybrid-dev-clean.sh"
echo "  重置数据:     ./scripts/hybrid-dev-clean.sh && ./scripts/hybrid-dev-start.sh"
echo ""
echo "🌐 Web访问 (如果服务正在运行):"
echo "  管理后台:     http://localhost:3100"
echo "  API文档:      http://localhost:3101/docs (移动端) | http://localhost:3102/docs (管理端)"
echo "  MinIO控制台:  http://localhost:9101"
echo "  Redis管理:    http://localhost:8081 (如果启用)"
echo "  数据库管理:   http://localhost:8182 (如果启用)"