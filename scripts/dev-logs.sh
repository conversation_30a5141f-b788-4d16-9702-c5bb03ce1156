#!/bin/bash

echo "📋 查看 MasteryOS 开发环境日志..."

# 显示可用选项
if [ -z "$1" ]; then
    echo ""
    echo "用法: $0 [service_name]"
    echo ""
    echo "可用服务:"
    echo "  dev        - 开发容器"
    echo "  postgres   - PostgreSQL数据库"
    echo "  redis      - Redis缓存"
    echo "  minio      - MinIO对象存储"
    echo "  docling    - Docling PDF解析"
    echo "  nginx      - Nginx负载均衡"
    echo "  all        - 所有服务 (默认)"
    echo ""
    echo "示例:"
    echo "  $0 dev       # 查看开发容器日志"
    echo "  $0 postgres  # 查看数据库日志"
    echo "  $0           # 查看所有日志"
    echo ""
    
    # 默认显示所有服务状态
    echo "当前服务状态:"
    docker-compose -f docker-compose.independent.yml ps
    echo ""
    echo "显示最近所有日志 (按 Ctrl+C 退出):"
    docker-compose -f docker-compose.independent.yml logs -f --tail=50
else
    service_name=$1
    
    case $service_name in
        "all")
            echo "显示所有服务日志 (按 Ctrl+C 退出):"
            docker-compose -f docker-compose.independent.yml logs -f --tail=50
            ;;
        "dev"|"postgres"|"redis"|"minio"|"docling"|"nginx")
            echo "显示 $service_name 服务日志 (按 Ctrl+C 退出):"
            docker-compose -f docker-compose.independent.yml logs -f --tail=50 $service_name
            ;;
        *)
            echo "❌ 未知服务: $service_name"
            echo "请使用: dev, postgres, redis, minio, docling, nginx, all"
            exit 1
            ;;
    esac
fi