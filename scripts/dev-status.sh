#!/bin/bash

echo "📊 MasteryOS 开发环境状态检查"
echo "=================================="

# 检查 Docker 是否运行
echo ""
echo "🐳 Docker 状态:"
if docker info > /dev/null 2>&1; then
    echo "  ✅ Docker 运行正常"
    echo "  🔧 版本: $(docker --version)"
else
    echo "  ❌ Docker 未运行或无法访问"
    exit 1
fi

# 检查容器状态
echo ""
echo "📦 容器状态:"
if docker-compose -f docker-compose.independent.yml ps > /dev/null 2>&1; then
    docker-compose -f docker-compose.independent.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
else
    echo "  ⚠️  开发环境未启动或配置文件不存在"
fi

# 检查网络
echo ""
echo "🌐 网络状态:"
if docker network ls | grep -q masteryos-dev-network; then
    echo "  ✅ masteryos-dev-network 网络存在"
else
    echo "  ⚠️  masteryos-dev-network 网络不存在"
fi

# 检查数据卷
echo ""
echo "💾 数据卷状态:"
volumes=(
    "masteryos-postgres-dev-data"
    "masteryos-redis-dev-data"
    "masteryos-minio-dev-data"
    "masteryos-docling-dev-cache"
    "masteryos-node-modules-cache"
    "masteryos-pnpm-cache"
)

for volume in "${volumes[@]}"; do
    if docker volume ls | grep -q "$volume"; then
        size=$(docker system df -v | grep "$volume" | awk '{print $3}' || echo "未知")
        echo "  ✅ $volume ($size)"
    else
        echo "  ⚠️  $volume (不存在)"
    fi
done

# 检查端口占用
echo ""
echo "🔌 端口状态:"
ports=(8180 8181 8182 8183 8184 8185 8186 8080)
for port in "${ports[@]}"; do
    if lsof -i :$port > /dev/null 2>&1; then
        process=$(lsof -i :$port | tail -1 | awk '{print $1}')
        echo "  🟢 端口 $port: 被 $process 占用"
    else
        echo "  ⚪ 端口 $port: 空闲"
    fi
done

# 检查健康状态
echo ""
echo "🩺 健康检查:"
if docker ps --format "table {{.Names}}\t{{.Status}}" | grep masteryos- > /dev/null 2>&1; then
    echo "运行中的 MasteryOS 容器:"
    docker ps --format "table {{.Names}}\t{{.Status}}" | grep masteryos- | while read line; do
        if echo "$line" | grep -q "healthy"; then
            echo "  ✅ $line"
        elif echo "$line" | grep -q "unhealthy"; then
            echo "  ❌ $line"
        else
            echo "  🟡 $line"
        fi
    done
else
    echo "  ⚠️  没有运行中的 MasteryOS 容器"
fi

# 检查磁盘使用
echo ""
echo "💽 磁盘使用:"
if which docker > /dev/null 2>&1; then
    echo "Docker 资源使用情况:"
    docker system df
fi

echo ""
echo "=================================="
echo "🎯 快速操作:"
echo "  启动环境: ./scripts/dev-start.sh"
echo "  进入容器: ./scripts/dev-enter.sh"
echo "  查看日志: ./scripts/dev-logs.sh"
echo "  停止环境: ./scripts/dev-stop.sh"
echo "  清理环境: ./scripts/dev-clean.sh"