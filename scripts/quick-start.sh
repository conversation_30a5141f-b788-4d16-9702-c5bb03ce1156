#!/bin/bash

# =================================
# MasteryOS 快速启动脚本
# =================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "MasteryOS 快速启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务（默认）"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  clean     清理所有数据和镜像"
    echo "  help      显示此帮助信息"
    echo ""
    echo "环境:"
    echo "  dev       开发环境（默认）"
    echo "  prod      生产环境"
    echo ""
    echo "示例:"
    echo "  $0 start         # 启动开发环境"
    echo "  $0 start prod    # 启动生产环境"
    echo "  $0 logs admin-bff  # 查看admin-bff日志"
}

# 检查环境文件
check_env_file() {
    local env=${1:-dev}
    
    if [ "$env" = "prod" ]; then
        if [ ! -f ".env" ]; then
            log_warning ".env文件不存在，请复制.env.example并配置"
            log_info "cp .env.example .env"
            exit 1
        fi
    fi
}

# 启动服务
start_services() {
    local env=${1:-dev}
    
    log_info "启动MasteryOS服务（$env环境）..."
    
    check_env_file "$env"
    
    if [ "$env" = "prod" ]; then
        # 生产环境部署
        if [ -f "scripts/deploy.sh" ]; then
            log_info "使用生产部署脚本..."
            source .env
            export $(grep -v '^#' .env | xargs)
            bash scripts/deploy.sh
        else
            log_info "直接启动生产环境..."
            docker-compose -f docker-compose.prod.yml up -d
        fi
    else
        # 开发环境启动
        log_info "启动开发环境..."
        
        # 启动数据库和基础服务
        docker-compose -f infrastructure/docker/docker-compose.database-only.yml up -d
        
        # 等待数据库启动
        log_info "等待数据库启动..."
        sleep 10
        
        # 启动admin-bff
        log_info "启动Admin BFF..."
        cd apps/admin-bff
        pnpm install
        pnpm run start:dev &
        ADMIN_BFF_PID=$!
        
        # 返回根目录并启动admin-web
        cd ../..
        log_info "启动Admin Web..."
        cd apps/admin-web
        flutter pub get
        flutter pub run build_runner build --delete-conflicting-outputs
        flutter run -d web-server --web-hostname=0.0.0.0 --web-port=3200 &
        ADMIN_WEB_PID=$!
        
        cd ../..
        
        # 保存PID以便后续停止
        echo "$ADMIN_BFF_PID" > .admin-bff.pid
        echo "$ADMIN_WEB_PID" > .admin-web.pid
        
        log_success "开发环境启动完成！"
        log_info "Admin BFF: http://localhost:3102"
        log_info "Admin Web: http://localhost:3200"
        log_info "API文档: http://localhost:3102/api/docs"
    fi
}

# 停止服务
stop_services() {
    local env=${1:-dev}
    
    log_info "停止MasteryOS服务（$env环境）..."
    
    if [ "$env" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        # 停止开发环境进程
        if [ -f ".admin-bff.pid" ]; then
            kill $(cat .admin-bff.pid) 2>/dev/null || true
            rm .admin-bff.pid
        fi
        
        if [ -f ".admin-web.pid" ]; then
            kill $(cat .admin-web.pid) 2>/dev/null || true
            rm .admin-web.pid
        fi
        
        # 停止数据库服务
        docker-compose -f infrastructure/docker/docker-compose.database-only.yml down
    fi
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    local env=${1:-dev}
    
    log_info "重启MasteryOS服务..."
    stop_services "$env"
    sleep 2
    start_services "$env"
}

# 查看服务状态
show_status() {
    local env=${1:-dev}
    
    log_info "服务状态："
    
    if [ "$env" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        echo "开发环境状态："
        
        # 检查数据库服务
        docker-compose -f infrastructure/docker/docker-compose.database-only.yml ps
        
        # 检查开发进程
        if [ -f ".admin-bff.pid" ] && kill -0 $(cat .admin-bff.pid) 2>/dev/null; then
            echo "Admin BFF: ✅ 运行中 (PID: $(cat .admin-bff.pid))"
        else
            echo "Admin BFF: ❌ 未运行"
        fi
        
        if [ -f ".admin-web.pid" ] && kill -0 $(cat .admin-web.pid) 2>/dev/null; then
            echo "Admin Web: ✅ 运行中 (PID: $(cat .admin-web.pid))"
        else
            echo "Admin Web: ❌ 未运行"
        fi
    fi
}

# 查看日志
show_logs() {
    local env=${1:-dev}
    local service=${2:-all}
    
    if [ "$env" = "prod" ]; then
        if [ "$service" = "all" ]; then
            docker-compose -f docker-compose.prod.yml logs -f
        else
            docker-compose -f docker-compose.prod.yml logs -f "$service"
        fi
    else
        if [ "$service" = "all" ]; then
            docker-compose -f infrastructure/docker/docker-compose.database-only.yml logs -f
        else
            docker-compose -f infrastructure/docker/docker-compose.database-only.yml logs -f "$service"
        fi
    fi
}

# 清理数据
clean_data() {
    local env=${1:-dev}
    
    log_warning "这将删除所有数据和镜像，确定要继续吗？(y/N)"
    read -r confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "操作已取消"
        return
    fi
    
    log_info "清理MasteryOS数据..."
    
    # 停止服务
    stop_services "$env"
    
    if [ "$env" = "prod" ]; then
        # 删除容器和卷
        docker-compose -f docker-compose.prod.yml down -v --rmi all
    else
        # 删除开发环境容器和卷
        docker-compose -f infrastructure/docker/docker-compose.database-only.yml down -v --rmi all
    fi
    
    # 清理构建缓存
    docker system prune -f
    
    log_success "清理完成"
}

# 主函数
main() {
    local command=${1:-start}
    local env_or_service=${2:-dev}
    local service=${3:-all}
    
    case "$command" in
        "start")
            start_services "$env_or_service"
            ;;
        "stop")
            stop_services "$env_or_service"
            ;;
        "restart")
            restart_services "$env_or_service"
            ;;
        "status")
            show_status "$env_or_service"
            ;;
        "logs")
            show_logs "$env_or_service" "$service"
            ;;
        "clean")
            clean_data "$env_or_service"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 捕获Ctrl+C信号
trap 'log_info "接收到中断信号，正在停止服务..."; stop_services; exit 0' INT

# 执行主函数
main "$@"