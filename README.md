# MasteryOS (1w) - 智能技能发展平台

<div align="center">

![MasteryOS](https://img.shields.io/badge/MasteryOS-1w-blue?style=for-the-badge)
![Version](https://img.shields.io/badge/version-1.0.0-green?style=for-the-badge)
![License](https://img.shields.io/badge/license-MIT-yellow?style=for-the-badge)

**基于"一万小时定律"的智能化技能培养与跟踪系统**

[🚀 快速开始](#快速开始) • [📖 文档](#文档) • [🏗️ 架构](#技术架构) • [🤝 贡献](#贡献指南) • [📧 联系](#联系我们)

</div>

---

## 🎯 项目简介

MasteryOS (万时通) 是一个现代化的技能发展和学习管理平台，帮助个人和组织系统化地培养各种技能，实现从新手到专家的完整成长路径。

### ✨ 核心特性

- **🎯 智能技能追踪**: 基于"一万小时定律"的科学化技能发展模型
- **📚 文档智能化**: AI 驱动的学习资料分析和知识提取
- **👥 社交化学习**: 构建学习社区，促进知识分享和协作
- **🎮 游戏化机制**: 成就系统、等级提升、学习排行榜
- **📊 数据驱动**: 详细的学习分析和进度可视化
- **🔒 企业级安全**: 完整的权限管理和数据安全保障

### 🏆 适用场景

- **个人技能发展**: 编程、设计、语言学习、音乐、体育等
- **企业培训管理**: 员工技能培养、认证管理、培训跟踪  
- **教育机构**: 学生学习管理、课程规划、成绩分析
- **专业认证**: 职业技能认证、持续教育管理

---

## 🚀 快速开始

### 📋 环境要求

- **Node.js**: ≥ 22.18.0 LTS
- **pnpm**: ≥ 8.0.0
- **Docker**: ≥ 20.10.0
- **Flutter**: 3.32.1 (通过 FVM 管理)

### ⚡ 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/changxiaoyangbrain/1w.git
cd 1w

# 2. 安装依赖
pnpm install

# 3. 启动数据库环境
./scripts/1w-db.sh start

# 4. 启动开发服务
cd apps/mobile && fvm flutter run -d web-server --web-port=8080
```

🌐 **访问地址**:
- 移动端开发: http://localhost:8080
- 数据库管理: http://localhost:8182 (pgAdmin)

> 📖 **详细指南**: 查看 [环境搭建文档](docs/getting-started/installation.md)

---

## 📖 文档

### 🎯 快速导航

| 📂 分类 | 📄 文档 | 📝 描述 |
|---------|---------|---------|
| **🚀 开始使用** | [安装指南](docs/getting-started/installation.md) | 环境搭建和依赖安装 |
| | [快速启动](docs/getting-started/quick-start.md) | 5分钟快速体验 |
| **🛠️ 开发指南** | [开发环境](docs/development/development-guide.md) | 开发环境配置和工作流 |
| | [编码规范](docs/development/coding-standards.md) | 代码风格和质量标准 |
| **🏗️ 系统架构** | [技术架构](docs/architecture/system-overview.md) | 系统设计和技术选型 |
| | [数据库设计](docs/architecture/database-design.md) | 数据模型和关系设计 |
| **📡 API 文档** | [管理端 API](docs/api/admin-api/README.md) | 后台管理接口文档 |
| | [移动端 API](docs/api/mobile-api/README.md) | 移动应用接口文档 |
| **🚀 部署运维** | [Docker 部署](docs/deployment/docker-deployment.md) | 容器化部署指南 |
| | [生产环境](docs/deployment/production-setup.md) | 生产环境配置 |

### 📚 完整文档

访问 [docs/](docs/) 目录查看完整的项目文档，或访问在线文档站点 (待开发)。

---

## 🏗️ 技术架构

### 🎨 技术栈

| 层级 | 技术选型 | 版本 | 用途 |
|------|----------|------|------|
| **前端移动** | Flutter | 3.32.1 | 跨平台移动应用 |
| **前端管理** | React + Vite | 18.3.1 | 管理后台界面 |
| **后端 API** | NestJS + TypeScript | 10.4.9 | RESTful API 服务 |
| **数据库** | PostgreSQL + pgvector | 16 | 关系数据 + 向量搜索 |
| **缓存** | Redis | 7 | 会话缓存和任务队列 |
| **容器化** | Docker Compose | - | 开发和部署环境 |
| **包管理** | pnpm Workspaces | 10.14.0 | Monorepo 依赖管理 |

### 🏢 项目结构

```
1w/                                # 项目根目录
├── apps/                          # 应用程序
│   ├── mobile/                    # Flutter 移动应用
│   ├── admin-spa/                 # React 管理后台
│   ├── admin-bff/                 # 管理端 API (NestJS)
│   └── mobile-bff/                # 移动端 API (NestJS)
├── infrastructure/docker/         # Docker 配置
├── scripts/                       # 开发脚本
├── docs/                          # 项目文档
└── README.md                      # 项目说明
```

### 🔗 服务端口

| 服务 | 端口 | 环境 | 用途 |
|------|------|------|------|
| Flutter Web | 8080 | 开发 | 移动端预览 |
| Admin SPA | 3100 | 开发 | 管理后台 |
| Mobile BFF | 3101 | 开发 | 移动端 API |
| Admin BFF | 3102 | 开发 | 管理端 API |
| PostgreSQL | 8182 | 开发 | 主数据库 |
| Redis | 8183 | 开发 | 缓存服务 |

---

## 📈 项目状态

### ✅ 已完成功能 (v1.0.0)

- [x] **基础架构**: 完整的 Monorepo 项目结构
- [x] **开发环境**: Docker 化开发环境，支持热重载
- [x] **数据库设计**: 18个核心数据表，支持技能追踪体系
- [x] **移动端框架**: Flutter 基础应用架构和导航
- [x] **后端 API**: NestJS 服务框架和认证体系  
- [x] **管理后台**: React Admin 基础管理界面
- [x] **代码质量**: ESLint + TypeScript + Prettier 完整配置

### 🚧 开发中功能 (v1.1.0)

- [ ] **用户认证**: JWT + OAuth 2.0 认证体系
- [ ] **技能管理**: 技能创建、追踪、统计功能
- [ ] **文档处理**: PDF 解析和 AI 知识提取
- [ ] **学习记录**: 时长追踪和进度分析
- [ ] **社交功能**: 用户关注和学习动态

### 📅 规划功能 (v2.0.0)

- [ ] **AI 助手**: 智能学习建议和路径规划
- [ ] **移动应用**: 完整的 iOS/Android 原生体验
- [ ] **企业版**: 多租户、权限管理、数据分析
- [ ] **第三方集成**: 课程平台、认证机构对接

---

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

### 🔧 开发流程

1. **Fork** 项目到你的 GitHub 账户
2. **Clone** 到本地开发环境
3. **创建** 特性分支: `git checkout -b feature/new-feature`
4. **提交** 代码: `git commit -am 'Add new feature'`
5. **推送** 分支: `git push origin feature/new-feature`
6. **创建** Pull Request

### 📋 开发规范

- **代码风格**: 遵循 ESLint + Prettier 配置
- **提交信息**: 使用 [约定式提交](https://www.conventionalcommits.org/)
- **测试覆盖**: 新功能需要包含单元测试
- **文档更新**: 重要变更需要更新相关文档

---

## 📊 统计信息

![GitHub Stats](https://img.shields.io/github/stars/changxiaoyangbrain/1w?style=social)
![GitHub Forks](https://img.shields.io/github/forks/changxiaoyangbrain/1w?style=social)
![GitHub Issues](https://img.shields.io/github/issues/changxiaoyangbrain/1w)
![GitHub PRs](https://img.shields.io/github/issues-pr/changxiaoyangbrain/1w)

---

## 📄 许可证

本项目基于 [MIT 许可证](LICENSE) 开源。

---

## 📧 联系我们

- **项目主页**: https://github.com/changxiaoyangbrain/1w
- **Issue 反馈**: https://github.com/changxiaoyangbrain/1w/issues
- **邮箱联系**: [待补充]
- **社区讨论**: [待补充]

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个 Star！⭐**

Made with ❤️ by MasteryOS Team

</div>