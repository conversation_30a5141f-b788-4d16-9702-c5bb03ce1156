{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "composite": true, "removeComments": true, "noEmit": true, "importHelpers": true, "downlevelIteration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@apps/*": ["apps/*"], "@infrastructure/*": ["infrastructure/*"], "@docs/*": ["docs/*"]}, "types": ["node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["apps/**/*", "infrastructure/**/*", "scripts/**/*", "*.js", "*.ts"], "exclude": ["node_modules", "**/node_modules", "**/dist", "**/build", "**/.next", "**/coverage", "**/*.spec.ts", "**/*.test.ts", "apps/mobile/**/*", "apps/admin-bff/dist", "apps/mobile-bff/dist", "apps/admin-spa/dist"]}