import 'package:flutter/material.dart';
import '../utils/screen_utils.dart';

/// BuildContext扩展，提供便捷的上下文访问方法
extension BuildContextExtensions on BuildContext {
  /// 显示SnackBar
  void showSnackBar(
    String message, {
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
    Color? backgroundColor,
    Color? textColor,
  }) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(color: textColor),
        ),
        duration: duration,
        action: action,
        backgroundColor: backgroundColor,
      ),
    );
  }
  
  /// 显示成功消息
  void showSuccessSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }
  
  /// 显示错误消息
  void showErrorSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.red,
      textColor: Colors.white,
    );
  }
  
  /// 显示警告消息
  void showWarningSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
    );
  }
  
  /// 显示信息消息
  void showInfoSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }
  
  /// 显示加载对话框
  void showLoadingDialog({String? message}) {
    showDialog<void>(
      context: this,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            if (message != null) ...[
              const SizedBox(height: 16),
              Text(message),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 关闭对话框
  void closeDialog() {
    Navigator.of(this).pop();
  }
  
  /// 显示确认对话框
  Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
    bool isDangerous = false,
  }) {
    return showDialog<bool>(
      context: this,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: isDangerous
                ? TextButton.styleFrom(foregroundColor: Colors.red)
                : null,
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
  
  /// 显示底部弹出框
  Future<T?> showBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    Color? backgroundColor,
    double? elevation,
  }) {
    return showModalBottomSheet<T>(
      context: this,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => child,
    );
  }
  
  /// 隐藏键盘
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }
  
  /// 获取本地化字符串
  // String localizedString(String key) {
  //   return AppLocalizations.of(this)?.translate(key) ?? key;
  // }
  
  /// 导航到指定页面
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) {
    return Navigator.of(this).pushNamed<T>(routeName, arguments: arguments);
  }
  
  /// 替换当前页面
  Future<T?> pushReplacementNamed<T, TO>(String routeName, {Object? arguments}) {
    return Navigator.of(this).pushReplacementNamed<T, TO>(routeName, arguments: arguments);
  }
  
  /// 清除堆栈并导航到指定页面
  Future<T?> pushNamedAndClearStack<T>(String routeName, {Object? arguments}) {
    return Navigator.of(this).pushNamedAndRemoveUntil<T>(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }
  
  /// 返回上一页
  void pop<T>([T? result]) {
    Navigator.of(this).pop<T>(result);
  }
  
  /// 判断是否可以返回上一页
  bool canPop() {
    return Navigator.of(this).canPop();
  }
  
  /// 获取路由参数
  Object? get routeArguments {
    return ModalRoute.of(this)?.settings.arguments;
  }
  
  /// 获取路由名称
  String? get routeName {
    return ModalRoute.of(this)?.settings.name;
  }
  
  /// 获取屏幕类型
  ScreenType get screenType => ScreenUtils.getScreenType(this);
  
  /// 获取响应式值
  T getResponsiveValue<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    return ScreenUtils.getResponsiveValue(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
  
  /// 获取响应式列数
  int get responsiveColumns => ScreenUtils.getResponsiveColumns(this);
  
  /// 获取响应式间距
  double get responsiveSpacing => ScreenUtils.getResponsiveSpacing(this);
  
  /// 获取响应式内边距
  EdgeInsets get responsivePadding => ScreenUtils.getResponsivePadding(this);
  
  /// 获取可用高度
  double getAvailableHeight({
    bool excludeAppBar = true,
    bool excludeBottomNav = false,
  }) {
    return ScreenUtils.getAvailableHeight(
      this,
      excludeAppBar: excludeAppBar,
      excludeBottomNav: excludeBottomNav,
    );
  }
  
  /// 获取最大内容宽度
  double get maxContentWidth => ScreenUtils.getMaxContentWidth(this);
  
  /// 显示菜单
  Future<T?> showPopupMenu<T>({
    required List<PopupMenuEntry<T>> items,
    required Offset position,
    T? initialValue,
    double? elevation,
    Color? color,
    ShapeBorder? shape,
  }) {
    return showMenu<T>(
      context: this,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx,
        position.dy,
      ),
      items: items,
      initialValue: initialValue,
      elevation: elevation,
      color: color,
      shape: shape,
    );
  }
  
  /// 显示日期选择器
  Future<DateTime?> showDatePicker({
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    DatePickerMode initialDatePickerMode = DatePickerMode.day,
    String? helpText,
    String? cancelText,
    String? confirmText,
  }) {
    return showDatePicker(
      context: this,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      initialDatePickerMode: initialDatePickerMode,
      helpText: helpText,
      cancelText: cancelText,
      confirmText: confirmText,
    );
  }
  
  /// 显示时间选择器
  Future<TimeOfDay?> showTimePicker({
    TimeOfDay? initialTime,
    String? helpText,
    String? cancelText,
    String? confirmText,
  }) {
    return showTimePicker(
      context: this,
      initialTime: initialTime ?? TimeOfDay.now(),
      helpText: helpText,
      cancelText: cancelText,
      confirmText: confirmText,
    );
  }
}