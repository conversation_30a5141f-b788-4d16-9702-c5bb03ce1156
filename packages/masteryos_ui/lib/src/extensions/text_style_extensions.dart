import 'package:flutter/material.dart';

/// 文本样式扩展，提供更多文本样式操作方法
extension TextStyleExtensions on TextStyle {
  /// 创建响应式文本样式
  TextStyle responsive(BuildContext context, {
    double? mobileScale,
    double? tabletScale,
    double? desktopScale,
  }) {
    final width = MediaQuery.of(context).size.width;
    double scale = 1.0;
    
    if (width < 768) {
      scale = mobileScale ?? 1.0;
    } else if (width < 1024) {
      scale = tabletScale ?? 1.1;
    } else {
      scale = desktopScale ?? 1.2;
    }
    
    return copyWith(fontSize: (fontSize ?? 14) * scale);
  }
  
  /// 设置文本阴影
  TextStyle withShadow({
    Color color = Colors.black54,
    Offset offset = const Offset(1, 1),
    double blurRadius = 2.0,
  }) {
    return copyWith(
      shadows: [
        Shadow(
          color: color,
          offset: offset,
          blurRadius: blurRadius,
        ),
      ],
    );
  }
  
  /// 设置文本描边
  TextStyle withStroke({
    Color color = Colors.black,
    double width = 1.0,
  }) {
    return copyWith(
      foreground: Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = width
        ..color = color,
    );
  }
  
  /// 设置渐变文本
  TextStyle withGradient(Gradient gradient) {
    return copyWith(
      foreground: Paint()..shader = gradient.createShader(
        const Rect.fromLTWH(0, 0, 200, 70),
      ),
    );
  }
  
  /// 应用动画效果的文本样式
  TextStyle animate({
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    // 这里可以返回带有动画配置信息的样式
    // 实际动画需要在使用时配合AnimatedDefaultTextStyle等Widget
    return this;
  }
  
  /// 设置文本间距（字符间距和单词间距）
  TextStyle spacing({
    double? letterSpacing,
    double? wordSpacing,
  }) {
    return copyWith(
      letterSpacing: letterSpacing ?? this.letterSpacing,
      wordSpacing: wordSpacing ?? this.wordSpacing,
    );
  }
  
  /// 设置紧凑间距
  TextStyle get tight => copyWith(letterSpacing: -0.5);
  
  /// 设置宽松间距
  TextStyle get loose => copyWith(letterSpacing: 1.0);
  
  /// 设置超宽松间距
  TextStyle get extraLoose => copyWith(letterSpacing: 2.0);
  
  /// 应用材料设计排版样式
  TextStyle material(TextTheme textTheme) {
    return merge(textTheme.bodyMedium);
  }
  
  /// 创建按钮文本样式
  TextStyle get button => copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );
  
  /// 创建标题文本样式
  TextStyle get headline => copyWith(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    height: 1.2,
  );
  
  /// 创建副标题文本样式
  TextStyle get subtitle => copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );
  
  /// 创建说明文本样式
  TextStyle get caption => copyWith(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    height: 1.3,
  );
  
  /// 创建代码文本样式
  TextStyle get code => copyWith(
    fontFamily: 'monospace',
    fontSize: 14,
    backgroundColor: Colors.grey[100],
  );
  
  /// 应用主题色彩
  TextStyle themed(ColorScheme colorScheme) {
    return copyWith(color: colorScheme.onSurface);
  }
  
  /// 应用主色调
  TextStyle primary(ColorScheme colorScheme) {
    return copyWith(color: colorScheme.primary);
  }
  
  /// 应用次要色调
  TextStyle secondary(ColorScheme colorScheme) {
    return copyWith(color: colorScheme.secondary);
  }
  
  /// 应用错误色调
  TextStyle error(ColorScheme colorScheme) {
    return copyWith(color: colorScheme.error);
  }
  
  /// 应用成功色调
  TextStyle success() {
    return copyWith(color: Colors.green);
  }
  
  /// 应用警告色调
  TextStyle warning() {
    return copyWith(color: Colors.orange);
  }
  
  /// 应用信息色调
  TextStyle info() {
    return copyWith(color: Colors.blue);
  }
  
  /// 设置可点击样式
  TextStyle get clickable => copyWith(
    color: Colors.blue,
    decoration: TextDecoration.underline,
  );
  
  /// 设置禁用样式
  TextStyle get disabled => copyWith(
    color: Colors.grey,
  );
  
  /// 设置加粗样式的变体
  TextStyle get extraBold => copyWith(fontWeight: FontWeight.w800);
  TextStyle get black => copyWith(fontWeight: FontWeight.w900);
  
  /// 设置字重的数值方法
  TextStyle weight(int weight) {
    return copyWith(fontWeight: FontWeight.values[weight ~/ 100 - 1]);
  }
  
  /// 创建多行文本样式
  TextStyle multiline({double? height}) {
    return copyWith(height: height ?? 1.5);
  }
  
  /// 创建单行文本样式
  TextStyle singleLine() {
    return copyWith(height: 1.0);
  }
  
  /// 应用文本对齐相关的样式
  TextStyle aligned(TextAlign alignment) {
    // 注意：TextAlign不是TextStyle的属性，这里只是示例
    // 实际使用时需要在Text widget中设置textAlign属性
    return this;
  }
  
  /// 设置文本缩放
  TextStyle scale(double factor) {
    return copyWith(fontSize: (fontSize ?? 14) * factor);
  }
  
  /// 创建链接样式
  TextStyle get link => copyWith(
    color: Colors.blue,
    decoration: TextDecoration.underline,
    decorationColor: Colors.blue,
  );
  
  /// 创建强调样式
  TextStyle get emphasis => copyWith(
    fontWeight: FontWeight.w600,
    fontStyle: FontStyle.italic,
  );
  
  /// 应用条件样式
  TextStyle when(bool condition, TextStyle Function(TextStyle) modifier) {
    return condition ? modifier(this) : this;
  }
  
  /// 应用多个样式修饰符
  TextStyle apply(List<TextStyle Function(TextStyle)> modifiers) {
    return modifiers.fold(this, (style, modifier) => modifier(style));
  }
}