import 'package:flutter/material.dart';

/// 颜色扩展，提供更多颜色操作方法
extension ColorExtensions on Color {
  /// 将颜色转换为十六进制字符串
  String toHex() {
    return '#${toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
  }
  
  /// 从十六进制字符串创建颜色
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
  
  /// 获取颜色的亮度值
  double get brightness {
    final redVal = (r * 255.0).round() & 0xff;
    final greenVal = (g * 255.0).round() & 0xff;
    final blueVal = (b * 255.0).round() & 0xff;
    return (0.299 * redVal + 0.587 * greenVal + 0.114 * blueVal) / 255;
  }
  
  /// 根据亮度调整颜色
  Color adjustBrightness(double factor) {
    if (factor > 0) {
      // 变亮
      return Color.lerp(this, Colors.white, factor) ?? this;
    } else {
      // 变暗
      return Color.lerp(this, Colors.black, -factor) ?? this;
    }
  }
  
  /// 获取颜色的饱和度调整版本
  Color adjustSaturation(double factor) {
    final hslColor = HSLColor.fromColor(this);
    final newSaturation = (hslColor.saturation + factor).clamp(0.0, 1.0);
    return hslColor.withSaturation(newSaturation).toColor();
  }
  
  /// 获取颜色的色相调整版本
  Color adjustHue(double degrees) {
    final hslColor = HSLColor.fromColor(this);
    final newHue = (hslColor.hue + degrees) % 360;
    return hslColor.withHue(newHue).toColor();
  }
  
  /// 获取互补色
  Color get complementary {
    return adjustHue(180);
  }
  
  /// 获取类似色（+30度和-30度）
  List<Color> get analogous {
    return [
      adjustHue(-30),
      this,
      adjustHue(30),
    ];
  }
  
  /// 获取三元色（120度间隔）
  List<Color> get triadic {
    return [
      this,
      adjustHue(120),
      adjustHue(240),
    ];
  }
  
  /// 获取较亮的颜色版本
  Color get lighter => adjustBrightness(0.2);
  
  /// 获取较暗的颜色版本
  Color get darker => adjustBrightness(-0.2);
  
  /// 获取四元色（90度间隔）
  List<Color> get tetradic {
    return [
      this,
      adjustHue(90),
      adjustHue(180),
      adjustHue(270),
    ];
  }
  
  /// 获取单色调色板（不同亮度）
  List<Color> get monochromatic {
    return [
      adjustBrightness(-0.4),
      adjustBrightness(-0.2),
      this,
      adjustBrightness(0.2),
      adjustBrightness(0.4),
    ];
  }
  
  /// 混合两种颜色
  Color blend(Color other, double ratio) {
    return Color.lerp(this, other, ratio) ?? this;
  }
  
  /// 获取颜色的材料设计色板
  MaterialColor toMaterialColor() {
    final swatch = <int, Color>{};
    final hsl = HSLColor.fromColor(this);
    
    swatch[50] = hsl.withLightness(0.95).toColor();
    swatch[100] = hsl.withLightness(0.9).toColor();
    swatch[200] = hsl.withLightness(0.8).toColor();
    swatch[300] = hsl.withLightness(0.7).toColor();
    swatch[400] = hsl.withLightness(0.6).toColor();
    swatch[500] = this;
    swatch[600] = hsl.withLightness(0.4).toColor();
    swatch[700] = hsl.withLightness(0.3).toColor();
    swatch[800] = hsl.withLightness(0.2).toColor();
    swatch[900] = hsl.withLightness(0.1).toColor();
    
    return MaterialColor(toARGB32(), swatch);
  }
  
  /// 检查颜色对比度是否足够（WCAG标准）
  bool hasGoodContrast(Color other, {double threshold = 4.5}) {
    return calculateContrast(other) >= threshold;
  }
  
  /// 计算与另一种颜色的对比度
  double calculateContrast(Color other) {
    final luminance1 = computeLuminance();
    final luminance2 = other.computeLuminance();
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }
  
  /// 获取最佳前景色（黑色或白色）
  Color get bestForegroundColor {
    return hasGoodContrast(Colors.white) ? Colors.white : Colors.black;
  }
  
  /// 转换为HSL颜色
  HSLColor toHSL() => HSLColor.fromColor(this);
  
  /// 转换为HSV颜色
  HSVColor toHSV() => HSVColor.fromColor(this);
  
  /// 创建渐变色
  LinearGradient createGradient({
    Color? endColor,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    final colors = endColor != null ? [this, endColor] : [lighter, this, darker];
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: stops,
    );
  }
  
  /// 创建径向渐变色
  RadialGradient createRadialGradient({
    Color? endColor,
    AlignmentGeometry center = Alignment.center,
    double radius = 0.5,
    List<double>? stops,
  }) {
    final colors = endColor != null ? [this, endColor] : [this, darker];
    return RadialGradient(
      center: center,
      radius: radius,
      colors: colors,
      stops: stops,
    );
  }
  
  /// 应用透明度渐变
  List<Color> createOpacityGradient(int steps) {
    final colors = <Color>[];
    for (int i = 0; i < steps; i++) {
      final opacity = i / (steps - 1);
      colors.add(withValues(alpha: opacity));
    }
    return colors;
  }
  
  /// 检查是否为暖色调
  bool get isWarm {
    final hue = toHSL().hue;
    return (hue >= 0 && hue <= 60) || (hue >= 300 && hue <= 360);
  }
  
  /// 检查是否为冷色调
  bool get isCool => !isWarm;
  
  /// 获取温度调整后的颜色
  Color adjustTemperature(double factor) {
    if (factor > 0) {
      // 变暖（偏红/黄）
      return blend(const Color(0xFFFF8C00), factor.abs().clamp(0.0, 0.3));
    } else {
      // 变冷（偏蓝）
      return blend(const Color(0xFF4169E1), factor.abs().clamp(0.0, 0.3));
    }
  }
}