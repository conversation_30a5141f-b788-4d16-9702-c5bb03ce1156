import 'package:flutter/material.dart';
import '../theme/theme_constants.dart';

/// 屏幕工具类
class ScreenUtils {
  ScreenUtils._();

  /// 获取屏幕类型
  static ScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < ThemeConstants.breakpointSM) {
      return ScreenType.mobile;
    } else if (width < ThemeConstants.breakpointLG) {
      return ScreenType.tablet;
    } else {
      return ScreenType.desktop;
    }
  }
  
  /// 获取响应式值
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile;
      case ScreenType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
  
  /// 获取响应式列数
  static int getResponsiveColumns(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
    );
  }
  
  /// 获取响应式间距
  static double getResponsiveSpacing(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: ThemeConstants.spaceMD,
      tablet: ThemeConstants.spaceLG,
      desktop: ThemeConstants.spaceXL,
    );
  }
  
  /// 获取响应式内边距
  static EdgeInsets getResponsivePadding(BuildContext context) {
    return EdgeInsets.all(getResponsiveSpacing(context));
  }
  
  /// 获取响应式字体大小
  static double getResponsiveFontSize(
    BuildContext context, {
    required double baseFontSize,
    double scaleFactor = 0.1,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return baseFontSize;
      case ScreenType.tablet:
        return baseFontSize + (baseFontSize * scaleFactor);
      case ScreenType.desktop:
        return baseFontSize + (baseFontSize * scaleFactor * 2);
    }
  }
  
  /// 是否为横屏
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }
  
  /// 是否为竖屏
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }
  
  /// 获取安全区域
  static EdgeInsets getSafeArea(BuildContext context) {
    return MediaQuery.of(context).padding;
  }
  
  /// 获取键盘高度
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }
  
  /// 是否显示键盘
  static bool isKeyboardVisible(BuildContext context) {
    return getKeyboardHeight(context) > 0;
  }
  
  /// 获取应用栏高度
  static double getAppBarHeight() {
    return kToolbarHeight;
  }
  
  /// 获取底部导航栏高度
  static double getBottomNavigationBarHeight() {
    return kBottomNavigationBarHeight;
  }
  
  /// 获取状态栏高度
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }
  
  /// 获取可用屏幕高度（排除状态栏、应用栏等）
  static double getAvailableHeight(
    BuildContext context, {
    bool excludeAppBar = true,
    bool excludeBottomNav = false,
  }) {
    double height = MediaQuery.of(context).size.height;
    
    // 减去状态栏高度
    height -= getStatusBarHeight(context);
    
    // 减去应用栏高度
    if (excludeAppBar) {
      height -= getAppBarHeight();
    }
    
    // 减去底部导航栏高度
    if (excludeBottomNav) {
      height -= getBottomNavigationBarHeight();
    }
    
    // 减去底部安全区域
    height -= MediaQuery.of(context).padding.bottom;
    
    return height;
  }
  
  /// 获取断点配置
  static Map<String, double> get breakpoints => {
    'xs': ThemeConstants.breakpointXS,
    'sm': ThemeConstants.breakpointSM,
    'md': ThemeConstants.breakpointMD,
    'lg': ThemeConstants.breakpointLG,
    'xl': ThemeConstants.breakpointXL,
    '2xl': ThemeConstants.breakpoint2XL,
  };
  
  /// 判断屏幕宽度是否匹配断点
  static bool matchesBreakpoint(BuildContext context, String breakpoint) {
    final width = MediaQuery.of(context).size.width;
    final breakpointValue = breakpoints[breakpoint];
    
    if (breakpointValue == null) return false;
    
    return width >= breakpointValue;
  }
  
  /// 获取网格列数配置
  static int getGridColumns(
    BuildContext context, {
    int mobile = 1,
    int tablet = 2,
    int desktop = 3,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
  
  /// 获取最大内容宽度
  static double getMaxContentWidth(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: double.infinity,
      tablet: 768,
      desktop: 1200,
    );
  }
}

/// 屏幕类型枚举
enum ScreenType {
  mobile,
  tablet,
  desktop,
}