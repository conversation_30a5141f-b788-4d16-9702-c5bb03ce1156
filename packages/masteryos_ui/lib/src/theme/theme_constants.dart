import 'package:flutter/material.dart';

/// 主题常量定义
class ThemeConstants {
  ThemeConstants._();

  // 颜色常量
  static const Color primaryColor = Color(0xFF2563EB); // Blue-600
  static const Color primaryLightColor = Color(0xFF3B82F6); // Blue-500
  static const Color primaryDarkColor = Color(0xFF1D4ED8); // Blue-700
  
  static const Color secondaryColor = Color(0xFF10B981); // Emerald-500
  static const Color secondaryLightColor = Color(0xFF34D399); // Emerald-400
  static const Color secondaryDarkColor = Color(0xFF059669); // Emerald-600
  
  static const Color errorColor = Color(0xFFEF4444); // Red-500
  static const Color warningColor = Color(0xFFF59E0B); // Amber-500
  static const Color successColor = Color(0xFF10B981); // Emerald-500
  static const Color infoColor = Color(0xFF3B82F6); // Blue-500
  
  // 中性色
  static const Color backgroundColor = Color(0xFFFAFAFA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  
  // 文字颜色
  static const Color textPrimaryColor = Color(0xFF111827); // Gray-900
  static const Color textSecondaryColor = Color(0xFF6B7280); // Gray-500
  static const Color textDisabledColor = Color(0xFF9CA3AF); // Gray-400
  static const Color textOnPrimaryColor = Color(0xFFFFFFFF);
  
  // 边框颜色
  static const Color borderColor = Color(0xFFE5E7EB); // Gray-200
  static const Color borderFocusColor = Color(0xFF3B82F6); // Blue-500
  static const Color borderErrorColor = Color(0xFFEF4444); // Red-500
  
  // 阴影颜色
  static const Color shadowColor = Color(0x1A000000);
  static const Color elevationShadowColor = Color(0x0F000000);
  
  // 尺寸常量
  static const double radiusXS = 2.0;
  static const double radiusSM = 4.0;
  static const double radiusMD = 6.0;
  static const double radiusLG = 8.0;
  static const double radiusXL = 12.0;
  static const double radius2XL = 16.0;
  static const double radius3XL = 24.0;
  static const double radiusFull = 9999.0;
  
  // 间距常量
  static const double spaceXS = 4.0;
  static const double spaceSM = 8.0;
  static const double spaceMD = 12.0;
  static const double spaceLG = 16.0;
  static const double spaceXL = 20.0;
  static const double space2XL = 24.0;
  static const double space3XL = 32.0;
  static const double space4XL = 40.0;
  static const double space5XL = 48.0;
  static const double space6XL = 56.0;
  
  // 字体大小
  static const double fontSizeXS = 12.0;
  static const double fontSizeSM = 14.0;
  static const double fontSizeMD = 16.0;
  static const double fontSizeLG = 18.0;
  static const double fontSizeXL = 20.0;
  static const double fontSize2XL = 24.0;
  static const double fontSize3XL = 30.0;
  static const double fontSize4XL = 36.0;
  static const double fontSize5XL = 48.0;
  static const double fontSize6XL = 60.0;
  
  // 行高
  static const double lineHeightTight = 1.25;
  static const double lineHeightSnug = 1.375;
  static const double lineHeightNormal = 1.5;
  static const double lineHeightRelaxed = 1.625;
  static const double lineHeightLoose = 2.0;
  
  // 字重
  static const FontWeight fontWeightThin = FontWeight.w100;
  static const FontWeight fontWeightExtraLight = FontWeight.w200;
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightNormal = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemibold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;
  static const FontWeight fontWeightBlack = FontWeight.w900;
  
  // 动画时长
  static const Duration animationDurationFast = Duration(milliseconds: 150);
  static const Duration animationDurationNormal = Duration(milliseconds: 300);
  static const Duration animationDurationSlow = Duration(milliseconds: 500);
  
  // 动画曲线
  static const Curve animationCurveEaseIn = Curves.easeIn;
  static const Curve animationCurveEaseOut = Curves.easeOut;
  static const Curve animationCurveEaseInOut = Curves.easeInOut;
  static const Curve animationCurveElastic = Curves.elasticOut;
  static const Curve animationCurveBounce = Curves.bounceOut;
  
  // 阴影
  static const List<BoxShadow> shadowSM = [
    BoxShadow(
      color: shadowColor,
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> shadowMD = [
    BoxShadow(
      color: shadowColor,
      offset: Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -1,
    ),
    BoxShadow(
      color: elevationShadowColor,
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: -1,
    ),
  ];
  
  static const List<BoxShadow> shadowLG = [
    BoxShadow(
      color: shadowColor,
      offset: Offset(0, 10),
      blurRadius: 15,
      spreadRadius: -3,
    ),
    BoxShadow(
      color: elevationShadowColor,
      offset: Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -2,
    ),
  ];
  
  static const List<BoxShadow> shadowXL = [
    BoxShadow(
      color: shadowColor,
      offset: Offset(0, 20),
      blurRadius: 25,
      spreadRadius: -5,
    ),
    BoxShadow(
      color: elevationShadowColor,
      offset: Offset(0, 10),
      blurRadius: 10,
      spreadRadius: -5,
    ),
  ];
  
  // 断点常量
  static const double breakpointXS = 475;
  static const double breakpointSM = 640;
  static const double breakpointMD = 768;
  static const double breakpointLG = 1024;
  static const double breakpointXL = 1280;
  static const double breakpoint2XL = 1536;
  
  // Z-index常量
  static const int zIndexDropdown = 1000;
  static const int zIndexModal = 2000;
  static const int zIndexDrawer = 3000;
  static const int zIndexTooltip = 4000;
  static const int zIndexPopover = 5000;
}