import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'theme_constants.dart';

/// 应用主题配置
class AppTheme {
  AppTheme._();

  /// 浅色主题
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // 颜色方案
      colorScheme: const ColorScheme.light(
        primary: ThemeConstants.primaryColor,
        primaryContainer: ThemeConstants.primaryLightColor,
        secondary: ThemeConstants.secondaryColor,
        secondaryContainer: ThemeConstants.secondaryLightColor,
        error: ThemeConstants.errorColor,
        surface: ThemeConstants.surfaceColor,
        onPrimary: ThemeConstants.textOnPrimaryColor,
        onSecondary: ThemeConstants.textOnPrimaryColor,
        onError: ThemeConstants.textOnPrimaryColor,
        onSurface: ThemeConstants.textPrimaryColor,
        outline: ThemeConstants.borderColor,
        shadow: ThemeConstants.shadowColor,
      ),

      // 应用栏主题
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: ThemeConstants.surfaceColor,
        foregroundColor: ThemeConstants.textPrimaryColor,
        titleTextStyle: TextStyle(
          color: ThemeConstants.textPrimaryColor,
          fontSize: ThemeConstants.fontSizeLG,
          fontWeight: ThemeConstants.fontWeightSemibold,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),

      // 卡片主题
      cardTheme: CardTheme(
        elevation: 2,
        color: ThemeConstants.cardColor,
        shadowColor: ThemeConstants.shadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusLG),
        ),
      ),

      // 输入装饰主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: ThemeConstants.surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          borderSide: const BorderSide(color: ThemeConstants.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          borderSide: const BorderSide(color: ThemeConstants.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          borderSide: const BorderSide(
              color: ThemeConstants.borderFocusColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          borderSide: const BorderSide(color: ThemeConstants.borderErrorColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          borderSide: const BorderSide(
              color: ThemeConstants.borderErrorColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceLG,
          vertical: ThemeConstants.spaceMD,
        ),
        hintStyle: const TextStyle(
          color: ThemeConstants.textSecondaryColor,
          fontSize: ThemeConstants.fontSizeMD,
        ),
        labelStyle: const TextStyle(
          color: ThemeConstants.textSecondaryColor,
          fontSize: ThemeConstants.fontSizeSM,
        ),
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          backgroundColor: ThemeConstants.primaryColor,
          foregroundColor: ThemeConstants.textOnPrimaryColor,
          padding: const EdgeInsets.symmetric(
            horizontal: ThemeConstants.spaceXL,
            vertical: ThemeConstants.spaceMD,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          ),
          textStyle: const TextStyle(
            fontSize: ThemeConstants.fontSizeMD,
            fontWeight: ThemeConstants.fontWeightMedium,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: ThemeConstants.primaryColor,
          padding: const EdgeInsets.symmetric(
            horizontal: ThemeConstants.spaceXL,
            vertical: ThemeConstants.spaceMD,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          ),
          side: const BorderSide(color: ThemeConstants.primaryColor),
          textStyle: const TextStyle(
            fontSize: ThemeConstants.fontSizeMD,
            fontWeight: ThemeConstants.fontWeightMedium,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ThemeConstants.primaryColor,
          padding: const EdgeInsets.symmetric(
            horizontal: ThemeConstants.spaceLG,
            vertical: ThemeConstants.spaceMD,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
          ),
          textStyle: const TextStyle(
            fontSize: ThemeConstants.fontSizeMD,
            fontWeight: ThemeConstants.fontWeightMedium,
          ),
        ),
      ),

      // 图标主题
      iconTheme: const IconThemeData(
        color: ThemeConstants.textSecondaryColor,
        size: 24,
      ),

      // 文本主题
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: ThemeConstants.fontSize5XL,
          fontWeight: ThemeConstants.fontWeightBold,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightTight,
        ),
        displayMedium: TextStyle(
          fontSize: ThemeConstants.fontSize4XL,
          fontWeight: ThemeConstants.fontWeightBold,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightTight,
        ),
        displaySmall: TextStyle(
          fontSize: ThemeConstants.fontSize3XL,
          fontWeight: ThemeConstants.fontWeightBold,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightSnug,
        ),
        headlineLarge: TextStyle(
          fontSize: ThemeConstants.fontSize2XL,
          fontWeight: ThemeConstants.fontWeightSemibold,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightSnug,
        ),
        headlineMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeXL,
          fontWeight: ThemeConstants.fontWeightSemibold,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        headlineSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeLG,
          fontWeight: ThemeConstants.fontWeightSemibold,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        titleLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeMD,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        titleMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeSM,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        titleSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeXS,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: ThemeConstants.textSecondaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        bodyLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeMD,
          fontWeight: ThemeConstants.fontWeightNormal,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightRelaxed,
        ),
        bodyMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeSM,
          fontWeight: ThemeConstants.fontWeightNormal,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        bodySmall: TextStyle(
          fontSize: ThemeConstants.fontSizeXS,
          fontWeight: ThemeConstants.fontWeightNormal,
          color: ThemeConstants.textSecondaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        labelLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeSM,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: ThemeConstants.textPrimaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        labelMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeXS,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: ThemeConstants.textSecondaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
        labelSmall: TextStyle(
          fontSize: 11,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: ThemeConstants.textSecondaryColor,
          height: ThemeConstants.lineHeightNormal,
        ),
      ),

      // 对话框主题
      dialogTheme: DialogTheme(
        backgroundColor: ThemeConstants.surfaceColor,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusXL),
        ),
        titleTextStyle: const TextStyle(
          fontSize: ThemeConstants.fontSizeLG,
          fontWeight: ThemeConstants.fontWeightSemibold,
          color: ThemeConstants.textPrimaryColor,
        ),
      ),

      // 底部导航栏主题
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: ThemeConstants.surfaceColor,
        selectedItemColor: ThemeConstants.primaryColor,
        unselectedItemColor: ThemeConstants.textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // 标签栏主题
      tabBarTheme: const TabBarTheme(
        indicatorColor: ThemeConstants.primaryColor,
        labelColor: ThemeConstants.primaryColor,
        unselectedLabelColor: ThemeConstants.textSecondaryColor,
        labelStyle: TextStyle(
          fontSize: ThemeConstants.fontSizeSM,
          fontWeight: ThemeConstants.fontWeightMedium,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: ThemeConstants.fontSizeSM,
          fontWeight: ThemeConstants.fontWeightNormal,
        ),
      ),

      // 分割线主题
      dividerTheme: const DividerThemeData(
        color: ThemeConstants.borderColor,
        thickness: 1,
        space: 1,
      ),
    );
  }

  /// 深色主题
  static ThemeData get darkTheme {
    return lightTheme.copyWith(
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: ThemeConstants.primaryLightColor,
        primaryContainer: ThemeConstants.primaryDarkColor,
        secondary: ThemeConstants.secondaryLightColor,
        secondaryContainer: ThemeConstants.secondaryDarkColor,
        error: ThemeConstants.errorColor,
        surface: Color(0xFF1F2937), // Gray-900
        onPrimary: ThemeConstants.textOnPrimaryColor,
        onSecondary: ThemeConstants.textOnPrimaryColor,
        onError: ThemeConstants.textOnPrimaryColor,
        onSurface: Color(0xFFF9FAFB), // Gray-50
        outline: Color(0xFF374151), // Gray-700
        shadow: Color(0x40000000),
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: Color(0xFF1F2937), // Gray-800
        foregroundColor: Color(0xFFF9FAFB), // Gray-50
        titleTextStyle: TextStyle(
          color: Color(0xFFF9FAFB), // Gray-50
          fontSize: ThemeConstants.fontSizeLG,
          fontWeight: ThemeConstants.fontWeightSemibold,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        color: const Color(0xFF1F2937), // Gray-800
        shadowColor: const Color(0x40000000),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusLG),
        ),
      ),
    );
  }
}
