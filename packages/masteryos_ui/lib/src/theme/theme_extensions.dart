import 'package:flutter/material.dart';

import 'theme_constants.dart';

/// 主题扩展类，提供便捷的主题访问方法
extension ThemeExtensions on BuildContext {
  /// 获取主题数据
  ThemeData get theme => Theme.of(this);

  /// 获取颜色方案
  ColorScheme get colorScheme => theme.colorScheme;

  /// 获取文本主题
  TextTheme get textTheme => theme.textTheme;

  /// 是否为深色模式
  bool get isDarkMode => theme.brightness == Brightness.dark;

  /// 是否为浅色模式
  bool get isLightMode => theme.brightness == Brightness.light;

  /// 获取屏幕尺寸
  Size get screenSize => MediaQuery.of(this).size;

  /// 获取屏幕宽度
  double get screenWidth => screenSize.width;

  /// 获取屏幕高度
  double get screenHeight => screenSize.height;

  /// 获取设备像素比
  double get devicePixelRatio => MediaQuery.of(this).devicePixelRatio;

  /// 获取状态栏高度
  double get statusBarHeight => MediaQuery.of(this).padding.top;

  /// 获取底部安全区域高度
  double get bottomPadding => MediaQuery.of(this).padding.bottom;

  /// 判断是否为移动设备
  bool get isMobile => screenWidth < ThemeConstants.breakpointMD;

  /// 判断是否为平板设备
  bool get isTablet =>
      screenWidth >= ThemeConstants.breakpointMD &&
      screenWidth < ThemeConstants.breakpointLG;

  /// 判断是否为桌面设备
  bool get isDesktop => screenWidth >= ThemeConstants.breakpointLG;

  /// 判断是否为小屏幕
  bool get isSmallScreen => screenWidth < ThemeConstants.breakpointSM;

  /// 判断是否为中等屏幕
  bool get isMediumScreen =>
      screenWidth >= ThemeConstants.breakpointSM &&
      screenWidth < ThemeConstants.breakpointLG;

  /// 判断是否为大屏幕
  bool get isLargeScreen => screenWidth >= ThemeConstants.breakpointLG;

  /// 快速访问常用颜色
  Color get primaryColor => colorScheme.primary;
  Color get secondaryColor => colorScheme.secondary;
  Color get backgroundColor => colorScheme.surface;
  Color get surfaceColor => colorScheme.surface;
  Color get errorColor => colorScheme.error;
  Color get onPrimaryColor => colorScheme.onPrimary;
  Color get onSecondaryColor => colorScheme.onSecondary;
  Color get onBackgroundColor => colorScheme.onSurface;
  Color get onSurfaceColor => colorScheme.onSurface;
  Color get onErrorColor => colorScheme.onError;

  /// 快速访问文本样式
  TextStyle? get displayLarge => textTheme.displayLarge;
  TextStyle? get displayMedium => textTheme.displayMedium;
  TextStyle? get displaySmall => textTheme.displaySmall;
  TextStyle? get headlineLarge => textTheme.headlineLarge;
  TextStyle? get headlineMedium => textTheme.headlineMedium;
  TextStyle? get headlineSmall => textTheme.headlineSmall;
  TextStyle? get titleLarge => textTheme.titleLarge;
  TextStyle? get titleMedium => textTheme.titleMedium;
  TextStyle? get titleSmall => textTheme.titleSmall;
  TextStyle? get bodyLarge => textTheme.bodyLarge;
  TextStyle? get bodyMedium => textTheme.bodyMedium;
  TextStyle? get bodySmall => textTheme.bodySmall;
  TextStyle? get labelLarge => textTheme.labelLarge;
  TextStyle? get labelMedium => textTheme.labelMedium;
  TextStyle? get labelSmall => textTheme.labelSmall;
}

/// 颜色扩展，提供常用的颜色变体
extension ColorExtensions on Color {
  /// 获取颜色的浅色变体
  Color get lighter {
    return Color.lerp(this, Colors.white, 0.3) ?? this;
  }

  /// 获取颜色的深色变体
  Color get darker {
    return Color.lerp(this, Colors.black, 0.3) ?? this;
  }

  /// 获取颜色的半透明变体
  Color withOpacity(double opacity) {
    return Color.fromRGBO((r * 255.0).round() & 0xff, (g * 255.0).round() & 0xff, (b * 255.0).round() & 0xff, opacity);
  }

  /// 判断颜色是否为浅色
  bool get isLight {
    final luminance = computeLuminance();
    return luminance > 0.5;
  }

  /// 判断颜色是否为深色
  bool get isDark => !isLight;

  /// 获取对比色（黑色或白色）
  Color get contrastColor => isLight ? Colors.black : Colors.white;
}

/// 文本样式扩展
extension TextStyleExtensions on TextStyle {
  /// 设置字体大小
  TextStyle fontSize(double size) => copyWith(fontSize: size);

  /// 设置字体重量
  TextStyle fontWeight(FontWeight weight) => copyWith(fontWeight: weight);

  /// 设置字体颜色
  TextStyle color(Color color) => copyWith(color: color);

  /// 设置行高
  TextStyle lineHeight(double height) => copyWith(height: height);

  /// 设置字间距
  TextStyle letterSpacing(double spacing) => copyWith(letterSpacing: spacing);

  /// 设置为粗体
  TextStyle get bold => copyWith(fontWeight: FontWeight.bold);

  /// 设置为半粗体
  TextStyle get semiBold => copyWith(fontWeight: FontWeight.w600);

  /// 设置为中等字重
  TextStyle get medium => copyWith(fontWeight: FontWeight.w500);

  /// 设置为正常字重
  TextStyle get normal => copyWith(fontWeight: FontWeight.normal);

  /// 设置为浅色字重
  TextStyle get light => copyWith(fontWeight: FontWeight.w300);

  /// 设置为斜体
  TextStyle get italic => copyWith(fontStyle: FontStyle.italic);

  /// 设置下划线
  TextStyle get underline => copyWith(decoration: TextDecoration.underline);

  /// 设置删除线
  TextStyle get lineThrough => copyWith(decoration: TextDecoration.lineThrough);
}

/// Widget扩展，提供便捷的间距和装饰方法
extension WidgetExtensions on Widget {
  /// 添加内边距
  Widget padding(EdgeInsets padding) => Padding(padding: padding, child: this);

  /// 添加对称内边距
  Widget paddingSymmetric({double vertical = 0, double horizontal = 0}) =>
      Padding(
        padding:
            EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal),
        child: this,
      );

  /// 添加统一内边距
  Widget paddingAll(double padding) =>
      Padding(padding: EdgeInsets.all(padding), child: this);

  /// 添加只有特定方向的内边距
  Widget paddingOnly({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) =>
      Padding(
        padding:
            EdgeInsets.only(left: left, top: top, right: right, bottom: bottom),
        child: this,
      );

  /// 添加外边距
  Widget margin(EdgeInsets margin) => Container(margin: margin, child: this);

  /// 添加对称外边距
  Widget marginSymmetric({double vertical = 0, double horizontal = 0}) =>
      Container(
        margin:
            EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal),
        child: this,
      );

  /// 添加统一外边距
  Widget marginAll(double margin) =>
      Container(margin: EdgeInsets.all(margin), child: this);

  /// 添加只有特定方向的外边距
  Widget marginOnly({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) =>
      Container(
        margin:
            EdgeInsets.only(left: left, top: top, right: right, bottom: bottom),
        child: this,
      );

  /// 居中对齐
  Widget get center => Center(child: this);

  /// 左对齐
  Widget get alignLeft => Align(alignment: Alignment.centerLeft, child: this);

  /// 右对齐
  Widget get alignRight => Align(alignment: Alignment.centerRight, child: this);

  /// 顶部对齐
  Widget get alignTop => Align(alignment: Alignment.topCenter, child: this);

  /// 底部对齐
  Widget get alignBottom =>
      Align(alignment: Alignment.bottomCenter, child: this);

  /// 扩展填充
  Widget get expanded => Expanded(child: this);

  /// 灵活布局
  Widget flexible({int flex = 1}) => Flexible(flex: flex, child: this);

  /// 添加点击事件
  Widget onTap(VoidCallback onTap) =>
      GestureDetector(onTap: onTap, child: this);

  /// 添加长按事件
  Widget onLongPress(VoidCallback onLongPress) =>
      GestureDetector(onLongPress: onLongPress, child: this);

  /// 设置可见性
  Widget visible(bool visible) => Visibility(visible: visible, child: this);

  /// 条件显示
  Widget showIf(bool condition) => condition ? this : const SizedBox.shrink();
}
