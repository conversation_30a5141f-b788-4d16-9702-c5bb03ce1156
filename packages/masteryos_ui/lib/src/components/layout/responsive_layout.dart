import 'package:flutter/material.dart';
import '../../utils/screen_utils.dart';

/// 响应式布局组件
class ResponsiveLayout extends StatelessWidget {
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.breakpoints,
  });

  /// 移动端布局
  final Widget mobile;
  
  /// 平板端布局
  final Widget? tablet;
  
  /// 桌面端布局
  final Widget? desktop;
  
  /// 自定义断点
  final Map<String, double>? breakpoints;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final customBreakpoints = breakpoints ?? ScreenUtils.breakpoints;
        
        if (width >= (customBreakpoints['lg'] ?? 1024)) {
          return desktop ?? tablet ?? mobile;
        } else if (width >= (customBreakpoints['md'] ?? 768)) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}

/// 响应式建造者组件
class ResponsiveBuilder extends StatelessWidget {
  const ResponsiveBuilder({
    super.key,
    required this.builder,
    this.breakpoints,
  });

  /// 建造者函数
  final Widget Function(BuildContext context, ScreenType screenType, BoxConstraints constraints) builder;
  
  /// 自定义断点
  final Map<String, double>? breakpoints;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenType = _getScreenType(constraints.maxWidth);
        return builder(context, screenType, constraints);
      },
    );
  }
  
  ScreenType _getScreenType(double width) {
    final customBreakpoints = breakpoints ?? ScreenUtils.breakpoints;
    
    if (width >= (customBreakpoints['lg'] ?? 1024)) {
      return ScreenType.desktop;
    } else if (width >= (customBreakpoints['md'] ?? 768)) {
      return ScreenType.tablet;
    } else {
      return ScreenType.mobile;
    }
  }
}

/// 响应式值组件
class ResponsiveValue<T> {
  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  final T mobile;
  final T? tablet;
  final T? desktop;

  T getValue(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile;
      case ScreenType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}

/// 响应式容器组件
class ResponsiveContainer extends StatelessWidget {
  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
    this.alignment = Alignment.center,
  });

  final Widget child;
  final ResponsiveValue<double>? maxWidth;
  final ResponsiveValue<EdgeInsets>? padding;
  final ResponsiveValue<EdgeInsets>? margin;
  final Alignment alignment;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, constraints) {
        final containerMaxWidth = maxWidth?.getValue(screenType);
        final containerPadding = padding?.getValue(screenType);
        final containerMargin = margin?.getValue(screenType);

        return Align(
          alignment: alignment,
          child: Container(
            width: containerMaxWidth,
            constraints: containerMaxWidth != null
                ? BoxConstraints(maxWidth: containerMaxWidth)
                : null,
            padding: containerPadding,
            margin: containerMargin,
            child: child,
          ),
        );
      },
    );
  }
}

/// 响应式网格组件
class ResponsiveGrid extends StatelessWidget {
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.columns = const ResponsiveValue(mobile: 1, tablet: 2, desktop: 3),
    this.spacing = 16.0,
    this.runSpacing,
    this.childAspectRatio = 1.0,
  });

  final List<Widget> children;
  final ResponsiveValue<int> columns;
  final double spacing;
  final double? runSpacing;
  final double childAspectRatio;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, constraints) {
        final columnCount = columns.getValue(screenType);
        
        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columnCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: runSpacing ?? spacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}

/// 响应式行组件
class ResponsiveRow extends StatelessWidget {
  const ResponsiveRow({
    super.key,
    required this.children,
    this.spacing = 16.0,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.breakpoint = 768.0,
  });

  final List<Widget> children;
  final double spacing;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final double breakpoint;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final shouldStack = constraints.maxWidth < breakpoint;
        
        if (shouldStack) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: children.map((child) {
              final index = children.indexOf(child);
              return Padding(
                padding: EdgeInsets.only(
                  bottom: index < children.length - 1 ? spacing : 0,
                ),
                child: child,
              );
            }).toList(),
          );
        } else {
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: children.map((child) {
              final index = children.indexOf(child);
              return Expanded(
                child: Padding(
                  padding: EdgeInsets.only(
                    right: index < children.length - 1 ? spacing : 0,
                  ),
                  child: child,
                ),
              );
            }).toList(),
          );
        }
      },
    );
  }
}

/// 响应式间距组件
class ResponsiveSpacing extends StatelessWidget {
  const ResponsiveSpacing({
    super.key,
    this.height,
    this.width,
  });

  final ResponsiveValue<double>? height;
  final ResponsiveValue<double>? width;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, constraints) {
        final h = height?.getValue(screenType);
        final w = width?.getValue(screenType);
        
        return SizedBox(
          height: h,
          width: w,
        );
      },
    );
  }
}

/// 响应式文本组件
class ResponsiveText extends StatelessWidget {
  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.fontSize,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  final String text;
  final TextStyle? style;
  final ResponsiveValue<double>? fontSize;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, constraints) {
        final responsiveFontSize = fontSize?.getValue(screenType);
        
        return Text(
          text,
          style: style?.copyWith(fontSize: responsiveFontSize) ??
              TextStyle(fontSize: responsiveFontSize),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }
}

/// 响应式图片组件
class ResponsiveImage extends StatelessWidget {
  const ResponsiveImage({
    super.key,
    required this.imageProvider,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.alignment = Alignment.center,
  });

  final ImageProvider imageProvider;
  final ResponsiveValue<double>? width;
  final ResponsiveValue<double>? height;
  final BoxFit fit;
  final Alignment alignment;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, constraints) {
        final w = width?.getValue(screenType);
        final h = height?.getValue(screenType);
        
        return Image(
          image: imageProvider,
          width: w,
          height: h,
          fit: fit,
          alignment: alignment,
        );
      },
    );
  }
}