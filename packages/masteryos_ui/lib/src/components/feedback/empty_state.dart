import 'package:flutter/material.dart';
import '../../theme/theme_constants.dart';

/// 空状态组件
class EmptyState extends StatelessWidget {
  const EmptyState({
    super.key,
    this.icon,
    this.title,
    this.message,
    this.action,
    this.illustration,
    this.padding,
    this.spacing = 16.0,
    this.size = EmptyStateSize.medium,
  });

  /// 图标
  final IconData? icon;
  
  /// 标题
  final String? title;
  
  /// 消息内容
  final String? message;
  
  /// 操作按钮
  final Widget? action;
  
  /// 自定义插图
  final Widget? illustration;
  
  /// 内边距
  final EdgeInsets? padding;
  
  /// 元素间距
  final double spacing;
  
  /// 空状态尺寸
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Padding(
      padding: padding ?? _getDefaultPadding(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 插图或图标
          if (illustration != null)
            illustration!
          else if (icon != null)
            Icon(
              icon,
              size: _getIconSize(),
              color: colorScheme.onSurface.withValues(alpha: 0.3),
            ),
          
          if ((illustration != null || icon != null) && (title != null || message != null))
            SizedBox(height: spacing),
          
          // 标题
          if (title != null) ...[
            Text(
              title!,
              style: _getTitleStyle(context),
              textAlign: TextAlign.center,
            ),
            if (message != null) SizedBox(height: spacing / 2),
          ],
          
          // 消息
          if (message != null) ...[
            Text(
              message!,
              style: _getMessageStyle(context),
              textAlign: TextAlign.center,
            ),
            if (action != null) SizedBox(height: spacing),
          ],
          
          // 操作按钮
          if (action != null) action!,
        ],
      ),
    );
  }
  
  EdgeInsets _getDefaultPadding() {
    switch (size) {
      case EmptyStateSize.small:
        return const EdgeInsets.all(ThemeConstants.spaceLG);
      case EmptyStateSize.medium:
        return const EdgeInsets.all(ThemeConstants.space2XL);
      case EmptyStateSize.large:
        return const EdgeInsets.all(ThemeConstants.space3XL);
    }
  }
  
  double _getIconSize() {
    switch (size) {
      case EmptyStateSize.small:
        return 48;
      case EmptyStateSize.medium:
        return 64;
      case EmptyStateSize.large:
        return 80;
    }
  }
  
  TextStyle _getTitleStyle(BuildContext context) {
    final theme = Theme.of(context);
    
    switch (size) {
      case EmptyStateSize.small:
        return theme.textTheme.titleMedium!.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        );
      case EmptyStateSize.medium:
        return theme.textTheme.titleLarge!.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        );
      case EmptyStateSize.large:
        return theme.textTheme.headlineSmall!.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        );
    }
  }
  
  TextStyle _getMessageStyle(BuildContext context) {
    final theme = Theme.of(context);
    
    return theme.textTheme.bodyMedium!.copyWith(
      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
    );
  }
}

/// 空状态尺寸枚举
enum EmptyStateSize {
  small,
  medium,
  large,
}

/// 预定义的空状态组件

/// 无数据状态
class NoDataState extends StatelessWidget {
  const NoDataState({
    super.key,
    this.title = '暂无数据',
    this.message = '当前没有可显示的数据',
    this.action,
    this.size = EmptyStateSize.medium,
  });

  final String title;
  final String message;
  final Widget? action;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.inbox_outlined,
      title: title,
      message: message,
      action: action,
      size: size,
    );
  }
}

/// 搜索无结果状态
class NoSearchResultState extends StatelessWidget {
  const NoSearchResultState({
    super.key,
    this.title = '未找到相关内容',
    this.message = '尝试使用其他关键词或条件进行搜索',
    this.action,
    this.size = EmptyStateSize.medium,
  });

  final String title;
  final String message;
  final Widget? action;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.search_off_outlined,
      title: title,
      message: message,
      action: action,
      size: size,
    );
  }
}

/// 网络错误状态
class NetworkErrorState extends StatelessWidget {
  const NetworkErrorState({
    super.key,
    this.title = '网络连接异常',
    this.message = '请检查网络连接后重试',
    this.onRetry,
    this.size = EmptyStateSize.medium,
  });

  final String title;
  final String message;
  final VoidCallback? onRetry;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.wifi_off_outlined,
      title: title,
      message: message,
      action: onRetry != null
          ? ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
            )
          : null,
      size: size,
    );
  }
}

/// 权限不足状态
class NoPermissionState extends StatelessWidget {
  const NoPermissionState({
    super.key,
    this.title = '权限不足',
    this.message = '您没有访问此内容的权限',
    this.action,
    this.size = EmptyStateSize.medium,
  });

  final String title;
  final String message;
  final Widget? action;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.lock_outline,
      title: title,
      message: message,
      action: action,
      size: size,
    );
  }
}

/// 维护中状态
class MaintenanceState extends StatelessWidget {
  const MaintenanceState({
    super.key,
    this.title = '系统维护中',
    this.message = '系统正在维护，请稍后再试',
    this.action,
    this.size = EmptyStateSize.medium,
  });

  final String title;
  final String message;
  final Widget? action;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.build_outlined,
      title: title,
      message: message,
      action: action,
      size: size,
    );
  }
}

/// 即将推出状态
class ComingSoonState extends StatelessWidget {
  const ComingSoonState({
    super.key,
    this.title = '敬请期待',
    this.message = '此功能即将推出，敬请期待',
    this.action,
    this.size = EmptyStateSize.medium,
  });

  final String title;
  final String message;
  final Widget? action;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.rocket_launch_outlined,
      title: title,
      message: message,
      action: action,
      size: size,
    );
  }
}

/// 完成状态
class CompletedState extends StatelessWidget {
  const CompletedState({
    super.key,
    this.title = '已完成',
    this.message = '所有任务已完成',
    this.action,
    this.size = EmptyStateSize.medium,
  });

  final String title;
  final String message;
  final Widget? action;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.check_circle_outline,
      title: title,
      message: message,
      action: action,
      size: size,
    );
  }
}

/// 自定义插图的空状态
class IllustrationEmptyState extends StatelessWidget {
  const IllustrationEmptyState({
    super.key,
    required this.illustrationPath,
    this.title,
    this.message,
    this.action,
    this.illustrationWidth = 200,
    this.illustrationHeight = 200,
    this.size = EmptyStateSize.medium,
  });

  final String illustrationPath;
  final String? title;
  final String? message;
  final Widget? action;
  final double illustrationWidth;
  final double illustrationHeight;
  final EmptyStateSize size;

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      illustration: Image.asset(
        illustrationPath,
        width: illustrationWidth,
        height: illustrationHeight,
        fit: BoxFit.contain,
      ),
      title: title,
      message: message,
      action: action,
      size: size,
    );
  }
}