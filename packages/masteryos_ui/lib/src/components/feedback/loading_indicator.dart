import 'package:flutter/material.dart';

/// 加载指示器组件
class LoadingIndicator extends StatelessWidget {
  const LoadingIndicator({
    super.key,
    this.size = LoadingIndicatorSize.medium,
    this.color,
    this.strokeWidth,
    this.message,
    this.messageStyle,
    this.spacing = 16.0,
    this.type = LoadingIndicatorType.circular,
  });

  /// 加载器尺寸
  final LoadingIndicatorSize size;

  /// 颜色
  final Color? color;

  /// 线条宽度
  final double? strokeWidth;

  /// 加载消息
  final String? message;

  /// 消息样式
  final TextStyle? messageStyle;

  /// 消息与指示器间距
  final double spacing;

  /// 加载器类型
  final LoadingIndicatorType type;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final indicatorColor = color ?? theme.colorScheme.primary;

    Widget indicator;

    switch (type) {
      case LoadingIndicatorType.circular:
        indicator = CircularProgressIndicator(
          color: indicatorColor,
          strokeWidth: strokeWidth ?? _getStrokeWidth(),
        );
        break;
      case LoadingIndicatorType.linear:
        indicator = LinearProgressIndicator(
          color: indicatorColor,
          backgroundColor: indicatorColor.withValues(alpha: 0.2),
        );
        break;
      case LoadingIndicatorType.dots:
        indicator = _DotsLoadingIndicator(
          color: indicatorColor,
          size: _getDotSize(),
        );
        break;
      case LoadingIndicatorType.pulse:
        indicator = _PulseLoadingIndicator(
          color: indicatorColor,
          size: _getIndicatorSize(),
        );
        break;
    }

    if (type == LoadingIndicatorType.linear) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (message != null) ...[
            Text(
              message!,
              style: messageStyle ?? _getMessageStyle(context),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: spacing),
          ],
          SizedBox(
            width: double.infinity,
            child: indicator,
          ),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: _getIndicatorSize(),
          height: _getIndicatorSize(),
          child: indicator,
        ),
        if (message != null) ...[
          SizedBox(height: spacing),
          Text(
            message!,
            style: messageStyle ?? _getMessageStyle(context),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  double _getIndicatorSize() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 24;
      case LoadingIndicatorSize.medium:
        return 36;
      case LoadingIndicatorSize.large:
        return 48;
    }
  }

  double _getStrokeWidth() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 2;
      case LoadingIndicatorSize.medium:
        return 3;
      case LoadingIndicatorSize.large:
        return 4;
    }
  }

  double _getDotSize() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 6;
      case LoadingIndicatorSize.medium:
        return 8;
      case LoadingIndicatorSize.large:
        return 10;
    }
  }

  TextStyle _getMessageStyle(BuildContext context) {
    final theme = Theme.of(context);
    return theme.textTheme.bodyMedium!.copyWith(
      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
    );
  }
}

/// 加载指示器尺寸枚举
enum LoadingIndicatorSize {
  small,
  medium,
  large,
}

/// 加载指示器类型枚举
enum LoadingIndicatorType {
  circular,
  linear,
  dots,
  pulse,
}

/// 点状加载指示器
class _DotsLoadingIndicator extends StatefulWidget {
  const _DotsLoadingIndicator({
    required this.color,
    required this.size,
  });

  final Color color;
  final double size;

  @override
  State<_DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<_DotsLoadingIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimations();
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startAnimations() async {
    while (mounted) {
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].forward();
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }
      await Future.delayed(const Duration(milliseconds: 200));
      for (final controller in _controllers) {
        if (mounted) {
          controller.reverse();
        }
      }
      await Future.delayed(const Duration(milliseconds: 400));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: widget.size / 4),
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                color: widget.color.withValues(alpha: _animations[index].value),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }
}

/// 脉冲加载指示器
class _PulseLoadingIndicator extends StatefulWidget {
  const _PulseLoadingIndicator({
    required this.color,
    required this.size,
  });

  final Color color;
  final double size;

  @override
  State<_PulseLoadingIndicator> createState() => _PulseLoadingIndicatorState();
}

class _PulseLoadingIndicatorState extends State<_PulseLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            color: widget.color.withValues(alpha: _animation.value),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }
}

/// 全屏加载遮罩
class LoadingOverlay extends StatelessWidget {
  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingWidget,
    this.backgroundColor,
    this.message,
  });

  /// 是否显示加载
  final bool isLoading;

  /// 子组件
  final Widget child;

  /// 自定义加载组件
  final Widget? loadingWidget;

  /// 背景颜色
  final Color? backgroundColor;

  /// 加载消息
  final String? message;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: backgroundColor ?? Colors.black.withValues(alpha: 0.5),
            child: Center(
              child: loadingWidget ??
                  LoadingIndicator(
                    message: message,
                    size: LoadingIndicatorSize.large,
                    color: Colors.white,
                  ),
            ),
          ),
      ],
    );
  }
}

/// 内联加载器
class InlineLoader extends StatelessWidget {
  const InlineLoader({
    super.key,
    this.size = LoadingIndicatorSize.small,
    this.color,
    this.padding,
  });

  final LoadingIndicatorSize size;
  final Color? color;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(8.0),
      child: LoadingIndicator(
        size: size,
        color: color,
        type: LoadingIndicatorType.circular,
      ),
    );
  }
}

/// 按钮加载状态
class LoadingButton extends StatelessWidget {
  const LoadingButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.isLoading = false,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      child: isLoading
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                child,
              ],
            )
          : child,
    );
  }
}
