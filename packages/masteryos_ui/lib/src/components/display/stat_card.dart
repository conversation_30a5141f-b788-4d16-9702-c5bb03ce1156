import 'package:flutter/material.dart';
import '../../theme/theme_constants.dart';

/// 统计卡片组件
class StatCard extends StatelessWidget {
  const StatCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.color,
    this.backgroundColor,
    this.changeValue,
    this.changePercentage,
    this.isPositiveChange,
    this.onTap,
    this.padding,
    this.margin,
    this.borderRadius,
    this.elevation = 2,
    this.size = StatCardSize.medium,
  });

  /// 标题
  final String title;
  
  /// 主要数值
  final String value;
  
  /// 副标题
  final String? subtitle;
  
  /// 图标
  final IconData? icon;
  
  /// 主色调
  final Color? color;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 变化数值
  final String? changeValue;
  
  /// 变化百分比
  final double? changePercentage;
  
  /// 是否为正向变化
  final bool? isPositiveChange;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 内边距
  final EdgeInsets? padding;
  
  /// 外边距
  final EdgeInsets? margin;
  
  /// 边框圆角
  final BorderRadius? borderRadius;
  
  /// 阴影高度
  final double elevation;
  
  /// 卡片尺寸
  final StatCardSize size;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final cardColor = backgroundColor ?? colorScheme.surface;
    final primaryColor = color ?? colorScheme.primary;
    
    Widget card = Card(
      color: cardColor,
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(ThemeConstants.radiusLG),
      ),
      margin: margin,
      child: Padding(
        padding: padding ?? _getDefaultPadding(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题行
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: _getTitleStyle(context),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (icon != null) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
                    ),
                    child: Icon(
                      icon,
                      color: primaryColor,
                      size: _getIconSize(),
                    ),
                  ),
                ],
              ],
            ),
            
            SizedBox(height: _getSpacing()),
            
            // 主要数值
            Text(
              value,
              style: _getValueStyle(context),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            if (subtitle != null) ...[
              SizedBox(height: _getSpacing() / 2),
              Text(
                subtitle!,
                style: _getSubtitleStyle(context),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            
            // 变化指标
            if (changeValue != null || changePercentage != null) ...[
              SizedBox(height: _getSpacing()),
              _buildChangeIndicator(context),
            ],
          ],
        ),
      ),
    );
    
    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(ThemeConstants.radiusLG),
        child: card,
      );
    }
    
    return card;
  }
  
  Widget _buildChangeIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final isPositive = isPositiveChange ?? true;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeIcon = isPositive ? Icons.trending_up : Icons.trending_down;
    
    String changeText = '';
    if (changeValue != null && changePercentage != null) {
      changeText = '$changeValue (${changePercentage!.abs().toStringAsFixed(1)}%)';
    } else if (changeValue != null) {
      changeText = changeValue!;
    } else if (changePercentage != null) {
      changeText = '${changePercentage!.abs().toStringAsFixed(1)}%';
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          changeIcon,
          color: changeColor,
          size: _getChangeIconSize(),
        ),
        const SizedBox(width: 4),
        Text(
          changeText,
          style: theme.textTheme.bodySmall?.copyWith(
            color: changeColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
  
  EdgeInsets _getDefaultPadding() {
    switch (size) {
      case StatCardSize.small:
        return const EdgeInsets.all(ThemeConstants.spaceMD);
      case StatCardSize.medium:
        return const EdgeInsets.all(ThemeConstants.spaceLG);
      case StatCardSize.large:
        return const EdgeInsets.all(ThemeConstants.spaceXL);
    }
  }
  
  double _getSpacing() {
    switch (size) {
      case StatCardSize.small:
        return ThemeConstants.spaceXS;
      case StatCardSize.medium:
        return ThemeConstants.spaceSM;
      case StatCardSize.large:
        return ThemeConstants.spaceMD;
    }
  }
  
  double _getIconSize() {
    switch (size) {
      case StatCardSize.small:
        return 20;
      case StatCardSize.medium:
        return 24;
      case StatCardSize.large:
        return 28;
    }
  }
  
  double _getChangeIconSize() {
    switch (size) {
      case StatCardSize.small:
        return 14;
      case StatCardSize.medium:
        return 16;
      case StatCardSize.large:
        return 18;
    }
  }
  
  TextStyle _getTitleStyle(BuildContext context) {
    final baseStyle = Theme.of(context).textTheme.bodyMedium!;
    
    switch (size) {
      case StatCardSize.small:
        return baseStyle.copyWith(
          fontSize: ThemeConstants.fontSizeXS,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
        );
      case StatCardSize.medium:
        return baseStyle.copyWith(
          fontSize: ThemeConstants.fontSizeSM,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
        );
      case StatCardSize.large:
        return baseStyle.copyWith(
          fontSize: ThemeConstants.fontSizeMD,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
        );
    }
  }
  
  TextStyle _getValueStyle(BuildContext context) {
    final baseStyle = Theme.of(context).textTheme.headlineMedium!;
    
    switch (size) {
      case StatCardSize.small:
        return baseStyle.copyWith(
          fontSize: ThemeConstants.fontSizeLG,
          fontWeight: FontWeight.bold,
          color: color ?? Theme.of(context).colorScheme.primary,
        );
      case StatCardSize.medium:
        return baseStyle.copyWith(
          fontSize: ThemeConstants.fontSize2XL,
          fontWeight: FontWeight.bold,
          color: color ?? Theme.of(context).colorScheme.primary,
        );
      case StatCardSize.large:
        return baseStyle.copyWith(
          fontSize: ThemeConstants.fontSize3XL,
          fontWeight: FontWeight.bold,
          color: color ?? Theme.of(context).colorScheme.primary,
        );
    }
  }
  
  TextStyle _getSubtitleStyle(BuildContext context) {
    final baseStyle = Theme.of(context).textTheme.bodySmall!;
    
    return baseStyle.copyWith(
      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
    );
  }
}

/// 统计卡片尺寸枚举
enum StatCardSize {
  small,
  medium,
  large,
}

/// 统计卡片网格组件
class StatCardGrid extends StatelessWidget {
  const StatCardGrid({
    super.key,
    required this.cards,
    this.crossAxisCount,
    this.childAspectRatio = 1.5,
    this.crossAxisSpacing = 16.0,
    this.mainAxisSpacing = 16.0,
    this.padding,
    this.shrinkWrap = true,
    this.physics = const NeverScrollableScrollPhysics(),
  });

  /// 卡片列表
  final List<Widget> cards;
  
  /// 列数（null时自动响应式）
  final int? crossAxisCount;
  
  /// 宽高比
  final double childAspectRatio;
  
  /// 横向间距
  final double crossAxisSpacing;
  
  /// 纵向间距
  final double mainAxisSpacing;
  
  /// 内边距
  final EdgeInsets? padding;
  
  /// 是否收缩包装
  final bool shrinkWrap;
  
  /// 滚动物理
  final ScrollPhysics? physics;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final responsiveColumns = crossAxisCount ?? _getResponsiveColumns(screenWidth);
    
    return GridView.builder(
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: responsiveColumns,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) => cards[index],
    );
  }
  
  int _getResponsiveColumns(double width) {
    if (width < ThemeConstants.breakpointSM) {
      return 1; // 移动端：1列
    } else if (width < ThemeConstants.breakpointLG) {
      return 2; // 平板端：2列
    } else {
      return 4; // 桌面端：4列
    }
  }
}

/// 简化的统计卡片构建器
class SimpleStatCard extends StatelessWidget {
  const SimpleStatCard({
    super.key,
    required this.title,
    required this.value,
    this.icon,
    this.color,
    this.onTap,
    this.size = StatCardSize.medium,
  });

  final String title;
  final String value;
  final IconData? icon;
  final Color? color;
  final VoidCallback? onTap;
  final StatCardSize size;

  @override
  Widget build(BuildContext context) {
    return StatCard(
      title: title,
      value: value,
      icon: icon,
      color: color,
      onTap: onTap,
      size: size,
    );
  }
}

/// 带趋势的统计卡片
class TrendingStatCard extends StatelessWidget {
  const TrendingStatCard({
    super.key,
    required this.title,
    required this.value,
    required this.changePercentage,
    this.icon,
    this.color,
    this.onTap,
    this.size = StatCardSize.medium,
    this.period = '较上期',
  });

  final String title;
  final String value;
  final double changePercentage;
  final IconData? icon;
  final Color? color;
  final VoidCallback? onTap;
  final StatCardSize size;
  final String period;

  @override
  Widget build(BuildContext context) {
    final isPositive = changePercentage >= 0;
    
    return StatCard(
      title: title,
      value: value,
      subtitle: period,
      icon: icon,
      color: color,
      changePercentage: changePercentage,
      isPositiveChange: isPositive,
      onTap: onTap,
      size: size,
    );
  }
}