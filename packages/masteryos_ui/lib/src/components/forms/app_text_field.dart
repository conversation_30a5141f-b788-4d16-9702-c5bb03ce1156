import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/theme_constants.dart';

/// 应用通用文本输入框组件
class AppTextField extends StatefulWidget {
  const AppTextField({
    super.key,
    this.controller,
    this.initialValue,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.style,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.textDirection,
    this.readOnly = false,
    this.enabled = true,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.maxLengthEnforcement,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.onTap,
    this.inputFormatters,
    this.validator,
    this.autovalidateMode,
    this.obscureText = false,
    this.obscuringCharacter = '•',
    this.autocorrect = true,
    this.enableSuggestions = true,
    this.showCursor,
    this.cursorWidth = 2.0,
    this.cursorHeight,
    this.cursorColor,
    this.cursorRadius,
    this.showClearButton = false,
    this.prefixIcon,
    this.suffixIcon,
    this.label,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.isRequired = false,
    this.borderRadius,
    this.fillColor,
    this.size = AppTextFieldSize.medium,
  });

  /// 文本控制器
  final TextEditingController? controller;
  
  /// 初始值
  final String? initialValue;
  
  /// 焦点节点
  final FocusNode? focusNode;
  
  /// 输入装饰
  final InputDecoration? decoration;
  
  /// 键盘类型
  final TextInputType? keyboardType;
  
  /// 文本输入动作
  final TextInputAction? textInputAction;
  
  /// 文本大小写
  final TextCapitalization textCapitalization;
  
  /// 文本样式
  final TextStyle? style;
  
  /// 文本对齐
  final TextAlign textAlign;
  
  /// 文本垂直对齐
  final TextAlignVertical? textAlignVertical;
  
  /// 文本方向
  final TextDirection? textDirection;
  
  /// 是否只读
  final bool readOnly;
  
  /// 是否启用
  final bool enabled;
  
  /// 最大行数
  final int? maxLines;
  
  /// 最小行数
  final int? minLines;
  
  /// 最大长度
  final int? maxLength;
  
  /// 最大长度强制执行
  final MaxLengthEnforcement? maxLengthEnforcement;
  
  /// 内容变化回调
  final ValueChanged<String>? onChanged;
  
  /// 编辑完成回调
  final VoidCallback? onEditingComplete;
  
  /// 提交回调
  final ValueChanged<String>? onSubmitted;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 输入格式化器
  final List<TextInputFormatter>? inputFormatters;
  
  /// 验证器
  final FormFieldValidator<String>? validator;
  
  /// 自动验证模式
  final AutovalidateMode? autovalidateMode;
  
  /// 是否隐藏文本
  final bool obscureText;
  
  /// 隐藏字符
  final String obscuringCharacter;
  
  /// 是否自动纠正
  final bool autocorrect;
  
  /// 是否启用建议
  final bool enableSuggestions;
  
  /// 是否显示光标
  final bool? showCursor;
  
  /// 光标宽度
  final double cursorWidth;
  
  /// 光标高度
  final double? cursorHeight;
  
  /// 光标颜色
  final Color? cursorColor;
  
  /// 光标圆角
  final Radius? cursorRadius;
  
  /// 是否显示清除按钮
  final bool showClearButton;
  
  /// 前缀图标
  final Widget? prefixIcon;
  
  /// 后缀图标
  final Widget? suffixIcon;
  
  /// 标签组件
  final Widget? label;
  
  /// 标签文本
  final String? labelText;
  
  /// 提示文本
  final String? hintText;
  
  /// 帮助文本
  final String? helperText;
  
  /// 错误文本
  final String? errorText;
  
  /// 是否必填
  final bool isRequired;
  
  /// 边框圆角
  final BorderRadius? borderRadius;
  
  /// 填充颜色
  final Color? fillColor;
  
  /// 输入框尺寸
  final AppTextFieldSize size;

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();
    
    _hasText = _controller.text.isNotEmpty;
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
    widget.onChanged?.call(_controller.text);
  }

  void _clearText() {
    _controller.clear();
    widget.onChanged?.call('');
    _focusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final decoration = _buildInputDecoration(context, colorScheme);
    
    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      decoration: decoration,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      textCapitalization: widget.textCapitalization,
      style: widget.style ?? _getTextStyle(context),
      textAlign: widget.textAlign,
      textAlignVertical: widget.textAlignVertical,
      textDirection: widget.textDirection,
      readOnly: widget.readOnly,
      enabled: widget.enabled,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      maxLengthEnforcement: widget.maxLengthEnforcement,
      onEditingComplete: widget.onEditingComplete,
      onFieldSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      inputFormatters: widget.inputFormatters,
      validator: widget.validator,
      autovalidateMode: widget.autovalidateMode,
      obscureText: widget.obscureText,
      obscuringCharacter: widget.obscuringCharacter,
      autocorrect: widget.autocorrect,
      enableSuggestions: widget.enableSuggestions,
      showCursor: widget.showCursor,
      cursorWidth: widget.cursorWidth,
      cursorHeight: widget.cursorHeight,
      cursorColor: widget.cursorColor ?? colorScheme.primary,
      cursorRadius: widget.cursorRadius,
    );
  }
  
  InputDecoration _buildInputDecoration(BuildContext context, ColorScheme colorScheme) {
    final baseDecoration = widget.decoration ?? const InputDecoration();
    final borderRadius = widget.borderRadius ?? BorderRadius.circular(ThemeConstants.radiusMD);
    
    // 构建后缀图标
    Widget? suffixIcon;
    if (widget.showClearButton && _hasText && widget.enabled && !widget.readOnly) {
      suffixIcon = IconButton(
        icon: const Icon(Icons.clear),
        onPressed: _clearText,
        iconSize: _getIconSize(),
        padding: EdgeInsets.zero,
        visualDensity: VisualDensity.compact,
      );
    } else if (widget.suffixIcon != null) {
      suffixIcon = widget.suffixIcon;
    }
    
    // 构建标签文本
    String? labelText = widget.labelText;
    if (widget.isRequired && labelText != null) {
      labelText = '$labelText *';
    }
    
    return baseDecoration.copyWith(
      labelText: labelText,
      hintText: widget.hintText,
      helperText: widget.helperText,
      errorText: widget.errorText,
      prefixIcon: widget.prefixIcon,
      suffixIcon: suffixIcon,
      label: widget.label,
      filled: true,
      fillColor: widget.fillColor ?? colorScheme.surface,
      contentPadding: _getPadding(),
      border: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.outline.withValues(alpha: 0.5)),
      ),
    );
  }
  
  EdgeInsets _getPadding() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceMD,
          vertical: ThemeConstants.spaceXS,
        );
      case AppTextFieldSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceLG,
          vertical: ThemeConstants.spaceMD,
        );
      case AppTextFieldSize.large:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceXL,
          vertical: ThemeConstants.spaceLG,
        );
    }
  }
  
  TextStyle _getTextStyle(BuildContext context) {
    final baseStyle = Theme.of(context).textTheme.bodyMedium!;
    
    switch (widget.size) {
      case AppTextFieldSize.small:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeXS);
      case AppTextFieldSize.medium:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeSM);
      case AppTextFieldSize.large:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeMD);
    }
  }
  
  double _getIconSize() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return 16;
      case AppTextFieldSize.medium:
        return 20;
      case AppTextFieldSize.large:
        return 24;
    }
  }
}

/// 输入框尺寸枚举
enum AppTextFieldSize {
  small,
  medium,
  large,
}

/// 邮箱输入框
class EmailTextField extends StatelessWidget {
  const EmailTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.labelText = '邮箱',
    this.hintText = '请输入邮箱地址',
    this.isRequired = false,
    this.size = AppTextFieldSize.medium,
  });

  final TextEditingController? controller;
  final FocusNode? focusNode;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final FormFieldValidator<String>? validator;
  final String labelText;
  final String hintText;
  final bool isRequired;
  final AppTextFieldSize size;

  @override
  Widget build(BuildContext context) {
    return AppTextField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      validator: validator ?? _defaultEmailValidator,
      labelText: labelText,
      hintText: hintText,
      isRequired: isRequired,
      size: size,
      showClearButton: true,
      prefixIcon: const Icon(Icons.email_outlined),
    );
  }

  String? _defaultEmailValidator(String? value) {
    if (isRequired && (value == null || value.isEmpty)) {
      return '邮箱不能为空';
    }
    if (value != null && value.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(value)) {
        return '请输入有效的邮箱地址';
      }
    }
    return null;
  }
}

/// 密码输入框
class PasswordTextField extends StatefulWidget {
  const PasswordTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.labelText = '密码',
    this.hintText = '请输入密码',
    this.isRequired = false,
    this.minLength = 6,
    this.size = AppTextFieldSize.medium,
  });

  final TextEditingController? controller;
  final FocusNode? focusNode;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final FormFieldValidator<String>? validator;
  final String labelText;
  final String hintText;
  final bool isRequired;
  final int minLength;
  final AppTextFieldSize size;

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  bool _obscureText = true;

  void _toggleObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppTextField(
      controller: widget.controller,
      focusNode: widget.focusNode,
      obscureText: _obscureText,
      keyboardType: TextInputType.visiblePassword,
      textInputAction: TextInputAction.done,
      onChanged: widget.onChanged,
      onSubmitted: widget.onSubmitted,
      validator: widget.validator ?? _defaultPasswordValidator,
      labelText: widget.labelText,
      hintText: widget.hintText,
      isRequired: widget.isRequired,
      size: widget.size,
      prefixIcon: const Icon(Icons.lock_outline),
      suffixIcon: IconButton(
        icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off),
        onPressed: _toggleObscureText,
      ),
    );
  }

  String? _defaultPasswordValidator(String? value) {
    if (widget.isRequired && (value == null || value.isEmpty)) {
      return '密码不能为空';
    }
    if (value != null && value.isNotEmpty && value.length < widget.minLength) {
      return '密码长度不能少于${widget.minLength}位';
    }
    return null;
  }
}

/// 搜索输入框
class SearchTextField extends StatelessWidget {
  const SearchTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.onChanged,
    this.onSubmitted,
    this.hintText = '搜索...',
    this.size = AppTextFieldSize.medium,
  });

  final TextEditingController? controller;
  final FocusNode? focusNode;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final String hintText;
  final AppTextFieldSize size;

  @override
  Widget build(BuildContext context) {
    return AppTextField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      hintText: hintText,
      size: size,
      showClearButton: true,
      prefixIcon: const Icon(Icons.search),
    );
  }
}