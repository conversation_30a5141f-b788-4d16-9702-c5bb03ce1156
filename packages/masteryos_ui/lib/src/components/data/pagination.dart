import 'package:flutter/material.dart';
import '../../theme/theme_constants.dart';

/// 分页组件
class Pagination extends StatelessWidget {
  const Pagination({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
    this.totalItems,
    this.itemsPerPage,
    this.showPageInfo = true,
    this.showPageNumbers = true,
    this.showFirstLastButtons = true,
    this.maxVisiblePages = 5,
    this.buttonSize = PaginationButtonSize.medium,
    this.spacing = 8.0,
  });

  /// 当前页码（从1开始）
  final int currentPage;
  
  /// 总页数
  final int totalPages;
  
  /// 页码变化回调
  final ValueChanged<int> onPageChanged;
  
  /// 总项目数
  final int? totalItems;
  
  /// 每页项目数
  final int? itemsPerPage;
  
  /// 是否显示页面信息
  final bool showPageInfo;
  
  /// 是否显示页码按钮
  final bool showPageNumbers;
  
  /// 是否显示首末页按钮
  final bool showFirstLastButtons;
  
  /// 最大可见页码数
  final int maxVisiblePages;
  
  /// 按钮尺寸
  final PaginationButtonSize buttonSize;
  
  /// 按钮间距
  final double spacing;

  @override
  Widget build(BuildContext context) {
    if (totalPages <= 1) {
      return showPageInfo ? _buildPageInfo(context) : const SizedBox.shrink();
    }
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showPageInfo) ...[
          _buildPageInfo(context),
          SizedBox(height: spacing),
        ],
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: _buildPaginationButtons(context),
        ),
      ],
    );
  }
  
  Widget _buildPageInfo(BuildContext context) {
    String info = '第 $currentPage 页，共 $totalPages 页';
    
    if (totalItems != null && itemsPerPage != null) {
      final startItem = (currentPage - 1) * itemsPerPage! + 1;
      final endItem = (currentPage * itemsPerPage!).clamp(1, totalItems!);
      info = '第 $startItem - $endItem 项，共 $totalItems 项 ($info)';
    }
    
    return Text(
      info,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }
  
  List<Widget> _buildPaginationButtons(BuildContext context) {
    final buttons = <Widget>[];
    
    // 首页按钮
    if (showFirstLastButtons && currentPage > 1) {
      buttons.addAll([
        _PaginationButton(
          icon: Icons.first_page,
          onPressed: () => onPageChanged(1),
          size: buttonSize,
          tooltip: '首页',
        ),
        SizedBox(width: spacing),
      ]);
    }
    
    // 上一页按钮
    if (currentPage > 1) {
      buttons.addAll([
        _PaginationButton(
          icon: Icons.chevron_left,
          onPressed: () => onPageChanged(currentPage - 1),
          size: buttonSize,
          tooltip: '上一页',
        ),
        SizedBox(width: spacing),
      ]);
    }
    
    // 页码按钮
    if (showPageNumbers) {
      final pageButtons = _buildPageNumberButtons(context);
      for (int i = 0; i < pageButtons.length; i++) {
        buttons.add(pageButtons[i]);
        if (i < pageButtons.length - 1) {
          buttons.add(SizedBox(width: spacing));
        }
      }
      buttons.add(SizedBox(width: spacing));
    }
    
    // 下一页按钮
    if (currentPage < totalPages) {
      buttons.addAll([
        _PaginationButton(
          icon: Icons.chevron_right,
          onPressed: () => onPageChanged(currentPage + 1),
          size: buttonSize,
          tooltip: '下一页',
        ),
        SizedBox(width: spacing),
      ]);
    }
    
    // 末页按钮
    if (showFirstLastButtons && currentPage < totalPages) {
      buttons.add(_PaginationButton(
        icon: Icons.last_page,
        onPressed: () => onPageChanged(totalPages),
        size: buttonSize,
        tooltip: '末页',
      ));
    }
    
    return buttons;
  }
  
  List<Widget> _buildPageNumberButtons(BuildContext context) {
    final buttons = <Widget>[];
    final visiblePages = _calculateVisiblePages();
    
    for (final page in visiblePages) {
      if (page == -1) {
        // 省略号
        buttons.add(_PaginationEllipsis(size: buttonSize));
      } else {
        buttons.add(_PaginationButton(
          text: page.toString(),
          onPressed: () => onPageChanged(page),
          isSelected: page == currentPage,
          size: buttonSize,
        ));
      }
    }
    
    return buttons;
  }
  
  List<int> _calculateVisiblePages() {
    if (totalPages <= maxVisiblePages) {
      return List.generate(totalPages, (index) => index + 1);
    }
    
    final pages = <int>[];
    final halfVisible = maxVisiblePages ~/ 2;
    
    // 计算起始和结束页码
    int startPage = (currentPage - halfVisible).clamp(1, totalPages);
    int endPage = (currentPage + halfVisible).clamp(1, totalPages);
    
    // 调整范围以保持固定的可见页数
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage == 1) {
        endPage = maxVisiblePages.clamp(1, totalPages);
      } else if (endPage == totalPages) {
        startPage = (totalPages - maxVisiblePages + 1).clamp(1, totalPages);
      }
    }
    
    // 添加第一页和省略号
    if (startPage > 1) {
      pages.add(1);
      if (startPage > 2) {
        pages.add(-1); // 省略号
      }
    }
    
    // 添加中间页码
    for (int i = startPage; i <= endPage; i++) {
      if (!pages.contains(i)) {
        pages.add(i);
      }
    }
    
    // 添加省略号和最后一页
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.add(-1); // 省略号
      }
      if (!pages.contains(totalPages)) {
        pages.add(totalPages);
      }
    }
    
    return pages;
  }
}

/// 分页按钮尺寸枚举
enum PaginationButtonSize {
  small,
  medium,
  large,
}

/// 分页按钮组件
class _PaginationButton extends StatelessWidget {
  const _PaginationButton({
    this.text,
    this.icon,
    this.onPressed,
    this.isSelected = false,
    required this.size,
    this.tooltip,
  });

  final String? text;
  final IconData? icon;
  final VoidCallback? onPressed;
  final bool isSelected;
  final PaginationButtonSize size;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final buttonSize = _getButtonSize();
    final buttonStyle = _getButtonStyle(context, colorScheme);
    
    Widget button;
    
    if (text != null) {
      button = SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: TextButton(
          onPressed: onPressed,
          style: buttonStyle,
          child: Text(
            text!,
            style: TextStyle(
              fontSize: _getFontSize(),
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      );
    } else if (icon != null) {
      button = SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(icon, size: _getIconSize()),
          style: buttonStyle,
        ),
      );
    } else {
      button = const SizedBox.shrink();
    }
    
    if (tooltip != null) {
      button = Tooltip(
        message: tooltip!,
        child: button,
      );
    }
    
    return button;
  }
  
  double _getButtonSize() {
    switch (size) {
      case PaginationButtonSize.small:
        return 32;
      case PaginationButtonSize.medium:
        return 40;
      case PaginationButtonSize.large:
        return 48;
    }
  }
  
  double _getIconSize() {
    switch (size) {
      case PaginationButtonSize.small:
        return 16;
      case PaginationButtonSize.medium:
        return 20;
      case PaginationButtonSize.large:
        return 24;
    }
  }
  
  double _getFontSize() {
    switch (size) {
      case PaginationButtonSize.small:
        return ThemeConstants.fontSizeXS;
      case PaginationButtonSize.medium:
        return ThemeConstants.fontSizeSM;
      case PaginationButtonSize.large:
        return ThemeConstants.fontSizeMD;
    }
  }
  
  ButtonStyle _getButtonStyle(BuildContext context, ColorScheme colorScheme) {
    return ButtonStyle(
      backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (isSelected) {
          return colorScheme.primary;
        }
        if (states.contains(WidgetState.hovered)) {
          return colorScheme.primary.withValues(alpha: 0.1);
        }
        return Colors.transparent;
      }),
      foregroundColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (isSelected) {
          return colorScheme.onPrimary;
        }
        if (states.contains(WidgetState.disabled)) {
          return colorScheme.onSurface.withValues(alpha: 0.38);
        }
        return colorScheme.onSurface;
      }),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeConstants.radiusMD),
        ),
      ),
      padding: WidgetStateProperty.all(EdgeInsets.zero),
      minimumSize: WidgetStateProperty.all(Size.zero),
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}

/// 分页省略号组件
class _PaginationEllipsis extends StatelessWidget {
  const _PaginationEllipsis({
    required this.size,
  });

  final PaginationButtonSize size;

  @override
  Widget build(BuildContext context) {
    final buttonSize = _getButtonSize();
    
    return SizedBox(
      width: buttonSize,
      height: buttonSize,
      child: Center(
        child: Text(
          '...',
          style: TextStyle(
            fontSize: _getFontSize(),
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ),
    );
  }
  
  double _getButtonSize() {
    switch (size) {
      case PaginationButtonSize.small:
        return 32;
      case PaginationButtonSize.medium:
        return 40;
      case PaginationButtonSize.large:
        return 48;
    }
  }
  
  double _getFontSize() {
    switch (size) {
      case PaginationButtonSize.small:
        return ThemeConstants.fontSizeXS;
      case PaginationButtonSize.medium:
        return ThemeConstants.fontSizeSM;
      case PaginationButtonSize.large:
        return ThemeConstants.fontSizeMD;
    }
  }
}

/// 简单分页组件（仅上一页/下一页）
class SimplePagination extends StatelessWidget {
  const SimplePagination({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
    this.buttonSize = PaginationButtonSize.medium,
    this.spacing = 16.0,
    this.showPageInfo = true,
  });

  final int currentPage;
  final int totalPages;
  final ValueChanged<int> onPageChanged;
  final PaginationButtonSize buttonSize;
  final double spacing;
  final bool showPageInfo;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 上一页按钮
        _PaginationButton(
          icon: Icons.chevron_left,
          text: '上一页',
          onPressed: currentPage > 1 ? () => onPageChanged(currentPage - 1) : null,
          size: buttonSize,
        ),
        
        // 页面信息
        if (showPageInfo)
          Text(
            '$currentPage / $totalPages',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        
        // 下一页按钮
        _PaginationButton(
          icon: Icons.chevron_right,
          text: '下一页',
          onPressed: currentPage < totalPages ? () => onPageChanged(currentPage + 1) : null,
          size: buttonSize,
        ),
      ],
    );
  }
}