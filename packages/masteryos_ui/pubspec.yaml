name: masteryos_ui
description: MasteryOS UI组件库 - 包含通用UI组件、主题系统和自适应布局
version: 1.0.0
publish_to: none

environment:
  sdk: '>=3.2.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # 核心共享包
  masteryos_core:
    path: ../masteryos_core
  
  # 状态管理
  flutter_bloc: ^9.1.1
  
  # 路由管理
  go_router: ^16.1.0
  
  # 图标
  cupertino_icons: ^1.0.8
  
  # 动画
  lottie: ^3.1.3
  
  # 图片加载和缓存
  cached_network_image: ^3.4.1
  
  # 文件选择
  file_picker: ^10.2.1
  
  # 图片选择
  image_picker: ^1.1.2
  
  # URL启动
  url_launcher: ^6.3.1
  
  # 分享功能
  share_plus: ^11.0.0
  
  # 权限管理
  permission_handler: ^12.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # 代码分析
  flutter_lints: ^6.0.0
  
  # 测试工具
  mockito: ^5.4.4
  
  # Golden测试
  golden_toolkit: ^0.15.0

flutter: