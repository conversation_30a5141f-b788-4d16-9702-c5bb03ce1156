name: masteryos_core
description: MasteryOS核心共享包 - 包含数据模型、API服务、工具类和配置管理
version: 1.0.0
publish_to: none

environment:
  sdk: '>=3.8.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # HTTP请求
  dio: ^5.7.0
  
  # JSON序列化
  json_annotation: ^4.9.0
  
  # 本地存储
  shared_preferences: ^2.3.3
  
  # 状态管理
  flutter_bloc: ^9.1.1
  
  # 路由管理
  go_router: ^16.1.0
  
  # 国际化
  intl: ^0.20.1
  
  # 日志
  logger: ^2.4.0
  
  # 加密
  crypto: ^3.0.3
  
  # 设备信息
  device_info_plus: ^11.5.0
  
  # 包信息
  package_info_plus: ^8.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # 代码生成
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  
  # 代码分析
  flutter_lints: ^6.0.0
  
  # 测试工具
  mockito: ^5.4.4

flutter: