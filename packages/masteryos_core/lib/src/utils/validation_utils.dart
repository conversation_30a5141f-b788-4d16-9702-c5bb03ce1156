/// 验证工具类
class ValidationUtils {
  ValidationUtils._();

  /// 验证邮箱格式
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// 验证手机号格式（中国大陆）
  static bool isValidPhoneNumber(String phone) {
    return RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
  }

  /// 验证身份证号码（中国大陆）
  static bool isValidIdCard(String idCard) {
    if (idCard.length != 18) return false;
    
    // 检查前17位是否为数字
    final prefix = idCard.substring(0, 17);
    if (!RegExp(r'^\d{17}$').hasMatch(prefix)) return false;
    
    // 检查最后一位校验码
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    int sum = 0;
    for (int i = 0; i < 17; i++) {
      sum += int.parse(prefix[i]) * weights[i];
    }
    
    final expectedCheckCode = checkCodes[sum % 11];
    return idCard[17].toUpperCase() == expectedCheckCode;
  }

  /// 验证密码强度
  static PasswordStrength getPasswordStrength(String password) {
    if (password.length < 6) return PasswordStrength.weak;
    
    int score = 0;
    
    // 长度得分
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // 包含小写字母
    if (RegExp(r'[a-z]').hasMatch(password)) score += 1;
    
    // 包含大写字母  
    if (RegExp(r'[A-Z]').hasMatch(password)) score += 1;
    
    // 包含数字
    if (RegExp(r'\d').hasMatch(password)) score += 1;
    
    // 包含特殊字符
    if (RegExp(r'[!@#$%^&*(),.?\":{}|<>]').hasMatch(password)) score += 1;
    
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }

  /// 验证URL格式
  static bool isValidUrl(String url) {
    return RegExp(r'^https?://[^\s/$.?#].[^\s]*$', caseSensitive: false)
        .hasMatch(url);
  }

  /// 验证IP地址格式
  static bool isValidIpAddress(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    
    return true;
  }

  /// 验证银行卡号格式（简单验证）
  static bool isValidBankCard(String cardNumber) {
    // 移除空格和连字符
    final cleaned = cardNumber.replaceAll(RegExp(r'[\s-]'), '');
    
    // 检查长度和数字
    if (cleaned.length < 16 || cleaned.length > 19) return false;
    if (!RegExp(r'^\d+$').hasMatch(cleaned)) return false;
    
    // Luhn算法验证
    return _luhnCheck(cleaned);
  }

  /// Luhn算法校验
  static bool _luhnCheck(String cardNumber) {
    int sum = 0;
    bool isEven = false;
    
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit = digit ~/ 10 + digit % 10;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 == 0;
  }

  /// 验证用户名格式
  static bool isValidUsername(String username) {
    // 4-20位，字母、数字、下划线，以字母开头
    return RegExp(r'^[a-zA-Z][a-zA-Z0-9_]{3,19}$').hasMatch(username);
  }

  /// 验证QQ号格式
  static bool isValidQQ(String qq) {
    return RegExp(r'^[1-9]\d{4,10}$').hasMatch(qq);
  }

  /// 验证微信号格式
  static bool isValidWechat(String wechat) {
    // 6-20位，字母、数字、下划线、减号
    return RegExp(r'^[a-zA-Z][a-zA-Z0-9_-]{5,19}$').hasMatch(wechat);
  }

  /// 验证中文姓名
  static bool isValidChineseName(String name) {
    return RegExp(r'^[\u4e00-\u9fa5]{2,8}$').hasMatch(name);
  }

  /// 验证英文姓名
  static bool isValidEnglishName(String name) {
    return RegExp(r'^[a-zA-Z\s]{2,50}$').hasMatch(name);
  }

  /// 验证车牌号（中国大陆）
  static bool isValidLicensePlate(String plate) {
    // 普通车牌
    final normalPattern = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$');
    // 新能源车牌
    final newEnergyPattern = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z][A-Z0-9DF][0-9]{4}[A-Z0-9]$');
    
    return normalPattern.hasMatch(plate) || newEnergyPattern.hasMatch(plate);
  }

  /// 验证邮政编码
  static bool isValidPostalCode(String code) {
    return RegExp(r'^\d{6}$').hasMatch(code);
  }

  /// 验证数字范围
  static bool isNumberInRange(num value, num min, num max) {
    return value >= min && value <= max;
  }

  /// 验证字符串长度范围
  static bool isLengthInRange(String str, int min, int max) {
    return str.length >= min && str.length <= max;
  }

  /// 验证是否为纯数字
  static bool isNumeric(String str) {
    return RegExp(r'^\d+$').hasMatch(str);
  }

  /// 验证是否为字母
  static bool isAlpha(String str) {
    return RegExp(r'^[a-zA-Z]+$').hasMatch(str);
  }

  /// 验证是否为字母数字组合
  static bool isAlphanumeric(String str) {
    return RegExp(r'^[a-zA-Z0-9]+$').hasMatch(str);
  }
}

/// 密码强度枚举
enum PasswordStrength {
  weak,
  medium,
  strong,
}

/// 密码强度扩展
extension PasswordStrengthExtension on PasswordStrength {
  String get description {
    switch (this) {
      case PasswordStrength.weak:
        return '弱';
      case PasswordStrength.medium:
        return '中';
      case PasswordStrength.strong:
        return '强';
    }
  }

  double get score {
    switch (this) {
      case PasswordStrength.weak:
        return 0.3;
      case PasswordStrength.medium:
        return 0.6;
      case PasswordStrength.strong:
        return 1.0;
    }
  }
}