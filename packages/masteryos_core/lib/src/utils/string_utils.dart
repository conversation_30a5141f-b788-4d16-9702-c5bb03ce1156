/// 字符串工具类
class StringUtils {
  StringUtils._();

  /// 检查字符串是否为空或null
  static bool isEmpty(String? str) {
    return str == null || str.isEmpty;
  }

  /// 检查字符串是否不为空
  static bool isNotEmpty(String? str) {
    return str != null && str.isNotEmpty;
  }

  /// 检查字符串是否为空白（包含空格、换行等）
  static bool isBlank(String? str) {
    return str == null || str.trim().isEmpty;
  }

  /// 检查字符串是否不为空白
  static bool isNotBlank(String? str) {
    return str != null && str.trim().isNotEmpty;
  }

  /// 首字母大写
  static String capitalize(String str) {
    if (str.isEmpty) return str;
    return str[0].toUpperCase() + str.substring(1).toLowerCase();
  }

  /// 每个单词首字母大写
  static String titleCase(String str) {
    return str.split(' ').map((word) => capitalize(word)).join(' ');
  }

  /// 驼峰命名转下划线
  static String camelToSnake(String str) {
    return str.replaceAllMapped(
      RegExp(r'[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    );
  }

  /// 下划线转驼峰命名
  static String snakeToCamel(String str) {
    return str.replaceAllMapped(
      RegExp(r'_([a-z])'),
      (match) => match.group(1)!.toUpperCase(),
    );
  }

  /// 截断字符串
  static String truncate(String str, int maxLength, {String suffix = '...'}) {
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength - suffix.length) + suffix;
  }

  /// 移除HTML标签
  static String removeHtmlTags(String str) {
    return str.replaceAll(RegExp(r'<[^>]*>'), '');
  }

  /// 生成随机字符串
  static String generateRandomString(int length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (index) => chars.codeUnitAt((random + index) % chars.length),
      ),
    );
  }

  /// 计算字符串的字节长度
  static int getByteLength(String str) {
    return str.runes.length;
  }

  /// 反转字符串
  static String reverse(String str) {
    return str.split('').reversed.join('');
  }

  /// 检查是否为回文
  static bool isPalindrome(String str) {
    final cleaned = str.toLowerCase().replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
    return cleaned == reverse(cleaned);
  }

  /// 统计字符出现次数
  static Map<String, int> countCharacters(String str) {
    final counts = <String, int>{};
    for (final char in str.split('')) {
      counts[char] = (counts[char] ?? 0) + 1;
    }
    return counts;
  }

  /// 模糊匹配
  static bool fuzzyMatch(String str, String pattern) {
    final regex = RegExp(pattern.split('').join('.*?'), caseSensitive: false);
    return regex.hasMatch(str);
  }

  /// 计算两个字符串的相似度（简单版本）
  static double similarity(String str1, String str2) {
    if (str1 == str2) return 1.0;
    if (str1.isEmpty || str2.isEmpty) return 0.0;
    
    final maxLength = [str1.length, str2.length].reduce((a, b) => a > b ? a : b);
    final distance = levenshteinDistance(str1, str2);
    return (maxLength - distance) / maxLength;
  }

  /// 计算Levenshtein距离
  static int levenshteinDistance(String str1, String str2) {
    final matrix = List.generate(
      str1.length + 1,
      (i) => List.generate(str2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= str1.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= str2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= str1.length; i++) {
      for (int j = 1; j <= str2.length; j++) {
        final cost = str1[i - 1] == str2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[str1.length][str2.length];
  }

  /// 提取数字
  static List<int> extractNumbers(String str) {
    return RegExp(r'\d+').allMatches(str)
        .map((match) => int.tryParse(match.group(0)!) ?? 0)
        .toList();
  }

  /// 提取邮箱地址
  static List<String> extractEmails(String str) {
    return RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        .allMatches(str)
        .map((match) => match.group(0)!)
        .toList();
  }

  /// 提取URL
  static List<String> extractUrls(String str) {
    return RegExp(r'https?://[^\s]+')
        .allMatches(str)
        .map((match) => match.group(0)!)
        .toList();
  }
}