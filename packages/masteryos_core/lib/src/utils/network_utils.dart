import 'dart:io';
import 'package:dio/dio.dart';

/// 网络工具类
class NetworkUtils {
  NetworkUtils._();

  /// 检查网络连接状态
  static Future<bool> isConnected() async {
    try {
      final result = await InternetAddress.lookup('www.google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// 检查特定主机的连接状态
  static Future<bool> canReachHost(String host, {int port = 80}) async {
    try {
      final socket = await Socket.connect(host, port, timeout: const Duration(seconds: 5));
      socket.destroy();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取本机IP地址
  static Future<String?> getLocalIpAddress() async {
    try {
      final interfaces = await NetworkInterface.list();
      for (final interface in interfaces) {
        for (final address in interface.addresses) {
          if (address.type == InternetAddressType.IPv4 && !address.isLoopback) {
            return address.address;
          }
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取所有网络接口信息
  static Future<List<Map<String, dynamic>>> getNetworkInterfaces() async {
    try {
      final interfaces = await NetworkInterface.list();
      return interfaces.map((interface) => {
        'name': interface.name,
        'index': interface.index,
        'addresses': interface.addresses.map((addr) => {
          'address': addr.address,
          'type': addr.type.name,
          'isLoopback': addr.isLoopback,
          'isLinkLocal': addr.isLinkLocal,
          'isMulticast': addr.isMulticast,
        }).toList(),
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Ping指定主机
  static Future<PingResult> ping(String host, {int count = 4, Duration timeout = const Duration(seconds: 5)}) async {
    final results = <Duration>[];
    int successCount = 0;
    
    for (int i = 0; i < count; i++) {
      final stopwatch = Stopwatch()..start();
      try {
        final socket = await Socket.connect(host, 80, timeout: timeout);
        socket.destroy();
        stopwatch.stop();
        results.add(stopwatch.elapsed);
        successCount++;
      } catch (e) {
        stopwatch.stop();
        // 连接失败，不记录时间
      }
    }
    
    return PingResult(
      host: host,
      totalPings: count,
      successfulPings: successCount,
      failedPings: count - successCount,
      times: results,
    );
  }

  /// 测试网络速度（下载）
  static Future<NetworkSpeedResult> testDownloadSpeed({
    String testUrl = 'https://httpbin.org/bytes/1048576', // 1MB测试文件
    Duration timeout = const Duration(seconds: 30),
  }) async {
    try {
      final dio = Dio();
      final stopwatch = Stopwatch()..start();
      
      final response = await dio.get(
        testUrl,
        options: Options(
          receiveTimeout: timeout,
          sendTimeout: timeout,
        ),
      );
      
      stopwatch.stop();
      
      final bytes = response.data.toString().length;
      final seconds = stopwatch.elapsed.inMilliseconds / 1000.0;
      final speedBps = bytes / seconds;
      
      return NetworkSpeedResult(
        bytes: bytes,
        duration: stopwatch.elapsed,
        speedBps: speedBps,
        speedKbps: speedBps / 1024,
        speedMbps: speedBps / (1024 * 1024),
      );
    } catch (e) {
      return NetworkSpeedResult(
        bytes: 0,
        duration: Duration.zero,
        speedBps: 0,
        speedKbps: 0,
        speedMbps: 0,
        error: e.toString(),
      );
    }
  }

  /// 解析URL
  static Map<String, dynamic> parseUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return {
        'scheme': uri.scheme,
        'host': uri.host,
        'port': uri.port,
        'path': uri.path,
        'query': uri.query,
        'fragment': uri.fragment,
        'queryParameters': uri.queryParameters,
      };
    } catch (e) {
      return {};
    }
  }

  /// 构建URL
  static String buildUrl({
    required String scheme,
    required String host,
    int? port,
    String? path,
    Map<String, String>? queryParameters,
    String? fragment,
  }) {
    final uri = Uri(
      scheme: scheme,
      host: host,
      port: port,
      path: path,
      queryParameters: queryParameters,
      fragment: fragment,
    );
    return uri.toString();
  }

  /// 检查URL是否有效
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// 获取域名
  static String? getDomain(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return null;
    }
  }

  /// 检查端口是否开放
  static Future<bool> isPortOpen(String host, int port, {Duration timeout = const Duration(seconds: 5)}) async {
    try {
      final socket = await Socket.connect(host, port, timeout: timeout);
      socket.destroy();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取HTTP状态码描述
  static String getHttpStatusDescription(int statusCode) {
    switch (statusCode) {
      case 200: return 'OK';
      case 201: return 'Created';
      case 204: return 'No Content';
      case 301: return 'Moved Permanently';
      case 302: return 'Found';
      case 304: return 'Not Modified';
      case 400: return 'Bad Request';
      case 401: return 'Unauthorized';
      case 403: return 'Forbidden';
      case 404: return 'Not Found';
      case 405: return 'Method Not Allowed';
      case 408: return 'Request Timeout';
      case 409: return 'Conflict';
      case 410: return 'Gone';
      case 422: return 'Unprocessable Entity';
      case 429: return 'Too Many Requests';
      case 500: return 'Internal Server Error';
      case 501: return 'Not Implemented';
      case 502: return 'Bad Gateway';
      case 503: return 'Service Unavailable';
      case 504: return 'Gateway Timeout';
      default: return 'Unknown Status Code';
    }
  }

  /// 判断是否为成功状态码
  static bool isSuccessStatusCode(int statusCode) {
    return statusCode >= 200 && statusCode < 300;
  }

  /// 判断是否为客户端错误状态码
  static bool isClientErrorStatusCode(int statusCode) {
    return statusCode >= 400 && statusCode < 500;
  }

  /// 判断是否为服务器错误状态码
  static bool isServerErrorStatusCode(int statusCode) {
    return statusCode >= 500 && statusCode < 600;
  }
}

/// Ping结果
class PingResult {
  final String host;
  final int totalPings;
  final int successfulPings;
  final int failedPings;
  final List<Duration> times;

  PingResult({
    required this.host,
    required this.totalPings,
    required this.successfulPings,
    required this.failedPings,
    required this.times,
  });

  /// 成功率
  double get successRate => successfulPings / totalPings;

  /// 平均响应时间
  Duration? get averageTime {
    if (times.isEmpty) return null;
    final totalMs = times.fold(0, (sum, time) => sum + time.inMilliseconds);
    return Duration(milliseconds: totalMs ~/ times.length);
  }

  /// 最小响应时间
  Duration? get minTime {
    if (times.isEmpty) return null;
    return times.reduce((a, b) => a.inMilliseconds < b.inMilliseconds ? a : b);
  }

  /// 最大响应时间
  Duration? get maxTime {
    if (times.isEmpty) return null;
    return times.reduce((a, b) => a.inMilliseconds > b.inMilliseconds ? a : b);
  }

  @override
  String toString() {
    return 'PingResult(host: $host, success: $successfulPings/$totalPings, avg: ${averageTime?.inMilliseconds}ms)';
  }
}

/// 网络速度测试结果
class NetworkSpeedResult {
  final int bytes;
  final Duration duration;
  final double speedBps;
  final double speedKbps;
  final double speedMbps;
  final String? error;

  NetworkSpeedResult({
    required this.bytes,
    required this.duration,
    required this.speedBps,
    required this.speedKbps,
    required this.speedMbps,
    this.error,
  });

  bool get hasError => error != null;
  bool get isSuccess => error == null && bytes > 0;

  @override
  String toString() {
    if (hasError) return 'NetworkSpeedResult(error: $error)';
    return 'NetworkSpeedResult(${speedMbps.toStringAsFixed(2)} Mbps, ${bytes} bytes in ${duration.inMilliseconds}ms)';
  }
}