import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// 加密工具类
class CryptoUtils {
  CryptoUtils._();

  /// MD5哈希
  static String md5Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// SHA1哈希
  static String sha1Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = sha1.convert(bytes);
    return digest.toString();
  }

  /// SHA256哈希
  static String sha256Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// SHA512哈希
  static String sha512Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = sha512.convert(bytes);
    return digest.toString();
  }

  /// HMAC-SHA256
  static String hmacSha256(String message, String key) {
    final keyBytes = utf8.encode(key);
    final messageBytes = utf8.encode(message);
    final hmac = Hmac(sha256, keyBytes);
    final digest = hmac.convert(messageBytes);
    return digest.toString();
  }

  /// HMAC-SHA512
  static String hmacSha512(String message, String key) {
    final keyBytes = utf8.encode(key);
    final messageBytes = utf8.encode(message);
    final hmac = Hmac(sha512, keyBytes);
    final digest = hmac.convert(messageBytes);
    return digest.toString();
  }

  /// Base64编码
  static String base64Encode(String input) {
    final bytes = utf8.encode(input);
    return base64.encode(bytes);
  }

  /// Base64解码
  static String base64Decode(String encoded) {
    try {
      final bytes = base64.decode(encoded);
      return utf8.decode(bytes);
    } catch (e) {
      throw FormatException('Invalid base64 string: $encoded');
    }
  }

  /// Base64 URL安全编码
  static String base64UrlEncode(String input) {
    final bytes = utf8.encode(input);
    return base64Url.encode(bytes);
  }

  /// Base64 URL安全解码
  static String base64UrlDecode(String encoded) {
    try {
      final bytes = base64Url.decode(encoded);
      return utf8.decode(bytes);
    } catch (e) {
      throw FormatException('Invalid base64url string: $encoded');
    }
  }

  /// 生成随机字符串
  static String generateRandomString(int length, {bool includeSpecialChars = false}) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const specialChars = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    
    final availableChars = includeSpecialChars ? chars + specialChars : chars;
    final random = Random.secure();
    
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => availableChars.codeUnitAt(random.nextInt(availableChars.length)),
      ),
    );
  }

  /// 生成随机数字字符串
  static String generateRandomDigits(int length) {
    const digits = '0123456789';
    final random = Random.secure();
    
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => digits.codeUnitAt(random.nextInt(digits.length)),
      ),
    );
  }

  /// 生成UUID v4
  static String generateUuidV4() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (_) => random.nextInt(256));
    
    // Set version (4) and variant bits
    bytes[6] = (bytes[6] & 0x0F) | 0x40; // Version 4
    bytes[8] = (bytes[8] & 0x3F) | 0x80; // Variant bits
    
    final hex = bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    
    return '${hex.substring(0, 8)}-${hex.substring(8, 12)}-${hex.substring(12, 16)}-${hex.substring(16, 20)}-${hex.substring(20, 32)}';
  }

  /// 简单异或加密/解密
  static String xorCrypt(String data, String key) {
    if (key.isEmpty) throw ArgumentError('Key cannot be empty');
    
    final dataBytes = utf8.encode(data);
    final keyBytes = utf8.encode(key);
    final result = <int>[];
    
    for (int i = 0; i < dataBytes.length; i++) {
      result.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return base64.encode(result);
  }

  /// 异或解密
  static String xorDecrypt(String encryptedData, String key) {
    if (key.isEmpty) throw ArgumentError('Key cannot be empty');
    
    try {
      final encryptedBytes = base64.decode(encryptedData);
      final keyBytes = utf8.encode(key);
      final result = <int>[];
      
      for (int i = 0; i < encryptedBytes.length; i++) {
        result.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(result);
    } catch (e) {
      throw FormatException('Failed to decrypt data: $e');
    }
  }

  /// 凯撒密码加密
  static String caesarCipher(String text, int shift) {
    final result = StringBuffer();
    
    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      
      if (char.codeUnitAt(0) >= 65 && char.codeUnitAt(0) <= 90) {
        // 大写字母
        final shifted = ((char.codeUnitAt(0) - 65 + shift) % 26) + 65;
        result.writeCharCode(shifted);
      } else if (char.codeUnitAt(0) >= 97 && char.codeUnitAt(0) <= 122) {
        // 小写字母
        final shifted = ((char.codeUnitAt(0) - 97 + shift) % 26) + 97;
        result.writeCharCode(shifted);
      } else {
        // 其他字符保持不变
        result.write(char);
      }
    }
    
    return result.toString();
  }

  /// 凯撒密码解密
  static String caesarDecipher(String text, int shift) {
    return caesarCipher(text, -shift);
  }

  /// ROT13编码/解码
  static String rot13(String text) {
    return caesarCipher(text, 13);
  }

  /// 计算字符串的哈希值（用于快速比较）
  static int fastHash(String input) {
    int hash = 0;
    for (int i = 0; i < input.length; i++) {
      hash = ((hash << 5) - hash + input.codeUnitAt(i)) & 0xFFFFFFFF;
    }
    return hash;
  }

  /// 生成密码哈希（模拟bcrypt风格）
  static String hashPassword(String password, {String? salt}) {
    salt ??= generateRandomString(16);
    final combined = password + salt;
    final hash = sha256Hash(combined);
    return '\$sha256\$$salt\$$hash';
  }

  /// 验证密码
  static bool verifyPassword(String password, String hashedPassword) {
    try {
      final parts = hashedPassword.split('\$');
      if (parts.length != 4 || parts[0].isNotEmpty || parts[1] != 'sha256') {
        return false;
      }
      
      final salt = parts[2];
      final hash = parts[3];
      
      final computedHash = sha256Hash(password + salt);
      return computedHash == hash;
    } catch (e) {
      return false;
    }
  }

  /// 生成密钥对（简单的RSA风格，仅用于演示）
  static KeyPair generateKeyPair() {
    // 这里只是一个简化的示例，真实的RSA需要更复杂的数学运算
    final random = Random.secure();
    final privateKey = generateRandomString(64);
    final publicKey = sha256Hash(privateKey).substring(0, 32);
    
    return KeyPair(
      publicKey: publicKey,
      privateKey: privateKey,
    );
  }

  /// 数字签名（简化版）
  static String sign(String message, String privateKey) {
    return hmacSha256(message, privateKey);
  }

  /// 验证签名（简化版）
  static bool verifySignature(String message, String signature, String publicKey) {
    // 这里是简化的验证逻辑，真实场景会更复杂
    final expectedSignature = hmacSha256(message, publicKey);
    return expectedSignature == signature;
  }

  /// 生成随机盐值
  static String generateSalt({int length = 32}) {
    return generateRandomString(length);
  }

  /// 时间戳哈希（防重放攻击）
  static String timestampHash(String data, {Duration window = const Duration(minutes: 5)}) {
    final timestamp = (DateTime.now().millisecondsSinceEpoch ~/ window.inMilliseconds) * window.inMilliseconds;
    return sha256Hash('$data:$timestamp');
  }

  /// 验证时间戳哈希
  static bool verifyTimestampHash(String data, String hash, {Duration window = const Duration(minutes: 5)}) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final windowMs = window.inMilliseconds;
    
    // 检查当前时间窗口和前一个时间窗口
    for (int i = 0; i <= 1; i++) {
      final timestamp = ((now ~/ windowMs) - i) * windowMs;
      final expectedHash = sha256Hash('$data:$timestamp');
      if (expectedHash == hash) {
        return true;
      }
    }
    
    return false;
  }
}

/// 密钥对
class KeyPair {
  final String publicKey;
  final String privateKey;

  KeyPair({
    required this.publicKey,
    required this.privateKey,
  });

  @override
  String toString() {
    return 'KeyPair(publicKey: ${publicKey.substring(0, 8)}..., privateKey: [HIDDEN])';
  }
}