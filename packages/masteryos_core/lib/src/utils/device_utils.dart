import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// 设备信息工具类
class DeviceUtils {
  DeviceUtils._();

  static DeviceInfoPlugin? _deviceInfo;
  static PackageInfo? _packageInfo;

  /// 初始化设备信息
  static Future<void> initialize() async {
    _deviceInfo = DeviceInfoPlugin();
    _packageInfo = await PackageInfo.fromPlatform();
  }

  /// 获取设备信息
  static DeviceInfoPlugin get deviceInfo {
    _deviceInfo ??= DeviceInfoPlugin();
    return _deviceInfo!;
  }

  /// 获取应用包信息
  static Future<PackageInfo> getPackageInfo() async {
    _packageInfo ??= await PackageInfo.fromPlatform();
    return _packageInfo!;
  }

  /// 获取平台名称
  static String getPlatformName() {
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    if (Platform.isFuchsia) return 'Fuchsia';
    return 'Unknown';
  }

  /// 检查是否为移动平台
  static bool isMobile() {
    return Platform.isAndroid || Platform.isIOS;
  }

  /// 检查是否为桌面平台
  static bool isDesktop() {
    return Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  }

  /// 检查是否为Web平台
  static bool isWeb() {
    // 在Flutter Web中，kIsWeb为true
    // 但在这个包中，我们假设不是Web环境
    return false;
  }

  /// 获取Android设备信息
  static Future<AndroidDeviceInfo?> getAndroidInfo() async {
    if (!Platform.isAndroid) return null;
    return await deviceInfo.androidInfo;
  }

  /// 获取iOS设备信息
  static Future<IosDeviceInfo?> getIosInfo() async {
    if (!Platform.isIOS) return null;
    return await deviceInfo.iosInfo;
  }

  /// 获取Windows设备信息
  static Future<WindowsDeviceInfo?> getWindowsInfo() async {
    if (!Platform.isWindows) return null;
    return await deviceInfo.windowsInfo;
  }

  /// 获取macOS设备信息
  static Future<MacOsDeviceInfo?> getMacOsInfo() async {
    if (!Platform.isMacOS) return null;
    return await deviceInfo.macOsInfo;
  }

  /// 获取Linux设备信息
  static Future<LinuxDeviceInfo?> getLinuxInfo() async {
    if (!Platform.isLinux) return null;
    return await deviceInfo.linuxInfo;
  }

  /// 获取设备型号
  static Future<String> getDeviceModel() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await getAndroidInfo();
        return '${androidInfo?.manufacturer} ${androidInfo?.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await getIosInfo();
        return iosInfo?.model ?? 'iPhone';
      } else if (Platform.isWindows) {
        final windowsInfo = await getWindowsInfo();
        return windowsInfo?.computerName ?? 'Windows PC';
      } else if (Platform.isMacOS) {
        final macInfo = await getMacOsInfo();
        return macInfo?.model ?? 'Mac';
      } else if (Platform.isLinux) {
        final linuxInfo = await getLinuxInfo();
        return linuxInfo?.name ?? 'Linux';
      }
      return 'Unknown Device';
    } catch (e) {
      return 'Unknown Device';
    }
  }

  /// 获取操作系统版本
  static Future<String> getOsVersion() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await getAndroidInfo();
        return 'Android ${androidInfo?.version.release}';
      } else if (Platform.isIOS) {
        final iosInfo = await getIosInfo();
        return 'iOS ${iosInfo?.systemVersion}';
      } else if (Platform.isWindows) {
        return 'Windows ${Platform.operatingSystemVersion}';
      } else if (Platform.isMacOS) {
        final macInfo = await getMacOsInfo();
        return 'macOS ${macInfo?.osRelease}';
      } else if (Platform.isLinux) {
        final linuxInfo = await getLinuxInfo();
        return 'Linux ${linuxInfo?.version}';
      }
      return Platform.operatingSystemVersion;
    } catch (e) {
      return Platform.operatingSystemVersion;
    }
  }

  /// 获取设备唯一标识符
  static Future<String> getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await getAndroidInfo();
        return androidInfo?.id ?? 'unknown';
      } else if (Platform.isIOS) {
        final iosInfo = await getIosInfo();
        return iosInfo?.identifierForVendor ?? 'unknown';
      } else if (Platform.isWindows) {
        final windowsInfo = await getWindowsInfo();
        return windowsInfo?.deviceId ?? 'unknown';
      } else if (Platform.isMacOS) {
        final macInfo = await getMacOsInfo();
        return macInfo?.systemGUID ?? 'unknown';
      } else if (Platform.isLinux) {
        final linuxInfo = await getLinuxInfo();
        return linuxInfo?.machineId ?? 'unknown';
      }
      return 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /// 获取应用版本
  static Future<String> getAppVersion() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.version;
  }

  /// 获取应用构建号
  static Future<String> getBuildNumber() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.buildNumber;
  }

  /// 获取应用包名
  static Future<String> getPackageName() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.packageName;
  }

  /// 获取应用名称
  static Future<String> getAppName() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.appName;
  }

  /// 检查是否为物理设备
  static Future<bool> isPhysicalDevice() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await getAndroidInfo();
        return androidInfo?.isPhysicalDevice ?? true;
      } else if (Platform.isIOS) {
        final iosInfo = await getIosInfo();
        return iosInfo?.isPhysicalDevice ?? true;
      }
      return true;
    } catch (e) {
      return true;
    }
  }

  /// 获取设备内存信息（仅Android）
  static Future<Map<String, dynamic>?> getMemoryInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await getAndroidInfo();
        return {
          'totalMemory': androidInfo?.systemFeatures,
          // 注意：Android设备信息插件不直接提供内存信息
          // 这里只是示例结构
        };
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取设备屏幕信息（需要结合Flutter的MediaQuery使用）
  static Map<String, dynamic> getScreenInfo({
    required double width,
    required double height,
    required double pixelRatio,
  }) {
    return {
      'width': width,
      'height': height,
      'pixelRatio': pixelRatio,
      'physicalWidth': width * pixelRatio,
      'physicalHeight': height * pixelRatio,
      'aspectRatio': width / height,
    };
  }

  /// 获取完整的设备信息摘要
  static Future<Map<String, dynamic>> getDeviceSummary() async {
    return {
      'platform': getPlatformName(),
      'isMobile': isMobile(),
      'isDesktop': isDesktop(),
      'isWeb': isWeb(),
      'deviceModel': await getDeviceModel(),
      'osVersion': await getOsVersion(),
      'deviceId': await getDeviceId(),
      'appVersion': await getAppVersion(),
      'buildNumber': await getBuildNumber(),
      'packageName': await getPackageName(),
      'appName': await getAppName(),
      'isPhysicalDevice': await isPhysicalDevice(),
    };
  }
}