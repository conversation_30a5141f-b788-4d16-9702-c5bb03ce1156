import 'package:json_annotation/json_annotation.dart';

part 'auth_models.g.dart';

/// 用户认证信息
@JsonSerializable()
class AuthUser {
  final String id;
  final String username;
  final String email;
  final String? name;
  final String? avatar;
  final List<String> roles;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  const AuthUser({
    required this.id,
    required this.username,
    required this.email,
    this.name,
    this.avatar,
    required this.roles,
    this.metadata,
    required this.createdAt,
    this.lastLoginAt,
  });

  factory AuthUser.fromJson(Map<String, dynamic> json) =>
      _$AuthUserFromJson(json);

  Map<String, dynamic> toJson() => _$AuthUserToJson(this);

  bool hasRole(String role) => roles.contains(role);
  bool hasAnyRole(List<String> targetRoles) =>
      roles.any((role) => targetRoles.contains(role));
}

/// JWT令牌信息
@JsonSerializable()
class JwtTokens {
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final String tokenType;

  const JwtTokens({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    this.tokenType = 'Bearer',
  });

  factory JwtTokens.fromJson(Map<String, dynamic> json) =>
      _$JwtTokensFromJson(json);

  Map<String, dynamic> toJson() => _$JwtTokensToJson(this);

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get willExpireSoon =>
      DateTime.now().add(const Duration(minutes: 5)).isAfter(expiresAt);
}

/// 登录凭据
@JsonSerializable()
class LoginCredentials {
  final String username;
  final String password;
  final bool rememberMe;

  const LoginCredentials({
    required this.username,
    required this.password,
    this.rememberMe = false,
  });

  factory LoginCredentials.fromJson(Map<String, dynamic> json) =>
      _$LoginCredentialsFromJson(json);

  Map<String, dynamic> toJson() => _$LoginCredentialsToJson(this);
}

/// 认证状态
enum AuthStatus {
  unknown,
  authenticated,
  unauthenticated,
  loading,
}

/// 认证状态数据
@JsonSerializable()
class AuthState {
  final AuthStatus status;
  final AuthUser? user;
  final JwtTokens? tokens;
  final String? error;

  const AuthState({
    required this.status,
    this.user,
    this.tokens,
    this.error,
  });

  factory AuthState.fromJson(Map<String, dynamic> json) =>
      _$AuthStateFromJson(json);

  Map<String, dynamic> toJson() => _$AuthStateToJson(this);

  factory AuthState.unknown() => const AuthState(status: AuthStatus.unknown);

  factory AuthState.loading() => const AuthState(status: AuthStatus.loading);

  factory AuthState.authenticated({
    required AuthUser user,
    required JwtTokens tokens,
  }) =>
      AuthState(
        status: AuthStatus.authenticated,
        user: user,
        tokens: tokens,
      );

  factory AuthState.unauthenticated({String? error}) => AuthState(
        status: AuthStatus.unauthenticated,
        error: error,
      );

  bool get isAuthenticated => status == AuthStatus.authenticated;
  bool get isLoading => status == AuthStatus.loading;
  bool get hasError => error != null;

  AuthState copyWith({
    AuthStatus? status,
    AuthUser? user,
    JwtTokens? tokens,
    String? error,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      tokens: tokens ?? this.tokens,
      error: error ?? this.error,
    );
  }
}