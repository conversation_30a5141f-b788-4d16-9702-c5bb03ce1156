import 'dart:async';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import 'auth_models.dart';
import 'auth_storage.dart';
import 'token_manager.dart';

/// 认证服务
class AuthService {
  final Dio _dio;
  final AuthStorage _storage;
  final TokenManager _tokenManager;
  final Logger _logger;

  final StreamController<AuthState> _stateController =
      StreamController<AuthState>.broadcast();

  AuthState _currentState = AuthState.unknown();

  AuthService({
    required Dio dio,
    required AuthStorage storage,
    required TokenManager tokenManager,
    Logger? logger,
  })  : _dio = dio,
        _storage = storage,
        _tokenManager = tokenManager,
        _logger = logger ?? Logger();

  /// 当前认证状态流
  Stream<AuthState> get authState => _stateController.stream;

  /// 当前认证状态
  AuthState get currentState => _currentState;

  /// 当前用户
  AuthUser? get currentUser => _currentState.user;

  /// 是否已认证
  bool get isAuthenticated => _currentState.isAuthenticated;

  /// 初始化认证服务
  Future<void> initialize() async {
    try {
      _updateState(AuthState.loading());

      // 尝试从存储中恢复认证状态
      final tokens = await _storage.getTokens();
      if (tokens != null && !tokens.isExpired) {
        // 验证令牌并获取用户信息
        final user = await _validateTokenAndGetUser(tokens.accessToken);
        if (user != null) {
          _updateState(AuthState.authenticated(user: user, tokens: tokens));
          _tokenManager.setTokens(tokens);
          return;
        }
      }

      _updateState(AuthState.unauthenticated());
    } catch (e) {
      _logger.e('认证服务初始化失败', error: e);
      _updateState(AuthState.unauthenticated(error: e.toString()));
    }
  }

  /// 用户登录
  Future<AuthState> login(LoginCredentials credentials) async {
    try {
      _updateState(AuthState.loading());

      final response = await _dio.post('/auth/login', data: {
        'username': credentials.username,
        'password': credentials.password,
        'rememberMe': credentials.rememberMe,
      });

      final data = response.data;
      final tokens = JwtTokens.fromJson(data['tokens']);
      final user = AuthUser.fromJson(data['user']);

      // 保存令牌
      await _storage.saveTokens(tokens);
      if (credentials.rememberMe) {
        await _storage.saveUser(user);
      }

      _tokenManager.setTokens(tokens);

      final newState = AuthState.authenticated(user: user, tokens: tokens);
      _updateState(newState);

      _logger.i('用户登录成功: ${user.username}');
      return newState;
    } on DioException catch (e) {
      final error = _handleDioError(e);
      final newState = AuthState.unauthenticated(error: error);
      _updateState(newState);
      return newState;
    } catch (e) {
      _logger.e('登录失败', error: e);
      final newState = AuthState.unauthenticated(error: e.toString());
      _updateState(newState);
      return newState;
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      if (_currentState.tokens != null) {
        await _dio.post('/auth/logout', data: {
          'refreshToken': _currentState.tokens!.refreshToken,
        });
      }
    } catch (e) {
      _logger.w('服务端登出失败', error: e);
    } finally {
      await _storage.clearAll();
      _tokenManager.clearTokens();
      _updateState(AuthState.unauthenticated());
      _logger.i('用户已登出');
    }
  }

  /// 刷新令牌
  Future<bool> refreshToken() async {
    try {
      final currentTokens = _currentState.tokens;
      if (currentTokens == null) {
        throw Exception('无可用的刷新令牌');
      }

      final response = await _dio.post('/auth/refresh', data: {
        'refreshToken': currentTokens.refreshToken,
      });

      final newTokens = JwtTokens.fromJson(response.data['tokens']);
      await _storage.saveTokens(newTokens);
      _tokenManager.setTokens(newTokens);

      _updateState(_currentState.copyWith(tokens: newTokens));
      _logger.i('令牌刷新成功');
      return true;
    } catch (e) {
      _logger.e('令牌刷新失败', error: e);
      await logout();
      return false;
    }
  }

  /// 验证当前令牌
  Future<bool> validateCurrentToken() async {
    try {
      final tokens = _currentState.tokens;
      if (tokens == null || tokens.isExpired) {
        return false;
      }

      final user = await _validateTokenAndGetUser(tokens.accessToken);
      return user != null;
    } catch (e) {
      _logger.e('令牌验证失败', error: e);
      return false;
    }
  }

  /// 获取当前用户信息
  Future<AuthUser?> getCurrentUser() async {
    try {
      if (!isAuthenticated) return null;

      final response = await _dio.get('/auth/me');
      final user = AuthUser.fromJson(response.data);

      _updateState(_currentState.copyWith(user: user));
      return user;
    } catch (e) {
      _logger.e('获取用户信息失败', error: e);
      return null;
    }
  }

  /// 更新用户信息
  Future<AuthUser?> updateProfile(Map<String, dynamic> updates) async {
    try {
      final response = await _dio.patch('/auth/profile', data: updates);
      final user = AuthUser.fromJson(response.data);

      _updateState(_currentState.copyWith(user: user));
      await _storage.saveUser(user);

      _logger.i('用户信息更新成功');
      return user;
    } catch (e) {
      _logger.e('用户信息更新失败', error: e);
      return null;
    }
  }

  /// 修改密码
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      await _dio.post('/auth/change-password', data: {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      });

      _logger.i('密码修改成功');
      return true;
    } on DioException catch (e) {
      _logger.e('密码修改失败', error: e);
      return false;
    }
  }

  /// 验证令牌并获取用户信息
  Future<AuthUser?> _validateTokenAndGetUser(String accessToken) async {
    try {
      final response = await _dio.get(
        '/auth/validate',
        options: Options(headers: {'Authorization': 'Bearer $accessToken'}),
      );
      return AuthUser.fromJson(response.data);
    } catch (e) {
      return null;
    }
  }

  /// 处理网络错误
  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return '网络连接超时，请检查网络连接';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? '服务器错误';
        if (statusCode == 401) {
          return '用户名或密码错误';
        } else if (statusCode == 403) {
          return '无权限访问';
        }
        return message;
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接失败，请检查网络连接';
      default:
        return '登录失败，请重试';
    }
  }

  /// 更新认证状态
  void _updateState(AuthState newState) {
    _currentState = newState;
    if (!_stateController.isClosed) {
      _stateController.add(newState);
    }
  }

  /// 释放资源
  void dispose() {
    _stateController.close();
  }
}