import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

import 'auth_models.dart';

/// 认证存储服务
class AuthStorage {
  static const String _keyTokens = 'auth_tokens';
  static const String _keyUser = 'auth_user';
  static const String _keyRememberMe = 'auth_remember_me';

  final Logger _logger;

  AuthStorage({Logger? logger}) : _logger = logger ?? Logger();

  /// 保存JWT令牌
  Future<void> saveTokens(JwtTokens tokens) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tokensJson = jsonEncode(tokens.toJson());
      await prefs.setString(_keyTokens, tokensJson);
      _logger.d('JWT令牌已保存');
    } catch (e) {
      _logger.e('保存JWT令牌失败', error: e);
      rethrow;
    }
  }

  /// 获取JWT令牌
  Future<JwtTokens?> getTokens() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tokensJson = prefs.getString(_keyTokens);
      if (tokensJson == null) {
        return null;
      }

      final tokensMap = jsonDecode(tokensJson) as Map<String, dynamic>;
      return JwtTokens.fromJson(tokensMap);
    } catch (e) {
      _logger.e('获取JWT令牌失败', error: e);
      await clearTokens();
      return null;
    }
  }

  /// 清除JWT令牌
  Future<void> clearTokens() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyTokens);
      _logger.d('JWT令牌已清除');
    } catch (e) {
      _logger.e('清除JWT令牌失败', error: e);
    }
  }

  /// 保存用户信息
  Future<void> saveUser(AuthUser user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = jsonEncode(user.toJson());
      await prefs.setString(_keyUser, userJson);
      _logger.d('用户信息已保存');
    } catch (e) {
      _logger.e('保存用户信息失败', error: e);
      rethrow;
    }
  }

  /// 获取用户信息
  Future<AuthUser?> getUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_keyUser);
      if (userJson == null) {
        return null;
      }

      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return AuthUser.fromJson(userMap);
    } catch (e) {
      _logger.e('获取用户信息失败', error: e);
      await clearUser();
      return null;
    }
  }

  /// 清除用户信息
  Future<void> clearUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyUser);
      _logger.d('用户信息已清除');
    } catch (e) {
      _logger.e('清除用户信息失败', error: e);
    }
  }

  /// 设置记住我状态
  Future<void> setRememberMe(bool rememberMe) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyRememberMe, rememberMe);
      _logger.d('记住我状态已设置: $rememberMe');
    } catch (e) {
      _logger.e('设置记住我状态失败', error: e);
    }
  }

  /// 获取记住我状态
  Future<bool> getRememberMe() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyRememberMe) ?? false;
    } catch (e) {
      _logger.e('获取记住我状态失败', error: e);
      return false;
    }
  }

  /// 清除所有认证数据
  Future<void> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_keyTokens),
        prefs.remove(_keyUser),
        prefs.remove(_keyRememberMe),
      ]);
      _logger.d('所有认证数据已清除');
    } catch (e) {
      _logger.e('清除认证数据失败', error: e);
    }
  }

  /// 检查是否有保存的认证数据
  Future<bool> hasAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_keyTokens) && prefs.containsKey(_keyUser);
    } catch (e) {
      _logger.e('检查认证数据失败', error: e);
      return false;
    }
  }

  /// 获取存储大小信息（调试用）
  Future<Map<String, int>> getStorageInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final info = <String, int>{};

      final tokensJson = prefs.getString(_keyTokens);
      info['tokens_size'] = tokensJson?.length ?? 0;

      final userJson = prefs.getString(_keyUser);
      info['user_size'] = userJson?.length ?? 0;

      info['total_size'] = info['tokens_size']! + info['user_size']!;

      return info;
    } catch (e) {
      _logger.e('获取存储信息失败', error: e);
      return {};
    }
  }
}