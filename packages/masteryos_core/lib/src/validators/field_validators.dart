/// 字段特定验证器
class FieldValidators {
  FieldValidators._();

  /// 邮政编码验证
  static String? postalCode(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (!RegExp(r'^\d{6}$').hasMatch(value)) {
      return message ?? '请输入有效的邮政编码';
    }
    return null;
  }

  /// QQ号验证
  static String? qq(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (!RegExp(r'^[1-9]\d{4,10}$').hasMatch(value)) {
      return message ?? '请输入有效的QQ号';
    }
    return null;
  }

  /// 微信号验证
  static String? wechat(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    // 6-20位，字母、数字、下划线、减号
    if (!RegExp(r'^[a-zA-Z][a-zA-Z0-9_-]{5,19}$').hasMatch(value)) {
      return message ?? '请输入有效的微信号';
    }
    return null;
  }

  /// 车牌号验证（中国大陆）
  static String? licensePlate(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    // 普通车牌
    final normalPattern = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$');
    // 新能源车牌
    final newEnergyPattern = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z][A-Z0-9DF][0-9]{4}[A-Z0-9]$');
    
    if (!normalPattern.hasMatch(value) && !newEnergyPattern.hasMatch(value)) {
      return message ?? '请输入有效的车牌号';
    }
    return null;
  }

  /// IP地址验证
  static String? ipAddress(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final parts = value.split('.');
    if (parts.length != 4) {
      return message ?? '请输入有效的IP地址';
    }
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) {
        return message ?? '请输入有效的IP地址';
      }
    }
    
    return null;
  }

  /// MAC地址验证
  static String? macAddress(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final macRegex = RegExp(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$');
    if (!macRegex.hasMatch(value)) {
      return message ?? '请输入有效的MAC地址';
    }
    return null;
  }

  /// 端口号验证
  static String? port(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final portNum = int.tryParse(value);
    if (portNum == null || portNum < 1 || portNum > 65535) {
      return message ?? '端口号必须在1-65535之间';
    }
    return null;
  }

  /// 版本号验证（语义化版本）
  static String? version(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final versionRegex = RegExp(r'^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?(\+[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?$');
    if (!versionRegex.hasMatch(value)) {
      return message ?? '请输入有效的版本号（如：1.0.0）';
    }
    return null;
  }

  /// 颜色值验证（十六进制）
  static String? hexColor(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final colorRegex = RegExp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$');
    if (!colorRegex.hasMatch(value)) {
      return message ?? '请输入有效的颜色值（如：#FF0000）';
    }
    return null;
  }

  /// 经度验证
  static String? longitude(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final lng = double.tryParse(value);
    if (lng == null || lng < -180 || lng > 180) {
      return message ?? '经度必须在-180到180之间';
    }
    return null;
  }

  /// 纬度验证
  static String? latitude(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final lat = double.tryParse(value);
    if (lat == null || lat < -90 || lat > 90) {
      return message ?? '纬度必须在-90到90之间';
    }
    return null;
  }

  /// 信用卡号验证
  static String? creditCard(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    // 移除空格和连字符
    final cleaned = value.replaceAll(RegExp(r'[\s-]'), '');
    
    // 检查长度（13-19位）
    if (cleaned.length < 13 || cleaned.length > 19) {
      return message ?? '信用卡号长度不正确';
    }
    
    if (!RegExp(r'^\d+$').hasMatch(cleaned)) {
      return message ?? '信用卡号只能包含数字';
    }
    
    // Luhn算法验证
    if (!_luhnCheck(cleaned)) {
      return message ?? '信用卡号格式不正确';
    }
    
    return null;
  }

  /// ISBN验证
  static String? isbn(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    // 移除连字符
    final cleaned = value.replaceAll('-', '');
    
    // ISBN-10或ISBN-13
    if (cleaned.length == 10) {
      return _validateIsbn10(cleaned, message);
    } else if (cleaned.length == 13) {
      return _validateIsbn13(cleaned, message);
    } else {
      return message ?? '请输入有效的ISBN号';
    }
  }

  /// 统一社会信用代码验证
  static String? socialCreditCode(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length != 18) {
      return message ?? '统一社会信用代码必须为18位';
    }
    
    final codeRegex = RegExp(r'^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$');
    if (!codeRegex.hasMatch(value)) {
      return message ?? '统一社会信用代码格式不正确';
    }
    
    return null;
  }

  /// 组织机构代码验证
  static String? organizationCode(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length != 9) {
      return message ?? '组织机构代码必须为9位';
    }
    
    final codeRegex = RegExp(r'^[A-Z0-9]{8}-[A-Z0-9]$');
    if (!codeRegex.hasMatch(value)) {
      return message ?? '组织机构代码格式不正确';
    }
    
    return null;
  }

  /// 税号验证
  static String? taxNumber(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    // 纳税人识别号可能是15位、17位、18位或20位
    if (![15, 17, 18, 20].contains(value.length)) {
      return message ?? '税号长度不正确';
    }
    
    if (!RegExp(r'^[0-9A-Z]+$').hasMatch(value)) {
      return message ?? '税号只能包含数字和大写字母';
    }
    
    return null;
  }

  /// Luhn算法校验
  static bool _luhnCheck(String cardNumber) {
    int sum = 0;
    bool isEven = false;
    
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit = digit ~/ 10 + digit % 10;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 == 0;
  }

  /// ISBN-10验证
  static String? _validateIsbn10(String isbn, String? message) {
    if (!RegExp(r'^[0-9]{9}[0-9X]$').hasMatch(isbn)) {
      return message ?? '请输入有效的ISBN-10号';
    }
    
    int sum = 0;
    for (int i = 0; i < 9; i++) {
      sum += int.parse(isbn[i]) * (10 - i);
    }
    
    final checkDigit = isbn[9];
    final remainder = sum % 11;
    final expectedCheck = remainder == 0 ? '0' : 
                         remainder == 1 ? 'X' : 
                         (11 - remainder).toString();
    
    if (checkDigit != expectedCheck) {
      return message ?? 'ISBN-10校验失败';
    }
    
    return null;
  }

  /// ISBN-13验证
  static String? _validateIsbn13(String isbn, String? message) {
    if (!RegExp(r'^\d{13}$').hasMatch(isbn)) {
      return message ?? '请输入有效的ISBN-13号';
    }
    
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      final digit = int.parse(isbn[i]);
      sum += i.isEven ? digit : digit * 3;
    }
    
    final checkDigit = int.parse(isbn[12]);
    final expectedCheck = (10 - (sum % 10)) % 10;
    
    if (checkDigit != expectedCheck) {
      return message ?? 'ISBN-13校验失败';
    }
    
    return null;
  }
}