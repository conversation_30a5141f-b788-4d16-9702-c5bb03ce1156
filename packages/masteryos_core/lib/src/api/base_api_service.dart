import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import '../config/app_config.dart';
import '../services/storage_service.dart';
import '../utils/network_utils.dart';

abstract class BaseApiService {
  late final Dio _dio;
  late final Logger _logger;
  final StorageService _storage = StorageService();

  BaseApiService({
    required String baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Map<String, dynamic>? headers,
  }) {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 5,
        lineLength: 50,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
    );

    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout ?? const Duration(seconds: 30),
      receiveTimeout: receiveTimeout ?? const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...?headers,
      },
    ));

    _setupInterceptors();
  }

  Dio get dio => _dio;

  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          await _onRequest(options, handler);
        },
        onResponse: (response, handler) {
          _onResponse(response, handler);
        },
        onError: (error, handler) {
          _onError(error, handler);
        },
      ),
    );

    // 日志拦截器 (仅在调试模式下)
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestHeader: false,
          requestBody: true,
          responseHeader: false,
          responseBody: true,
          error: true,
          logPrint: (obj) => _logger.d(obj),
        ),
      );
    }
  }

  Future<void> _onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // 添加认证头
    final token = await _storage.getAccessToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    // 添加设备信息
    final deviceId = await _storage.getDeviceId();
    options.headers['X-Device-Id'] = deviceId;

    // 添加应用版本
    final version = AppConfig.version;
    options.headers['X-App-Version'] = version;

    // 添加平台信息
    options.headers['X-Platform'] = defaultTargetPlatform.name;

    // 检查网络连接
    if (!(await NetworkUtils.isConnected())) {
      throw DioException(
        requestOptions: options,
        type: DioExceptionType.connectionError,
        message: 'No network connection',
      );
    }

    handler.next(options);
  }

  void _onResponse(Response response, ResponseInterceptorHandler handler) {
    _logger.i(
        'API Response: ${response.statusCode} ${response.requestOptions.path}');
    handler.next(response);
  }

  void _onError(DioException error, ErrorInterceptorHandler handler) {
    _logger.e('API Error: ${error.message}', error: error);

    // 处理认证错误
    if (error.response?.statusCode == 401) {
      _handleUnauthorized();
    }

    // 转换错误
    final customError = _convertError(error);
    handler.next(customError);
  }

  void _handleUnauthorized() {
    // 清除本地认证信息
    _storage.clearAuthData();

    // 这里可以触发全局的登出事件
    // 具体实现可以通过EventBus或其他方式
  }

  DioException _convertError(DioException error) {
    String message;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Request timeout';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Response timeout';
        break;
      case DioExceptionType.badResponse:
        message = _extractErrorMessage(error.response);
        break;
      case DioExceptionType.cancel:
        message = 'Request cancelled';
        break;
      case DioExceptionType.connectionError:
        message = 'No network connection';
        break;
      case DioExceptionType.badCertificate:
        message = 'Certificate error';
        break;
      case DioExceptionType.unknown:
        message = error.message ?? 'Unknown error occurred';
        break;
    }

    return DioException(
      requestOptions: error.requestOptions,
      response: error.response,
      type: error.type,
      message: message,
    );
  }

  String _extractErrorMessage(Response? response) {
    if (response?.data is Map<String, dynamic>) {
      final data = response!.data as Map<String, dynamic>;

      // 尝试从不同字段获取错误信息
      if (data.containsKey('message')) {
        return data['message'].toString();
      }
      if (data.containsKey('error')) {
        return data['error'].toString();
      }
      if (data.containsKey('errors')) {
        final errors = data['errors'];
        if (errors is List && errors.isNotEmpty) {
          return errors.first.toString();
        }
        if (errors is Map && errors.isNotEmpty) {
          return errors.values.first.toString();
        }
      }
    }

    return 'Request failed with status ${response?.statusCode}';
  }

  // 通用的GET请求
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.get<T>(
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  // 通用的POST请求
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  // 通用的PUT请求
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.put<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  // 通用的DELETE请求
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.delete<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  // 文件上传
  Future<Response<T>> upload<T>(
    String path,
    FormData formData, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
  }) async {
    return await _dio.post<T>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
    );
  }

  // 文件下载
  Future<Response> download(
    String path,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    return await _dio.download(
      path,
      savePath,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
    );
  }

  // 设置访问令牌
  void setAccessToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  // 清除访问令牌
  void clearAccessToken() {
    _dio.options.headers.remove('Authorization');
  }

  // 关闭Dio实例
  void close() {
    _dio.close();
  }
}
