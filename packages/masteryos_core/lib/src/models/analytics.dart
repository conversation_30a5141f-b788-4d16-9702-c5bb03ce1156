import 'package:json_annotation/json_annotation.dart';

part 'analytics.g.dart';

@JsonSerializable()
class AnalyticsEvent {
  final String id;
  final String eventType;
  final String? userId;
  final Map<String, dynamic> properties;
  final DateTime timestamp;
  final String? sessionId;
  final String? deviceId;
  final String? platform;
  final String? version;

  const AnalyticsEvent({
    required this.id,
    required this.eventType,
    this.userId,
    required this.properties,
    required this.timestamp,
    this.sessionId,
    this.deviceId,
    this.platform,
    this.version,
  });

  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsEventFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticsEventToJson(this);
}

@JsonSerializable()
class AnalyticsDailyStats {
  final String id;
  final DateTime date;
  final int totalUsers;
  final int activeUsers;
  final int newUsers;
  final int totalSessions;
  final double averageSessionDuration;
  final int totalPageViews;
  final int uniquePageViews;
  final double bounceRate;
  final Map<String, int> eventCounts;
  final Map<String, dynamic> customMetrics;

  const AnalyticsDailyStats({
    required this.id,
    required this.date,
    required this.totalUsers,
    required this.activeUsers,
    required this.newUsers,
    required this.totalSessions,
    required this.averageSessionDuration,
    required this.totalPageViews,
    required this.uniquePageViews,
    required this.bounceRate,
    required this.eventCounts,
    required this.customMetrics,
  });

  factory AnalyticsDailyStats.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsDailyStatsFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticsDailyStatsToJson(this);

  // 便利方法
  double get retentionRate => 1.0 - bounceRate;
  double get newUserRate => totalUsers > 0 ? newUsers / totalUsers : 0.0;
  double get activeUserRate => totalUsers > 0 ? activeUsers / totalUsers : 0.0;
}

// 分析查询参数
@JsonSerializable()
class AnalyticsQuery {
  final DateTime startDate;
  final DateTime endDate;
  final String? eventType;
  final String? userId;
  final String? platform;
  final List<String>? metrics;
  final String? groupBy;
  final Map<String, dynamic>? filters;

  const AnalyticsQuery({
    required this.startDate,
    required this.endDate,
    this.eventType,
    this.userId,
    this.platform,
    this.metrics,
    this.groupBy,
    this.filters,
  });

  factory AnalyticsQuery.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsQueryFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticsQueryToJson(this);

  // 便利构造器
  factory AnalyticsQuery.lastDays(int days, {
    String? eventType,
    String? userId,
    String? platform,
    List<String>? metrics,
    String? groupBy,
  }) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));
    
    return AnalyticsQuery(
      startDate: startDate,
      endDate: endDate,
      eventType: eventType,
      userId: userId,
      platform: platform,
      metrics: metrics,
      groupBy: groupBy,
    );
  }

  factory AnalyticsQuery.thisMonth({
    String? eventType,
    String? userId,
    String? platform,
    List<String>? metrics,
    String? groupBy,
  }) {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, 1);
    final endDate = DateTime(now.year, now.month + 1, 0);
    
    return AnalyticsQuery(
      startDate: startDate,
      endDate: endDate,
      eventType: eventType,
      userId: userId,
      platform: platform,
      metrics: metrics,
      groupBy: groupBy,
    );
  }

  int get daysDifference => endDate.difference(startDate).inDays;
  bool get isToday {
    final now = DateTime.now();
    return startDate.year == now.year &&
           startDate.month == now.month &&
           startDate.day == now.day &&
           endDate.year == now.year &&
           endDate.month == now.month &&
           endDate.day == now.day;
  }
}

// 分析结果
@JsonSerializable()
class AnalyticsResult {
  final List<AnalyticsDailyStats> dailyStats;
  final Map<String, dynamic> summary;
  final Map<String, List<Map<String, dynamic>>> breakdown;
  final AnalyticsQuery query;

  const AnalyticsResult({
    required this.dailyStats,
    required this.summary,
    required this.breakdown,
    required this.query,
  });

  factory AnalyticsResult.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsResultFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticsResultToJson(this);

  // 便利方法
  int get totalDays => dailyStats.length;
  double get averageActiveUsers {
    if (dailyStats.isEmpty) return 0.0;
    return dailyStats.map((s) => s.activeUsers).reduce((a, b) => a + b) / dailyStats.length;
  }
  
  double get averageBounceRate {
    if (dailyStats.isEmpty) return 0.0;
    return dailyStats.map((s) => s.bounceRate).reduce((a, b) => a + b) / dailyStats.length;
  }
}