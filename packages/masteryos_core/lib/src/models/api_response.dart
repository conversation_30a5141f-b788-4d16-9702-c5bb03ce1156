import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final Map<String, dynamic>? errors;
  final int? code;
  final String? timestamp;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
    this.code,
    this.timestamp,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  // 便利构造器
  factory ApiResponse.success({
    required T data,
    String message = 'Success',
    int? code,
  }) {
    return ApiResponse<T>(
      success: true,
      message: message,
      data: data,
      code: code ?? 200,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  factory ApiResponse.error({
    required String message,
    Map<String, dynamic>? errors,
    int? code,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errors: errors,
      code: code ?? 500,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  bool get isSuccess => success;
  bool get isError => !success;
  bool get hasData => data != null;
  bool get hasErrors => errors != null && errors!.isNotEmpty;
}

// 简化的API响应（不需要泛型）
@JsonSerializable()
class SimpleApiResponse {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;
  final Map<String, dynamic>? errors;
  final int? code;
  final String? timestamp;

  const SimpleApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
    this.code,
    this.timestamp,
  });

  factory SimpleApiResponse.fromJson(Map<String, dynamic> json) =>
      _$SimpleApiResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SimpleApiResponseToJson(this);

  bool get isSuccess => success;
  bool get isError => !success;
  bool get hasData => data != null && data!.isNotEmpty;
  bool get hasErrors => errors != null && errors!.isNotEmpty;
}

// API错误响应
@JsonSerializable()
class ApiError {
  final String code;
  final String message;
  final String? field;
  final Map<String, dynamic>? details;

  const ApiError({
    required this.code,
    required this.message,
    this.field,
    this.details,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) =>
      _$ApiErrorFromJson(json);

  Map<String, dynamic> toJson() => _$ApiErrorToJson(this);
}