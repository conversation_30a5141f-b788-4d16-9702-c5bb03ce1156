// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Document _$DocumentFromJson(Map<String, dynamic> json) => Document(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String?,
  fileName: json['fileName'] as String,
  filePath: json['filePath'] as String,
  type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
  status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
  accessLevel: $enumDecode(_$AccessLevelEnumMap, json['accessLevel']),
  fileSize: (json['fileSize'] as num).toInt(),
  mimeType: json['mimeType'] as String?,
  uploadedBy: json['uploadedBy'] as String,
  uploadedAt: DateTime.parse(json['uploadedAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  category: json['category'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
  downloadCount: (json['downloadCount'] as num?)?.toInt() ?? 0,
  viewCount: (json['viewCount'] as num?)?.toInt() ?? 0,
  lastAccessedAt: json['lastAccessedAt'] == null
      ? null
      : DateTime.parse(json['lastAccessedAt'] as String),
);

Map<String, dynamic> _$DocumentToJson(Document instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'fileName': instance.fileName,
  'filePath': instance.filePath,
  'type': _$DocumentTypeEnumMap[instance.type]!,
  'status': _$DocumentStatusEnumMap[instance.status]!,
  'accessLevel': _$AccessLevelEnumMap[instance.accessLevel]!,
  'fileSize': instance.fileSize,
  'mimeType': instance.mimeType,
  'uploadedBy': instance.uploadedBy,
  'uploadedAt': instance.uploadedAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'tags': instance.tags,
  'category': instance.category,
  'metadata': instance.metadata,
  'downloadCount': instance.downloadCount,
  'viewCount': instance.viewCount,
  'lastAccessedAt': instance.lastAccessedAt?.toIso8601String(),
};

const _$DocumentTypeEnumMap = {
  DocumentType.pdf: 'pdf',
  DocumentType.doc: 'doc',
  DocumentType.docx: 'docx',
  DocumentType.txt: 'txt',
  DocumentType.md: 'md',
  DocumentType.image: 'image',
  DocumentType.video: 'video',
  DocumentType.audio: 'audio',
  DocumentType.other: 'other',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.draft: 'draft',
  DocumentStatus.published: 'published',
  DocumentStatus.archived: 'archived',
  DocumentStatus.deleted: 'deleted',
};

const _$AccessLevelEnumMap = {
  AccessLevel.public: 'public',
  AccessLevel.internal: 'internal',
  AccessLevel.private: 'private',
  AccessLevel.restricted: 'restricted',
};
