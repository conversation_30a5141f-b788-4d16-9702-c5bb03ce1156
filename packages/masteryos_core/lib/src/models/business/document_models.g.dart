// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Document _$DocumentFromJson(Map<String, dynamic> json) => Document(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
  status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
  accessLevel: $enumDecode(_$AccessLevelEnumMap, json['accessLevel']),
  filePath: json['filePath'] as String,
  url: json['url'] as String?,
  fileSize: (json['fileSize'] as num).toInt(),
  mimeType: json['mimeType'] as String,
  thumbnailPath: json['thumbnailPath'] as String?,
  thumbnailUrl: json['thumbnailUrl'] as String?,
  uploadedBy: json['uploadedBy'] as String,
  uploadedAt: DateTime.parse(json['uploadedAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  downloadCount: (json['downloadCount'] as num?)?.toInt() ?? 0,
  viewCount: (json['viewCount'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$DocumentToJson(Document instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'type': _$DocumentTypeEnumMap[instance.type]!,
  'status': _$DocumentStatusEnumMap[instance.status]!,
  'accessLevel': _$AccessLevelEnumMap[instance.accessLevel]!,
  'filePath': instance.filePath,
  'url': instance.url,
  'fileSize': instance.fileSize,
  'mimeType': instance.mimeType,
  'thumbnailPath': instance.thumbnailPath,
  'thumbnailUrl': instance.thumbnailUrl,
  'uploadedBy': instance.uploadedBy,
  'uploadedAt': instance.uploadedAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'metadata': instance.metadata,
  'tags': instance.tags,
  'downloadCount': instance.downloadCount,
  'viewCount': instance.viewCount,
};

const _$DocumentTypeEnumMap = {
  DocumentType.pdf: 'pdf',
  DocumentType.word: 'word',
  DocumentType.excel: 'excel',
  DocumentType.powerpoint: 'powerpoint',
  DocumentType.text: 'text',
  DocumentType.image: 'image',
  DocumentType.video: 'video',
  DocumentType.audio: 'audio',
  DocumentType.archive: 'archive',
  DocumentType.other: 'other',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.draft: 'draft',
  DocumentStatus.published: 'published',
  DocumentStatus.archived: 'archived',
  DocumentStatus.deleted: 'deleted',
};

const _$AccessLevelEnumMap = {
  AccessLevel.public: 'public',
  AccessLevel.internal: 'internal',
  AccessLevel.private: 'private',
  AccessLevel.restricted: 'restricted',
};

DocumentListResponse _$DocumentListResponseFromJson(
  Map<String, dynamic> json,
) => DocumentListResponse(
  documents: (json['documents'] as List<dynamic>)
      .map((e) => Document.fromJson(e as Map<String, dynamic>))
      .toList(),
  pagination: PaginationMeta.fromJson(
    json['pagination'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$DocumentListResponseToJson(
  DocumentListResponse instance,
) => <String, dynamic>{
  'documents': instance.documents,
  'pagination': instance.pagination,
};

PaginationMeta _$PaginationMetaFromJson(Map<String, dynamic> json) =>
    PaginationMeta(
      currentPage: (json['currentPage'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      totalItems: (json['totalItems'] as num).toInt(),
      itemsPerPage: (json['itemsPerPage'] as num).toInt(),
      hasNextPage: json['hasNextPage'] as bool,
      hasPreviousPage: json['hasPreviousPage'] as bool,
    );

Map<String, dynamic> _$PaginationMetaToJson(PaginationMeta instance) =>
    <String, dynamic>{
      'currentPage': instance.currentPage,
      'totalPages': instance.totalPages,
      'totalItems': instance.totalItems,
      'itemsPerPage': instance.itemsPerPage,
      'hasNextPage': instance.hasNextPage,
      'hasPreviousPage': instance.hasPreviousPage,
    };

DocumentStats _$DocumentStatsFromJson(Map<String, dynamic> json) =>
    DocumentStats(
      totalDocuments: (json['totalDocuments'] as num).toInt(),
      publishedDocuments: (json['publishedDocuments'] as num).toInt(),
      draftDocuments: (json['draftDocuments'] as num).toInt(),
      archivedDocuments: (json['archivedDocuments'] as num).toInt(),
      totalFileSize: (json['totalFileSize'] as num).toInt(),
      documentsByType: (json['documentsByType'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry($enumDecode(_$DocumentTypeEnumMap, k), (e as num).toInt()),
      ),
      documentsByAccessLevel:
          (json['documentsByAccessLevel'] as Map<String, dynamic>).map(
            (k, e) => MapEntry(
              $enumDecode(_$AccessLevelEnumMap, k),
              (e as num).toInt(),
            ),
          ),
      documentsByUploader: Map<String, int>.from(
        json['documentsByUploader'] as Map,
      ),
    );

Map<String, dynamic> _$DocumentStatsToJson(DocumentStats instance) =>
    <String, dynamic>{
      'totalDocuments': instance.totalDocuments,
      'publishedDocuments': instance.publishedDocuments,
      'draftDocuments': instance.draftDocuments,
      'archivedDocuments': instance.archivedDocuments,
      'totalFileSize': instance.totalFileSize,
      'documentsByType': instance.documentsByType.map(
        (k, e) => MapEntry(_$DocumentTypeEnumMap[k]!, e),
      ),
      'documentsByAccessLevel': instance.documentsByAccessLevel.map(
        (k, e) => MapEntry(_$AccessLevelEnumMap[k]!, e),
      ),
      'documentsByUploader': instance.documentsByUploader,
    };

DocumentQueryParams _$DocumentQueryParamsFromJson(Map<String, dynamic> json) =>
    DocumentQueryParams(
      page: (json['page'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      search: json['search'] as String?,
      type: $enumDecodeNullable(_$DocumentTypeEnumMap, json['type']),
      status: $enumDecodeNullable(_$DocumentStatusEnumMap, json['status']),
      accessLevel: $enumDecodeNullable(
        _$AccessLevelEnumMap,
        json['accessLevel'],
      ),
      uploadedBy: json['uploadedBy'] as String?,
      sortBy: json['sortBy'] as String?,
      sortOrder: json['sortOrder'] as String?,
      uploadedAfter: json['uploadedAfter'] == null
          ? null
          : DateTime.parse(json['uploadedAfter'] as String),
      uploadedBefore: json['uploadedBefore'] == null
          ? null
          : DateTime.parse(json['uploadedBefore'] as String),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$DocumentQueryParamsToJson(
  DocumentQueryParams instance,
) => <String, dynamic>{
  'page': instance.page,
  'limit': instance.limit,
  'search': instance.search,
  'type': _$DocumentTypeEnumMap[instance.type],
  'status': _$DocumentStatusEnumMap[instance.status],
  'accessLevel': _$AccessLevelEnumMap[instance.accessLevel],
  'uploadedBy': instance.uploadedBy,
  'sortBy': instance.sortBy,
  'sortOrder': instance.sortOrder,
  'uploadedAfter': instance.uploadedAfter?.toIso8601String(),
  'uploadedBefore': instance.uploadedBefore?.toIso8601String(),
  'tags': instance.tags,
};

DocumentUploadRequest _$DocumentUploadRequestFromJson(
  Map<String, dynamic> json,
) => DocumentUploadRequest(
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
  status:
      $enumDecodeNullable(_$DocumentStatusEnumMap, json['status']) ??
      DocumentStatus.draft,
  accessLevel:
      $enumDecodeNullable(_$AccessLevelEnumMap, json['accessLevel']) ??
      AccessLevel.private,
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$DocumentUploadRequestToJson(
  DocumentUploadRequest instance,
) => <String, dynamic>{
  'name': instance.name,
  'description': instance.description,
  'type': _$DocumentTypeEnumMap[instance.type]!,
  'status': _$DocumentStatusEnumMap[instance.status]!,
  'accessLevel': _$AccessLevelEnumMap[instance.accessLevel]!,
  'tags': instance.tags,
  'metadata': instance.metadata,
};

DocumentUpdateRequest _$DocumentUpdateRequestFromJson(
  Map<String, dynamic> json,
) => DocumentUpdateRequest(
  name: json['name'] as String?,
  description: json['description'] as String?,
  status: $enumDecodeNullable(_$DocumentStatusEnumMap, json['status']),
  accessLevel: $enumDecodeNullable(_$AccessLevelEnumMap, json['accessLevel']),
  tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$DocumentUpdateRequestToJson(
  DocumentUpdateRequest instance,
) => <String, dynamic>{
  'name': instance.name,
  'description': instance.description,
  'status': _$DocumentStatusEnumMap[instance.status],
  'accessLevel': _$AccessLevelEnumMap[instance.accessLevel],
  'tags': instance.tags,
  'metadata': instance.metadata,
};

BulkDocumentOperationRequest _$BulkDocumentOperationRequestFromJson(
  Map<String, dynamic> json,
) => BulkDocumentOperationRequest(
  documentIds: (json['documentIds'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  operation: $enumDecode(_$BulkDocumentOperationEnumMap, json['operation']),
  params: json['params'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$BulkDocumentOperationRequestToJson(
  BulkDocumentOperationRequest instance,
) => <String, dynamic>{
  'documentIds': instance.documentIds,
  'operation': _$BulkDocumentOperationEnumMap[instance.operation]!,
  'params': instance.params,
};

const _$BulkDocumentOperationEnumMap = {
  BulkDocumentOperation.delete: 'delete',
  BulkDocumentOperation.archive: 'archive',
  BulkDocumentOperation.publish: 'publish',
  BulkDocumentOperation.changeAccessLevel: 'changeAccessLevel',
  BulkDocumentOperation.addTags: 'addTags',
  BulkDocumentOperation.removeTags: 'removeTags',
  BulkDocumentOperation.export: 'export',
};
