// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserListResponse _$UserListResponseFromJson(Map<String, dynamic> json) =>
    UserListResponse(
      users: (json['users'] as List<dynamic>)
          .map((e) => User.fromJson(e as Map<String, dynamic>))
          .toList(),
      pagination: PaginationMeta.fromJson(
        json['pagination'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$UserListResponseToJson(UserListResponse instance) =>
    <String, dynamic>{
      'users': instance.users,
      'pagination': instance.pagination,
    };

PaginationMeta _$PaginationMetaFromJson(Map<String, dynamic> json) =>
    PaginationMeta(
      currentPage: (json['currentPage'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      totalItems: (json['totalItems'] as num).toInt(),
      itemsPerPage: (json['itemsPerPage'] as num).toInt(),
      hasNextPage: json['hasNextPage'] as bool,
      hasPreviousPage: json['hasPreviousPage'] as bool,
    );

Map<String, dynamic> _$PaginationMetaToJson(PaginationMeta instance) =>
    <String, dynamic>{
      'currentPage': instance.currentPage,
      'totalPages': instance.totalPages,
      'totalItems': instance.totalItems,
      'itemsPerPage': instance.itemsPerPage,
      'hasNextPage': instance.hasNextPage,
      'hasPreviousPage': instance.hasPreviousPage,
    };

UserStats _$UserStatsFromJson(Map<String, dynamic> json) => UserStats(
  totalUsers: (json['totalUsers'] as num).toInt(),
  activeUsers: (json['activeUsers'] as num).toInt(),
  inactiveUsers: (json['inactiveUsers'] as num).toInt(),
  suspendedUsers: (json['suspendedUsers'] as num).toInt(),
  adminUsers: (json['adminUsers'] as num).toInt(),
  regularUsers: (json['regularUsers'] as num).toInt(),
  usersByRole: (json['usersByRole'] as Map<String, dynamic>).map(
    (k, e) => MapEntry($enumDecode(_$UserRoleEnumMap, k), (e as num).toInt()),
  ),
  usersByStatus: (json['usersByStatus'] as Map<String, dynamic>).map(
    (k, e) => MapEntry($enumDecode(_$UserStatusEnumMap, k), (e as num).toInt()),
  ),
);

Map<String, dynamic> _$UserStatsToJson(UserStats instance) => <String, dynamic>{
  'totalUsers': instance.totalUsers,
  'activeUsers': instance.activeUsers,
  'inactiveUsers': instance.inactiveUsers,
  'suspendedUsers': instance.suspendedUsers,
  'adminUsers': instance.adminUsers,
  'regularUsers': instance.regularUsers,
  'usersByRole': instance.usersByRole.map(
    (k, e) => MapEntry(_$UserRoleEnumMap[k]!, e),
  ),
  'usersByStatus': instance.usersByStatus.map(
    (k, e) => MapEntry(_$UserStatusEnumMap[k]!, e),
  ),
};

const _$UserRoleEnumMap = {
  UserRole.admin: 'admin',
  UserRole.manager: 'manager',
  UserRole.user: 'user',
  UserRole.guest: 'guest',
};

const _$UserStatusEnumMap = {
  UserStatus.active: 'active',
  UserStatus.inactive: 'inactive',
  UserStatus.suspended: 'suspended',
  UserStatus.pending: 'pending',
};

UserQueryParams _$UserQueryParamsFromJson(Map<String, dynamic> json) =>
    UserQueryParams(
      page: (json['page'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      search: json['search'] as String?,
      role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']),
      status: $enumDecodeNullable(_$UserStatusEnumMap, json['status']),
      sortBy: json['sortBy'] as String?,
      sortOrder: json['sortOrder'] as String?,
      createdAfter: json['createdAfter'] == null
          ? null
          : DateTime.parse(json['createdAfter'] as String),
      createdBefore: json['createdBefore'] == null
          ? null
          : DateTime.parse(json['createdBefore'] as String),
    );

Map<String, dynamic> _$UserQueryParamsToJson(UserQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'limit': instance.limit,
      'search': instance.search,
      'role': _$UserRoleEnumMap[instance.role],
      'status': _$UserStatusEnumMap[instance.status],
      'sortBy': instance.sortBy,
      'sortOrder': instance.sortOrder,
      'createdAfter': instance.createdAfter?.toIso8601String(),
      'createdBefore': instance.createdBefore?.toIso8601String(),
    };

CreateUserRequest _$CreateUserRequestFromJson(Map<String, dynamic> json) =>
    CreateUserRequest(
      email: json['email'] as String,
      name: json['name'] as String,
      password: json['password'] as String?,
      role:
          $enumDecodeNullable(_$UserRoleEnumMap, json['role']) ?? UserRole.user,
      status:
          $enumDecodeNullable(_$UserStatusEnumMap, json['status']) ??
          UserStatus.active,
      avatar: json['avatar'] as String?,
      preferences: json['preferences'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CreateUserRequestToJson(CreateUserRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'name': instance.name,
      'password': instance.password,
      'role': _$UserRoleEnumMap[instance.role]!,
      'status': _$UserStatusEnumMap[instance.status]!,
      'avatar': instance.avatar,
      'preferences': instance.preferences,
    };

UpdateUserRequest _$UpdateUserRequestFromJson(Map<String, dynamic> json) =>
    UpdateUserRequest(
      email: json['email'] as String?,
      name: json['name'] as String?,
      password: json['password'] as String?,
      role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']),
      status: $enumDecodeNullable(_$UserStatusEnumMap, json['status']),
      avatar: json['avatar'] as String?,
      preferences: json['preferences'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UpdateUserRequestToJson(UpdateUserRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'name': instance.name,
      'password': instance.password,
      'role': _$UserRoleEnumMap[instance.role],
      'status': _$UserStatusEnumMap[instance.status],
      'avatar': instance.avatar,
      'preferences': instance.preferences,
    };

BulkUserOperationRequest _$BulkUserOperationRequestFromJson(
  Map<String, dynamic> json,
) => BulkUserOperationRequest(
  userIds: (json['userIds'] as List<dynamic>).map((e) => e as String).toList(),
  operation: $enumDecode(_$BulkUserOperationEnumMap, json['operation']),
  params: json['params'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$BulkUserOperationRequestToJson(
  BulkUserOperationRequest instance,
) => <String, dynamic>{
  'userIds': instance.userIds,
  'operation': _$BulkUserOperationEnumMap[instance.operation]!,
  'params': instance.params,
};

const _$BulkUserOperationEnumMap = {
  BulkUserOperation.delete: 'delete',
  BulkUserOperation.activate: 'activate',
  BulkUserOperation.deactivate: 'deactivate',
  BulkUserOperation.suspend: 'suspend',
  BulkUserOperation.changeRole: 'changeRole',
  BulkUserOperation.export: 'export',
};

UserActivityStats _$UserActivityStatsFromJson(Map<String, dynamic> json) =>
    UserActivityStats(
      userId: json['userId'] as String,
      loginCount: (json['loginCount'] as num).toInt(),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      sessionCount: (json['sessionCount'] as num).toInt(),
      averageSessionDuration: Duration(
        microseconds: (json['averageSessionDuration'] as num).toInt(),
      ),
      actionsCount: (json['actionsCount'] as num).toInt(),
      actionsByType: Map<String, int>.from(json['actionsByType'] as Map),
    );

Map<String, dynamic> _$UserActivityStatsToJson(UserActivityStats instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'loginCount': instance.loginCount,
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'sessionCount': instance.sessionCount,
      'averageSessionDuration': instance.averageSessionDuration.inMicroseconds,
      'actionsCount': instance.actionsCount,
      'actionsByType': instance.actionsByType,
    };

UserPermissions _$UserPermissionsFromJson(Map<String, dynamic> json) =>
    UserPermissions(
      userId: json['userId'] as String,
      role: $enumDecode(_$UserRoleEnumMap, json['role']),
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      roles: (json['roles'] as List<dynamic>).map((e) => e as String).toList(),
      customPermissions: json['customPermissions'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$UserPermissionsToJson(UserPermissions instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'role': _$UserRoleEnumMap[instance.role]!,
      'permissions': instance.permissions,
      'roles': instance.roles,
      'customPermissions': instance.customPermissions,
    };
