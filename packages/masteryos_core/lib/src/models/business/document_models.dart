import 'package:json_annotation/json_annotation.dart';

part 'document_models.g.dart';

/// 文档类型枚举
enum DocumentType {
  pdf,
  word,
  excel,
  powerpoint,
  text,
  image,
  video,
  audio,
  archive,
  other,
}

/// 文档状态枚举
enum DocumentStatus {
  draft,
  published,
  archived,
  deleted,
}

/// 访问级别枚举
enum AccessLevel {
  public,
  internal,
  private,
  restricted,
}

/// 文档模型
@JsonSerializable()
class Document {
  final String id;
  final String name;
  final String? description;
  final DocumentType type;
  final DocumentStatus status;
  final AccessLevel accessLevel;
  final String filePath;
  final String? url;
  final int fileSize;
  final String mimeType;
  final String? thumbnailPath;
  final String? thumbnailUrl;
  final String uploadedBy;
  final DateTime uploadedAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;
  final List<String> tags;
  final int downloadCount;
  final int viewCount;

  const Document({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.status,
    required this.accessLevel,
    required this.filePath,
    this.url,
    required this.fileSize,
    required this.mimeType,
    this.thumbnailPath,
    this.thumbnailUrl,
    required this.uploadedBy,
    required this.uploadedAt,
    required this.updatedAt,
    this.metadata,
    this.tags = const [],
    this.downloadCount = 0,
    this.viewCount = 0,
  });

  factory Document.fromJson(Map<String, dynamic> json) =>
      _$DocumentFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentToJson(this);

  /// 获取文档类型显示名称
  String get typeDisplayName {
    switch (type) {
      case DocumentType.pdf:
        return 'PDF';
      case DocumentType.word:
        return 'Word';
      case DocumentType.excel:
        return 'Excel';
      case DocumentType.powerpoint:
        return 'PowerPoint';
      case DocumentType.text:
        return '文本';
      case DocumentType.image:
        return '图片';
      case DocumentType.video:
        return '视频';
      case DocumentType.audio:
        return '音频';
      case DocumentType.archive:
        return '压缩包';
      case DocumentType.other:
        return '其他';
    }
  }

  /// 获取状态显示名称
  String get statusDisplayName {
    switch (status) {
      case DocumentStatus.draft:
        return '草稿';
      case DocumentStatus.published:
        return '已发布';
      case DocumentStatus.archived:
        return '已归档';
      case DocumentStatus.deleted:
        return '已删除';
    }
  }

  /// 获取访问级别显示名称
  String get accessLevelDisplayName {
    switch (accessLevel) {
      case AccessLevel.public:
        return '公开';
      case AccessLevel.internal:
        return '内部';
      case AccessLevel.private:
        return '私有';
      case AccessLevel.restricted:
        return '受限';
    }
  }

  /// 格式化文件大小
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// 是否为图片类型
  bool get isImage {
    return type == DocumentType.image ||
           mimeType.startsWith('image/');
  }

  /// 是否为视频类型
  bool get isVideo {
    return type == DocumentType.video ||
           mimeType.startsWith('video/');
  }

  /// 是否为音频类型
  bool get isAudio {
    return type == DocumentType.audio ||
           mimeType.startsWith('audio/');
  }

  /// 是否可预览
  bool get canPreview {
    return isImage || 
           type == DocumentType.pdf ||
           type == DocumentType.text ||
           mimeType == 'text/plain';
  }
}

/// 文档列表响应
@JsonSerializable()
class DocumentListResponse {
  final List<Document> documents;
  final PaginationMeta pagination;

  const DocumentListResponse({
    required this.documents,
    required this.pagination,
  });

  factory DocumentListResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentListResponseToJson(this);
}

/// 分页元数据
@JsonSerializable()
class PaginationMeta {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

/// 文档统计
@JsonSerializable()
class DocumentStats {
  final int totalDocuments;
  final int publishedDocuments;
  final int draftDocuments;
  final int archivedDocuments;
  final int totalFileSize;
  final Map<DocumentType, int> documentsByType;
  final Map<AccessLevel, int> documentsByAccessLevel;
  final Map<String, int> documentsByUploader;

  const DocumentStats({
    required this.totalDocuments,
    required this.publishedDocuments,
    required this.draftDocuments,
    required this.archivedDocuments,
    required this.totalFileSize,
    required this.documentsByType,
    required this.documentsByAccessLevel,
    required this.documentsByUploader,
  });

  factory DocumentStats.fromJson(Map<String, dynamic> json) =>
      _$DocumentStatsFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentStatsToJson(this);

  /// 获取格式化的总文件大小
  String get formattedTotalFileSize {
    if (totalFileSize < 1024) {
      return '$totalFileSize B';
    } else if (totalFileSize < 1024 * 1024) {
      return '${(totalFileSize / 1024).toStringAsFixed(1)} KB';
    } else if (totalFileSize < 1024 * 1024 * 1024) {
      return '${(totalFileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(totalFileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}

/// 文档查询参数
@JsonSerializable()
class DocumentQueryParams {
  final int? page;
  final int? limit;
  final String? search;
  final DocumentType? type;
  final DocumentStatus? status;
  final AccessLevel? accessLevel;
  final String? uploadedBy;
  final String? sortBy;
  final String? sortOrder;
  final DateTime? uploadedAfter;
  final DateTime? uploadedBefore;
  final List<String>? tags;

  const DocumentQueryParams({
    this.page,
    this.limit,
    this.search,
    this.type,
    this.status,
    this.accessLevel,
    this.uploadedBy,
    this.sortBy,
    this.sortOrder,
    this.uploadedAfter,
    this.uploadedBefore,
    this.tags,
  });

  factory DocumentQueryParams.fromJson(Map<String, dynamic> json) =>
      _$DocumentQueryParamsFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentQueryParamsToJson(this);

  /// 转换为查询字符串
  Map<String, String> toQueryString() {
    final Map<String, String> params = {};
    
    if (page != null) params['page'] = page.toString();
    if (limit != null) params['limit'] = limit.toString();
    if (search != null && search!.isNotEmpty) params['search'] = search!;
    if (type != null) params['type'] = type!.name;
    if (status != null) params['status'] = status!.name;
    if (accessLevel != null) params['accessLevel'] = accessLevel!.name;
    if (uploadedBy != null) params['uploadedBy'] = uploadedBy!;
    if (sortBy != null) params['sortBy'] = sortBy!;
    if (sortOrder != null) params['sortOrder'] = sortOrder!;
    if (uploadedAfter != null) params['uploadedAfter'] = uploadedAfter!.toIso8601String();
    if (uploadedBefore != null) params['uploadedBefore'] = uploadedBefore!.toIso8601String();
    if (tags != null && tags!.isNotEmpty) params['tags'] = tags!.join(',');
    
    return params;
  }
}

/// 文档上传请求
@JsonSerializable()
class DocumentUploadRequest {
  final String name;
  final String? description;
  final DocumentType type;
  final DocumentStatus status;
  final AccessLevel accessLevel;
  final List<String> tags;
  final Map<String, dynamic>? metadata;

  const DocumentUploadRequest({
    required this.name,
    this.description,
    required this.type,
    this.status = DocumentStatus.draft,
    this.accessLevel = AccessLevel.private,
    this.tags = const [],
    this.metadata,
  });

  factory DocumentUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentUploadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentUploadRequestToJson(this);
}

/// 文档更新请求
@JsonSerializable()
class DocumentUpdateRequest {
  final String? name;
  final String? description;
  final DocumentStatus? status;
  final AccessLevel? accessLevel;
  final List<String>? tags;
  final Map<String, dynamic>? metadata;

  const DocumentUpdateRequest({
    this.name,
    this.description,
    this.status,
    this.accessLevel,
    this.tags,
    this.metadata,
  });

  factory DocumentUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentUpdateRequestToJson(this);
}

/// 批量文档操作请求
@JsonSerializable()
class BulkDocumentOperationRequest {
  final List<String> documentIds;
  final BulkDocumentOperation operation;
  final Map<String, dynamic>? params;

  const BulkDocumentOperationRequest({
    required this.documentIds,
    required this.operation,
    this.params,
  });

  factory BulkDocumentOperationRequest.fromJson(Map<String, dynamic> json) =>
      _$BulkDocumentOperationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BulkDocumentOperationRequestToJson(this);
}

/// 批量文档操作类型
enum BulkDocumentOperation {
  delete,
  archive,
  publish,
  changeAccessLevel,
  addTags,
  removeTags,
  export,
}