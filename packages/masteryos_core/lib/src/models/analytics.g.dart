// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalyticsEvent _$AnalyticsEventFromJson(Map<String, dynamic> json) =>
    AnalyticsEvent(
      id: json['id'] as String,
      eventType: json['eventType'] as String,
      userId: json['userId'] as String?,
      properties: json['properties'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String?,
      deviceId: json['deviceId'] as String?,
      platform: json['platform'] as String?,
      version: json['version'] as String?,
    );

Map<String, dynamic> _$AnalyticsEventToJson(AnalyticsEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'eventType': instance.eventType,
      'userId': instance.userId,
      'properties': instance.properties,
      'timestamp': instance.timestamp.toIso8601String(),
      'sessionId': instance.sessionId,
      'deviceId': instance.deviceId,
      'platform': instance.platform,
      'version': instance.version,
    };

AnalyticsDailyStats _$AnalyticsDailyStatsFromJson(Map<String, dynamic> json) =>
    AnalyticsDailyStats(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      totalUsers: (json['totalUsers'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      newUsers: (json['newUsers'] as num).toInt(),
      totalSessions: (json['totalSessions'] as num).toInt(),
      averageSessionDuration: (json['averageSessionDuration'] as num)
          .toDouble(),
      totalPageViews: (json['totalPageViews'] as num).toInt(),
      uniquePageViews: (json['uniquePageViews'] as num).toInt(),
      bounceRate: (json['bounceRate'] as num).toDouble(),
      eventCounts: Map<String, int>.from(json['eventCounts'] as Map),
      customMetrics: json['customMetrics'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$AnalyticsDailyStatsToJson(
  AnalyticsDailyStats instance,
) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date.toIso8601String(),
  'totalUsers': instance.totalUsers,
  'activeUsers': instance.activeUsers,
  'newUsers': instance.newUsers,
  'totalSessions': instance.totalSessions,
  'averageSessionDuration': instance.averageSessionDuration,
  'totalPageViews': instance.totalPageViews,
  'uniquePageViews': instance.uniquePageViews,
  'bounceRate': instance.bounceRate,
  'eventCounts': instance.eventCounts,
  'customMetrics': instance.customMetrics,
};

AnalyticsQuery _$AnalyticsQueryFromJson(Map<String, dynamic> json) =>
    AnalyticsQuery(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      eventType: json['eventType'] as String?,
      userId: json['userId'] as String?,
      platform: json['platform'] as String?,
      metrics: (json['metrics'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      groupBy: json['groupBy'] as String?,
      filters: json['filters'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AnalyticsQueryToJson(AnalyticsQuery instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'eventType': instance.eventType,
      'userId': instance.userId,
      'platform': instance.platform,
      'metrics': instance.metrics,
      'groupBy': instance.groupBy,
      'filters': instance.filters,
    };

AnalyticsResult _$AnalyticsResultFromJson(Map<String, dynamic> json) =>
    AnalyticsResult(
      dailyStats: (json['dailyStats'] as List<dynamic>)
          .map((e) => AnalyticsDailyStats.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary: json['summary'] as Map<String, dynamic>,
      breakdown: (json['breakdown'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
          k,
          (e as List<dynamic>).map((e) => e as Map<String, dynamic>).toList(),
        ),
      ),
      query: AnalyticsQuery.fromJson(json['query'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AnalyticsResultToJson(AnalyticsResult instance) =>
    <String, dynamic>{
      'dailyStats': instance.dailyStats,
      'summary': instance.summary,
      'breakdown': instance.breakdown,
      'query': instance.query,
    };
