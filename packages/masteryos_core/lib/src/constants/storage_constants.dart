/// 存储相关常量
class StorageConstants {
  StorageConstants._();

  // SharedPreferences键名
  static const String keyAuthToken = 'auth_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyUserInfo = 'user_info';
  static const String keyUserSettings = 'user_settings';
  static const String keyAppSettings = 'app_settings';
  static const String keyTheme = 'theme';
  static const String keyLanguage = 'language';
  static const String keyRememberMe = 'remember_me';
  static const String keyLastLogin = 'last_login';
  static const String keyAppVersion = 'app_version';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyOnboardingCompleted = 'onboarding_completed';

  // 缓存键名
  static const String cacheUserProfile = 'user_profile';
  static const String cacheSkills = 'skills';
  static const String cacheLearningPaths = 'learning_paths';
  static const String cacheDocuments = 'documents';
  static const String cacheAnalytics = 'analytics';
  static const String cacheConfig = 'config';
  static const String cacheNews = 'news';
  static const String cacheNotifications = 'notifications';

  // 数据库表名
  static const String tableUsers = 'users';
  static const String tableSkills = 'skills';
  static const String tableDocuments = 'documents';
  static const String tableLearningPaths = 'learning_paths';
  static const String tableProgress = 'progress';
  static const String tableAnalytics = 'analytics';
  static const String tableSettings = 'settings';
  static const String tableCache = 'cache';
  static const String tableLogs = 'logs';

  // 数据库字段
  static const String columnId = 'id';
  static const String columnCreatedAt = 'created_at';
  static const String columnUpdatedAt = 'updated_at';
  static const String columnDeletedAt = 'deleted_at';
  static const String columnUserId = 'user_id';
  static const String columnName = 'name';
  static const String columnDescription = 'description';
  static const String columnStatus = 'status';
  static const String columnType = 'type';
  static const String columnData = 'data';
  static const String columnMetadata = 'metadata';

  // 文件路径
  static const String dirDocuments = 'documents';
  static const String dirImages = 'images';
  static const String dirCache = 'cache';
  static const String dirLogs = 'logs';
  static const String dirTemp = 'temp';
  static const String dirBackup = 'backup';

  // 文件名模式
  static const String fileUserData = 'user_data.json';
  static const String fileSettings = 'settings.json';
  static const String fileCache = 'cache.json';
  static const String fileLog = 'app.log';
  static const String fileBackup = 'backup_{timestamp}.zip';

  // 存储限制
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxBackupFiles = 5;
  static const int maxTempFiles = 50;

  // 缓存过期时间（秒）
  static const int cacheShortExpiry = 300; // 5分钟
  static const int cacheMediumExpiry = 3600; // 1小时
  static const int cacheLongExpiry = 86400; // 24小时
  static const int cacheVeryLongExpiry = 604800; // 7天

  // 数据同步
  static const String syncStatusPending = 'pending';
  static const String syncStatusInProgress = 'in_progress';
  static const String syncStatusCompleted = 'completed';
  static const String syncStatusFailed = 'failed';

  // 备份相关
  static const String backupTypeManual = 'manual';
  static const String backupTypeAutomatic = 'automatic';
  static const String backupTypeScheduled = 'scheduled';

  // 存储类型
  static const String storageTypeLocal = 'local';
  static const String storageTypeCloud = 'cloud';
  static const String storageTypeHybrid = 'hybrid';

  // 加密相关
  static const String encryptionAlgorithm = 'AES-256-GCM';
  static const String keyDerivationFunction = 'PBKDF2';
  static const int keyDerivationIterations = 100000;
  static const int saltLength = 32;
  static const int ivLength = 16;

  // SQLite配置
  static const int sqliteVersion = 1;
  static const String sqliteName = 'masteryos.db';
  static const bool sqliteReadOnly = false;
  static const bool sqliteSingleInstance = true;

  /// 获取缓存键的完整名称
  static String getCacheKey(String key, {String? userId}) {
    if (userId != null) {
      return 'cache_${userId}_$key';
    }
    return 'cache_$key';
  }

  /// 获取用户特定的存储键
  static String getUserKey(String key, String userId) {
    return 'user_${userId}_$key';
  }

  /// 获取临时文件路径
  static String getTempFilePath(String fileName) {
    return '$dirTemp/$fileName';
  }

  /// 获取缓存文件路径
  static String getCacheFilePath(String fileName) {
    return '$dirCache/$fileName';
  }

  /// 获取文档文件路径
  static String getDocumentFilePath(String fileName) {
    return '$dirDocuments/$fileName';
  }

  /// 获取备份文件名
  static String getBackupFileName(DateTime timestamp) {
    final formattedTime = timestamp.toIso8601String().replaceAll(':', '-');
    return 'backup_$formattedTime.zip';
  }

  /// 检查缓存是否过期
  static bool isCacheExpired(DateTime cachedTime, int expirySeconds) {
    final now = DateTime.now();
    final expiry = cachedTime.add(Duration(seconds: expirySeconds));
    return now.isAfter(expiry);
  }

  /// 获取存储空间使用情况的键名
  static const String keyStorageUsage = 'storage_usage';
  static const String keyStorageQuota = 'storage_quota';
  static const String keyStorageLastCleanup = 'storage_last_cleanup';

  /// 清理策略
  static const String cleanupStrategyLru = 'lru'; // 最近最少使用
  static const String cleanupStrategyFifo = 'fifo'; // 先进先出
  static const String cleanupStrategySize = 'size'; // 按大小
  static const String cleanupStrategyAge = 'age'; // 按年龄
}