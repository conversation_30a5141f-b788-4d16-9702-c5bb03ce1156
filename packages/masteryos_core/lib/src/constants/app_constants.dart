/// 应用常量
class AppConstants {
  AppConstants._();

  // 应用信息
  static const String appName = 'MasteryOS';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'MasteryOS - 智能技能发展平台';
  
  // 环境配置
  static const String prodEnv = 'production';
  static const String devEnv = 'development';
  static const String testEnv = 'test';
  
  // 默认配置
  static const int defaultTimeout = 30000; // 30秒
  static const int defaultRetryCount = 3;
  static const int defaultPageSize = 20;
  
  // 文件限制
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'txt', 'md'];
  
  // 用户角色
  static const String roleAdmin = 'admin';
  static const String roleUser = 'user';
  static const String roleModerator = 'moderator';
  static const String roleGuest = 'guest';
  
  // 权限
  static const String permissionRead = 'read';
  static const String permissionWrite = 'write';
  static const String permissionDelete = 'delete';
  static const String permissionManage = 'manage';
  
  // 状态
  static const String statusActive = 'active';
  static const String statusInactive = 'inactive';
  static const String statusPending = 'pending';
  static const String statusDeleted = 'deleted';
  static const String statusBlocked = 'blocked';
  
  // 优先级
  static const String priorityLow = 'low';
  static const String priorityMedium = 'medium';
  static const String priorityHigh = 'high';
  static const String priorityUrgent = 'urgent';
  
  // 主题
  static const String themeLight = 'light';
  static const String themeDark = 'dark';
  static const String themeSystem = 'system';
  
  // 语言
  static const String languageZh = 'zh';
  static const String languageEn = 'en';
  static const String languageZhCN = 'zh_CN';
  static const String languageEnUS = 'en_US';
  
  // 日期格式
  static const String dateFormatYMD = 'yyyy-MM-dd';
  static const String dateFormatYMDHMS = 'yyyy-MM-dd HH:mm:ss';
  static const String dateFormatMD = 'MM-dd';
  static const String dateFormatHM = 'HH:mm';
  
  // 正则表达式
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^1[3-9]\d{9}$';
  static const String passwordRegex = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  
  // 数据库
  static const String dbName = 'masteryos.db';
  static const int dbVersion = 1;
  
  // 缓存
  static const String cachePrefix = 'masteryos_';
  static const Duration defaultCacheDuration = Duration(hours: 1);
  static const Duration shortCacheDuration = Duration(minutes: 15);
  static const Duration longCacheDuration = Duration(days: 1);
  
  // 网络
  static const int connectionTimeout = 15000;
  static const int receiveTimeout = 15000;
  static const int sendTimeout = 15000;
  
  // 分页
  static const int minPageSize = 5;
  static const int maxPageSize = 100;
  static const int defaultPage = 1;
  
  // 验证
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 20;
  
  // 限制
  static const int maxUploadFiles = 10;
  static const int maxTextLength = 1000;
  static const int maxTitleLength = 100;
  static const int maxDescriptionLength = 500;
  
  // 动画
  static const Duration shortAnimation = Duration(milliseconds: 150);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
}