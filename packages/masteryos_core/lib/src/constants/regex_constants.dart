/// 正则表达式常量
class RegexConstants {
  RegexConstants._();

  // 基础验证
  static final RegExp email = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
  static final RegExp phone = RegExp(r'^1[3-9]\d{9}$');
  static final RegExp numeric = RegExp(r'^\d+$');
  static final RegExp alpha = RegExp(r'^[a-zA-Z]+$');
  static final RegExp alphanumeric = RegExp(r'^[a-zA-Z0-9]+$');
  static final RegExp whitespace = RegExp(r'\s+');

  // 中文相关
  static final RegExp chinese = RegExp(r'[\u4e00-\u9fa5]');
  static final RegExp chineseOnly = RegExp(r'^[\u4e00-\u9fa5]+$');
  static final RegExp chineseName = RegExp(r'^[\u4e00-\u9fa5]{2,8}$');

  // 密码验证
  static final RegExp passwordWeak = RegExp(r'^.{6,}$');
  static final RegExp passwordMedium = RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$');
  static final RegExp passwordStrong = RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#\$%^&*(),.?\":{}|<>]).{8,}$');
  static final RegExp passwordSpecialChars = RegExp(r'[!@#\$%^&*(),.?\":{}|<>]');

  // 用户名和昵称
  static final RegExp username = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]{3,19}$');
  static final RegExp nickname = RegExp(r'^[a-zA-Z0-9\u4e00-\u9fa5_]{2,20}$');

  // 身份验证
  static final RegExp idCard = RegExp(r'^\d{17}[0-9Xx]$');
  static final RegExp passport = RegExp(r'^[A-Z][0-9]{8}$');

  // 银行卡和支付
  static final RegExp bankCard = RegExp(r'^\d{16,19}$');
  static final RegExp creditCard = RegExp(r'^\d{13,19}$');

  // 社交平台
  static final RegExp qq = RegExp(r'^[1-9]\d{4,10}$');
  static final RegExp wechat = RegExp(r'^[a-zA-Z][a-zA-Z0-9_-]{5,19}$');

  // URL和网络
  static final RegExp url = RegExp(r'^https?://[^\s/\$\.?#].[^\s]*$', caseSensitive: false);
  static final RegExp domain = RegExp(r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$');
  static final RegExp ipv4 = RegExp(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$');
  static final RegExp ipv6 = RegExp(r'^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$');
  static final RegExp macAddress = RegExp(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$');
  static final RegExp port = RegExp(r'^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$');

  // 颜色
  static final RegExp hexColor = RegExp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$');
  static final RegExp rgbColor = RegExp(r'^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$');
  static final RegExp rgbaColor = RegExp(r'^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*(0|1|0?\.\d+)\)$');

  // 地理位置
  static final RegExp longitude = RegExp(r'^-?(?:180(?:\.0+)?|(?:1[0-7]\d|[1-9]?\d)(?:\.\d+)?)$');
  static final RegExp latitude = RegExp(r'^-?(?:90(?:\.0+)?|(?:[1-8]?\d)(?:\.\d+)?)$');
  static final RegExp postalCode = RegExp(r'^\d{6}$');

  // 车牌号
  static final RegExp licensePlate = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$');
  static final RegExp newEnergyLicensePlate = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z][A-Z0-9DF][0-9]{4}[A-Z0-9]$');

  // 版本号
  static final RegExp semver = RegExp(r'^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?(\+[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?$');
  static final RegExp version = RegExp(r'^\d+(\.\d+)*$');

  // 日期时间
  static final RegExp dateYMD = RegExp(r'^\d{4}-\d{2}-\d{2}$');
  static final RegExp dateMDY = RegExp(r'^\d{2}/\d{2}/\d{4}$');
  static final RegExp dateDMY = RegExp(r'^\d{2}\.\d{2}\.\d{4}$');
  static final RegExp timeHM = RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$');
  static final RegExp timeHMS = RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$');
  static final RegExp iso8601 = RegExp(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$');

  // 文件和路径
  static final RegExp fileName = RegExp(r'^[^<>:"/\\|?*]+$');
  static final RegExp fileExtension = RegExp(r'\.[a-zA-Z0-9]+$');
  static final RegExp imagePath = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp)$', caseSensitive: false);
  static final RegExp videoPath = RegExp(r'\.(mp4|avi|mkv|mov|wmv|flv)$', caseSensitive: false);
  static final RegExp audioPath = RegExp(r'\.(mp3|wav|flac|aac|ogg|wma)$', caseSensitive: false);

  // HTML和标记
  static final RegExp htmlTag = RegExp(r'<[^>]*>');
  static final RegExp htmlEntity = RegExp(r'&[a-zA-Z0-9#]+;');
  static final RegExp markdown = RegExp(r'[*_`~\[\]()#+-]');

  // 数字格式
  static final RegExp integer = RegExp(r'^-?\d+$');
  static final RegExp positiveInteger = RegExp(r'^\d+$');
  static final RegExp decimal = RegExp(r'^-?\d+(\.\d+)?$');
  static final RegExp currency = RegExp(r'^\d+(\.\d{2})?$');
  static final RegExp percentage = RegExp(r'^\d{1,3}(\.\d+)?%$');

  // 编程相关
  static final RegExp variableName = RegExp(r'^[a-zA-Z_][a-zA-Z0-9_]*$');
  static final RegExp jsonString = RegExp(r'^[\{\[].*[\}\]]$');
  static final RegExp base64 = RegExp(r'^[A-Za-z0-9+/]*={0,2}$');
  static final RegExp uuid = RegExp(r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$', caseSensitive: false);

  // 商业相关
  static final RegExp taxNumber = RegExp(r'^[0-9A-Z]{15,20}$');
  static final RegExp organizationCode = RegExp(r'^[A-Z0-9]{8}-[A-Z0-9]$');
  static final RegExp socialCreditCode = RegExp(r'^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$');

  // 学术相关
  static final RegExp isbn10 = RegExp(r'^[0-9]{9}[0-9X]$');
  static final RegExp isbn13 = RegExp(r'^\d{13}$');
  static final RegExp doi = RegExp(r'^10\.\d{4,9}/[-._;()/:\w\[\]]+$');

  /// 验证邮箱格式
  static bool isValidEmail(String value) => email.hasMatch(value);

  /// 验证手机号格式
  static bool isValidPhone(String value) => phone.hasMatch(value);

  /// 验证URL格式
  static bool isValidUrl(String value) => url.hasMatch(value);

  /// 验证身份证号格式
  static bool isValidIdCard(String value) => idCard.hasMatch(value);

  /// 验证用户名格式
  static bool isValidUsername(String value) => username.hasMatch(value);

  /// 检查密码强度
  static PasswordStrength getPasswordStrength(String password) {
    if (passwordStrong.hasMatch(password)) {
      return PasswordStrength.strong;
    } else if (passwordMedium.hasMatch(password)) {
      return PasswordStrength.medium;
    } else if (passwordWeak.hasMatch(password)) {
      return PasswordStrength.weak;
    } else {
      return PasswordStrength.veryWeak;
    }
  }

  /// 提取文本中的邮箱地址
  static List<String> extractEmails(String text) {
    return email.allMatches(text).map((match) => match.group(0)!).toList();
  }

  /// 提取文本中的URL
  static List<String> extractUrls(String text) {
    return url.allMatches(text).map((match) => match.group(0)!).toList();
  }

  /// 提取文本中的手机号
  static List<String> extractPhones(String text) {
    return phone.allMatches(text).map((match) => match.group(0)!).toList();
  }

  /// 清理HTML标签
  static String removeHtmlTags(String html) {
    return html.replaceAll(htmlTag, '');
  }

  /// 转义HTML实体
  static String escapeHtml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }
}

/// 密码强度枚举
enum PasswordStrength {
  veryWeak,
  weak,
  medium,
  strong,
}

/// 密码强度扩展
extension PasswordStrengthExtension on PasswordStrength {
  String get description {
    switch (this) {
      case PasswordStrength.veryWeak:
        return '非常弱';
      case PasswordStrength.weak:
        return '弱';
      case PasswordStrength.medium:
        return '中等';
      case PasswordStrength.strong:
        return '强';
    }
  }

  double get score {
    switch (this) {
      case PasswordStrength.veryWeak:
        return 0.2;
      case PasswordStrength.weak:
        return 0.4;
      case PasswordStrength.medium:
        return 0.7;
      case PasswordStrength.strong:
        return 1.0;
    }
  }
}