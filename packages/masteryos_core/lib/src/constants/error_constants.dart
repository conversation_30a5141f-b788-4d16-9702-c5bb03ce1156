/// 错误相关常量
class ErrorConstants {
  ErrorConstants._();

  // 通用错误
  static const String unknownError = 'UNKNOWN_ERROR';
  static const String internalError = 'INTERNAL_ERROR';
  static const String serviceUnavailable = 'SERVICE_UNAVAILABLE';
  static const String maintenanceMode = 'MAINTENANCE_MODE';

  // 网络错误
  static const String networkError = 'NETWORK_ERROR';
  static const String connectionTimeout = 'CONNECTION_TIMEOUT';
  static const String requestTimeout = 'REQUEST_TIMEOUT';
  static const String noInternetConnection = 'NO_INTERNET_CONNECTION';
  static const String dnsFailed = 'DNS_FAILED';
  static const String sslError = 'SSL_ERROR';

  // 认证错误
  static const String invalidCredentials = 'INVALID_CREDENTIALS';
  static const String tokenExpired = 'TOKEN_EXPIRED';
  static const String tokenInvalid = 'TOKEN_INVALID';
  static const String tokenMissing = 'TOKEN_MISSING';
  static const String refreshTokenExpired = 'REFRESH_TOKEN_EXPIRED';
  static const String sessionExpired = 'SESSION_EXPIRED';
  static const String accountLocked = 'ACCOUNT_LOCKED';
  static const String accountSuspended = 'ACCOUNT_SUSPENDED';
  static const String accountDeleted = 'ACCOUNT_DELETED';
  static const String loginFailed = 'LOGIN_FAILED';
  static const String logoutFailed = 'LOGOUT_FAILED';

  // 授权错误
  static const String permissionDenied = 'PERMISSION_DENIED';
  static const String accessDenied = 'ACCESS_DENIED';
  static const String insufficientPermissions = 'INSUFFICIENT_PERMISSIONS';
  static const String roleRequired = 'ROLE_REQUIRED';
  static const String adminRequired = 'ADMIN_REQUIRED';

  // 验证错误
  static const String validationFailed = 'VALIDATION_FAILED';
  static const String requiredField = 'REQUIRED_FIELD';
  static const String invalidFormat = 'INVALID_FORMAT';
  static const String invalidLength = 'INVALID_LENGTH';
  static const String invalidRange = 'INVALID_RANGE';
  static const String invalidEmail = 'INVALID_EMAIL';
  static const String invalidPhone = 'INVALID_PHONE';
  static const String invalidPassword = 'INVALID_PASSWORD';
  static const String passwordTooWeak = 'PASSWORD_TOO_WEAK';
  static const String passwordMismatch = 'PASSWORD_MISMATCH';

  // 资源错误
  static const String resourceNotFound = 'RESOURCE_NOT_FOUND';
  static const String resourceAlreadyExists = 'RESOURCE_ALREADY_EXISTS';
  static const String resourceInUse = 'RESOURCE_IN_USE';
  static const String resourceDeleted = 'RESOURCE_DELETED';
  static const String resourceUnavailable = 'RESOURCE_UNAVAILABLE';

  // 用户错误
  static const String userNotFound = 'USER_NOT_FOUND';
  static const String userAlreadyExists = 'USER_ALREADY_EXISTS';
  static const String usernameTaken = 'USERNAME_TAKEN';
  static const String emailTaken = 'EMAIL_TAKEN';
  static const String phoneNumberTaken = 'PHONE_NUMBER_TAKEN';
  static const String userProfileIncomplete = 'USER_PROFILE_INCOMPLETE';

  // 文件错误
  static const String fileNotFound = 'FILE_NOT_FOUND';
  static const String fileTooLarge = 'FILE_TOO_LARGE';
  static const String fileTypeNotSupported = 'FILE_TYPE_NOT_SUPPORTED';
  static const String fileUploadFailed = 'FILE_UPLOAD_FAILED';
  static const String fileDownloadFailed = 'FILE_DOWNLOAD_FAILED';
  static const String fileCorrupted = 'FILE_CORRUPTED';
  static const String storageQuotaExceeded = 'STORAGE_QUOTA_EXCEEDED';
  static const String diskSpaceFull = 'DISK_SPACE_FULL';

  // 数据库错误
  static const String databaseError = 'DATABASE_ERROR';
  static const String databaseConnectionFailed = 'DATABASE_CONNECTION_FAILED';
  static const String databaseTimeout = 'DATABASE_TIMEOUT';
  static const String databaseConstraintViolation = 'DATABASE_CONSTRAINT_VIOLATION';
  static const String duplicateEntry = 'DUPLICATE_ENTRY';
  static const String foreignKeyConstraint = 'FOREIGN_KEY_CONSTRAINT';
  static const String dataIntegrityViolation = 'DATA_INTEGRITY_VIOLATION';

  // 业务逻辑错误
  static const String businessRuleViolation = 'BUSINESS_RULE_VIOLATION';
  static const String operationNotAllowed = 'OPERATION_NOT_ALLOWED';
  static const String workflowError = 'WORKFLOW_ERROR';
  static const String stateTransitionError = 'STATE_TRANSITION_ERROR';
  static const String quotaExceeded = 'QUOTA_EXCEEDED';
  static const String limitExceeded = 'LIMIT_EXCEEDED';

  // 支付错误
  static const String paymentFailed = 'PAYMENT_FAILED';
  static const String paymentDeclined = 'PAYMENT_DECLINED';
  static const String insufficientFunds = 'INSUFFICIENT_FUNDS';
  static const String paymentMethodInvalid = 'PAYMENT_METHOD_INVALID';
  static const String subscriptionExpired = 'SUBSCRIPTION_EXPIRED';
  static const String subscriptionCancelled = 'SUBSCRIPTION_CANCELLED';

  // 第三方服务错误
  static const String thirdPartyServiceError = 'THIRD_PARTY_SERVICE_ERROR';
  static const String apiKeyInvalid = 'API_KEY_INVALID';
  static const String rateLimitExceeded = 'RATE_LIMIT_EXCEEDED';
  static const String serviceTemporarilyUnavailable = 'SERVICE_TEMPORARILY_UNAVAILABLE';

  // 缓存错误
  static const String cacheError = 'CACHE_ERROR';
  static const String cacheExpired = 'CACHE_EXPIRED';
  static const String cacheCorrupted = 'CACHE_CORRUPTED';
  static const String cacheFull = 'CACHE_FULL';

  // 同步错误
  static const String syncFailed = 'SYNC_FAILED';
  static const String syncConflict = 'SYNC_CONFLICT';
  static const String syncTimeout = 'SYNC_TIMEOUT';
  static const String syncNotSupported = 'SYNC_NOT_SUPPORTED';

  // 配置错误
  static const String configurationError = 'CONFIGURATION_ERROR';
  static const String missingConfiguration = 'MISSING_CONFIGURATION';
  static const String invalidConfiguration = 'INVALID_CONFIGURATION';
  static const String configurationOutdated = 'CONFIGURATION_OUTDATED';

  /// 错误消息映射
  static const Map<String, String> errorMessages = {
    unknownError: '未知错误',
    internalError: '内部服务器错误',
    networkError: '网络连接错误',
    connectionTimeout: '连接超时',
    invalidCredentials: '用户名或密码错误',
    tokenExpired: '登录已过期，请重新登录',
    permissionDenied: '权限不足',
    resourceNotFound: '资源不存在',
    validationFailed: '数据验证失败',
    fileUploadFailed: '文件上传失败',
    // ... 可以添加更多映射
  };

  /// 获取错误消息
  static String getMessage(String errorCode, {String? defaultMessage}) {
    return errorMessages[errorCode] ?? defaultMessage ?? '未知错误';
  }

  /// 检查是否为网络相关错误
  static bool isNetworkError(String errorCode) {
    return [
      networkError,
      connectionTimeout,
      requestTimeout,
      noInternetConnection,
      dnsFailed,
      sslError,
    ].contains(errorCode);
  }

  /// 检查是否为认证相关错误
  static bool isAuthError(String errorCode) {
    return [
      invalidCredentials,
      tokenExpired,
      tokenInvalid,
      tokenMissing,
      refreshTokenExpired,
      sessionExpired,
      accountLocked,
      accountSuspended,
      loginFailed,
    ].contains(errorCode);
  }

  /// 检查是否为权限相关错误
  static bool isPermissionError(String errorCode) {
    return [
      permissionDenied,
      accessDenied,
      insufficientPermissions,
      roleRequired,
      adminRequired,
    ].contains(errorCode);
  }

  /// 检查是否为验证相关错误
  static bool isValidationError(String errorCode) {
    return [
      validationFailed,
      requiredField,
      invalidFormat,
      invalidLength,
      invalidRange,
      invalidEmail,
      invalidPhone,
      invalidPassword,
    ].contains(errorCode);
  }

  /// 检查是否为可重试错误
  static bool isRetriableError(String errorCode) {
    return [
      networkError,
      connectionTimeout,
      requestTimeout,
      serviceUnavailable,
      databaseTimeout,
      rateLimitExceeded,
    ].contains(errorCode);
  }
}