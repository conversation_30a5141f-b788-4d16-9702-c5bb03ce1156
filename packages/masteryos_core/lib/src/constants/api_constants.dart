/// API相关常量
class ApiConstants {
  ApiConstants._();

  // 基础URL
  static const String baseUrl = 'http://localhost:3000';
  static const String apiVersion = 'v1';
  static const String apiPrefix = '/api/$apiVersion';

  // 认证相关
  static const String authLogin = '$apiPrefix/auth/login';
  static const String authLogout = '$apiPrefix/auth/logout';
  static const String authRefresh = '$apiPrefix/auth/refresh';
  static const String authProfile = '$apiPrefix/auth/profile';
  static const String authRegister = '$apiPrefix/auth/register';
  static const String authVerify = '$apiPrefix/auth/verify';
  static const String authForgotPassword = '$apiPrefix/auth/forgot-password';
  static const String authResetPassword = '$apiPrefix/auth/reset-password';

  // 用户相关
  static const String users = '$apiPrefix/users';
  static const String userProfile = '$apiPrefix/users/profile';
  static const String userSettings = '$apiPrefix/users/settings';
  static const String userAvatar = '$apiPrefix/users/avatar';

  // 文档相关
  static const String documents = '$apiPrefix/documents';
  static const String documentUpload = '$apiPrefix/documents/upload';
  static const String documentDownload = '$apiPrefix/documents/download';
  static const String documentShare = '$apiPrefix/documents/share';

  // 技能相关
  static const String skills = '$apiPrefix/skills';
  static const String skillCategories = '$apiPrefix/skills/categories';
  static const String skillAssessment = '$apiPrefix/skills/assessment';
  static const String skillProgress = '$apiPrefix/skills/progress';

  // 学习相关
  static const String learningPaths = '$apiPrefix/learning-paths';
  static const String learningProgress = '$apiPrefix/learning/progress';
  static const String learningResources = '$apiPrefix/learning/resources';

  // 分析相关
  static const String analytics = '$apiPrefix/analytics';
  static const String analyticsStats = '$apiPrefix/analytics/stats';
  static const String analyticsReports = '$apiPrefix/analytics/reports';

  // AI相关
  static const String aiChat = '$apiPrefix/ai/chat';
  static const String aiAnalyze = '$apiPrefix/ai/analyze';
  static const String aiSummarize = '$apiPrefix/ai/summarize';
  static const String aiSearch = '$apiPrefix/ai/search';

  // 系统相关
  static const String health = '$apiPrefix/health';
  static const String version = '$apiPrefix/version';
  static const String config = '$apiPrefix/config';

  // HTTP状态码
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusNoContent = 204;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusConflict = 409;
  static const int statusValidationError = 422;
  static const int statusInternalServerError = 500;

  // HTTP方法
  static const String methodGet = 'GET';
  static const String methodPost = 'POST';
  static const String methodPut = 'PUT';
  static const String methodPatch = 'PATCH';
  static const String methodDelete = 'DELETE';

  // 请求头
  static const String headerAuthorization = 'Authorization';
  static const String headerContentType = 'Content-Type';
  static const String headerAccept = 'Accept';
  static const String headerUserAgent = 'User-Agent';
  static const String headerXRequestId = 'X-Request-ID';

  // 内容类型
  static const String contentTypeJson = 'application/json';
  static const String contentTypeFormData = 'multipart/form-data';
  static const String contentTypeFormUrlEncoded = 'application/x-www-form-urlencoded';

  // 查询参数
  static const String paramPage = 'page';
  static const String paramLimit = 'limit';
  static const String paramSort = 'sort';
  static const String paramOrder = 'order';
  static const String paramSearch = 'search';
  static const String paramFilter = 'filter';

  // 排序方向
  static const String orderAsc = 'asc';
  static const String orderDesc = 'desc';

  // 响应字段
  static const String fieldData = 'data';
  static const String fieldMessage = 'message';
  static const String fieldError = 'error';
  static const String fieldCode = 'code';
  static const String fieldSuccess = 'success';
  static const String fieldTotal = 'total';
  static const String fieldPage = 'page';
  static const String fieldLimit = 'limit';

  // 错误码
  static const String errorInvalidCredentials = 'INVALID_CREDENTIALS';
  static const String errorTokenExpired = 'TOKEN_EXPIRED';
  static const String errorInvalidToken = 'INVALID_TOKEN';
  static const String errorPermissionDenied = 'PERMISSION_DENIED';
  static const String errorResourceNotFound = 'RESOURCE_NOT_FOUND';
  static const String errorValidationFailed = 'VALIDATION_FAILED';
  static const String errorDuplicateEntry = 'DUPLICATE_ENTRY';
  static const String errorInternalError = 'INTERNAL_ERROR';
  static const String errorNetworkError = 'NETWORK_ERROR';
  static const String errorTimeout = 'TIMEOUT';

  // 文件上传
  static const String fieldFile = 'file';
  static const String fieldFiles = 'files';
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedFileTypes = [
    'jpg', 'jpeg', 'png', 'gif', 'webp',
    'pdf', 'doc', 'docx', 'txt', 'md'
  ];

  // WebSocket
  static const String wsPath = '/ws';
  static const String wsReconnectDelay = '5000';

  // 环境配置
  static const Map<String, String> developmentConfig = {
    'baseUrl': 'http://localhost:3000',
    'wsUrl': 'ws://localhost:3000/ws',
  };

  static const Map<String, String> productionConfig = {
    'baseUrl': 'https://api.masteryos.com',
    'wsUrl': 'wss://api.masteryos.com/ws',
  };

  /// 根据环境获取配置
  static Map<String, String> getConfig(String environment) {
    switch (environment) {
      case 'production':
        return productionConfig;
      case 'development':
      default:
        return developmentConfig;
    }
  }

  /// 构建完整的API URL
  static String buildUrl(String endpoint, {Map<String, String>? queryParams}) {
    var url = baseUrl + endpoint;
    
    if (queryParams != null && queryParams.isNotEmpty) {
      final query = queryParams.entries
          .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
          .join('&');
      url += '?$query';
    }
    
    return url;
  }

  /// 检查是否为成功状态码
  static bool isSuccessStatusCode(int statusCode) {
    return statusCode >= 200 && statusCode < 300;
  }

  /// 检查是否为客户端错误状态码
  static bool isClientErrorStatusCode(int statusCode) {
    return statusCode >= 400 && statusCode < 500;
  }

  /// 检查是否为服务器错误状态码
  static bool isServerErrorStatusCode(int statusCode) {
    return statusCode >= 500 && statusCode < 600;
  }
}