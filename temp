
  # 启动基础服务（数据库 + API）
  pnpm run dev

  # 启动 Flutter Web 管理后台（新的管理界面）
  ./scripts/flutter-admin-web-start.sh


 MasteryOS 开发环境测试完成




  ✅ 测试结果

  所有服务启动成功：

  1. 数据库服务 ✅
    - PostgreSQL: 端口 8182 (健康运行)
    - Redis: 端口 8183 (健康运行)
  2. 后端 API 服务 ✅
    - Mobile BFF: 端口 3101 (NestJS)
    - Admin BFF: 端口 3102 (NestJS)
    - 数据库连接正常，TypeORM 初始化成功
  3. 前端服务 ✅
    - Admin SPA: 端口 3100 (React + Vite)
    - Flutter Web: 端口 8080 (Flutter Web Server)

  🚀 一键启动脚本

  创建了完整的启动和管理脚本：

  # 一键启动所有服务
  pnpm run dev
  # 或者
  ./scripts/start-all-services.sh

  # 停止所有服务
  pnpm run dev:stop

  # 查看服务状态
  pnpm run dev:status

  # 数据库管理
  pnpm run db:start|stop|status

  🌐 服务访问地址

  - Flutter Web: http://localhost:8080
  - Admin SPA: http://localhost:3100
  - Mobile BFF API: http://localhost:3101
  - Admin BFF API: http://localhost:3102
  - API 文档:
    - Mobile BFF: http://localhost:3101/docs
    - Admin BFF: http://localhost:3102/docs

  🔧 已解决的问题

  1. 端口冲突: 修正 Admin SPA 从 3000 改为 3100 端口
  2. 数据库连接: 创建了 .env 配置文件，配置正确的数据库连接
  3. 环境隔离: 所有服务使用独立的 1w 项目容器组
  4. 一键管理: 提供完整的启动、停止、状态检查脚本

  💡 使用建议

  开发时可以使用以下命令：
  - pnpm run dev - 一键启动所有服务
  - pnpm run dev:stop - 停止所有服务
  - pnpm run dev:status - 查看服务状态

  项目现在拥有完整且独立的开发环境，可以开始核心功能开发！

