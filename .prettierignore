# 依赖目录
node_modules/
**/node_modules/

# 构建输出
dist/
build/
.next/
out/
coverage/

# Flutter
apps/mobile/.dart_tool/
apps/mobile/build/
apps/mobile/.flutter-plugins
apps/mobile/.flutter-plugins-dependencies

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 环境变量
.env
.env.local
.env.production

# 缓存和临时文件
.cache
.temp
.tmp
*.swp
*.swo
*~

# IDE 配置
.vscode/
.idea/
*.iml

# 系统文件
.DS_Store
Thumbs.db

# 包管理器
pnpm-lock.yaml
package-lock.json
yarn.lock

# 文档构建
docs/.vuepress/dist/
docs/.vuepress/.temp/
docs/.vuepress/.cache/

# 其他
*.min.js
*.min.css
vendor/