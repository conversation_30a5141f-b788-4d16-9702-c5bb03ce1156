# MasteryOS 部署指南

本文档提供 MasteryOS 系统的完整部署指南，包括开发环境和生产环境的部署方式。

## 快速开始

### 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd masteryos

# 快速启动开发环境
./scripts/quick-start.sh start dev

# 或者使用传统方式
./scripts/dev-start.sh
```

### 生产环境

```bash
# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置

# 快速部署生产环境
./scripts/quick-start.sh start prod

# 或者使用专用部署脚本
./scripts/deploy.sh
```

## 环境要求

### 基础要求

- Docker 20.0+
- Docker Compose 2.0+
- Node.js 18+ (开发环境)
- Flutter 3.24.5+ (开发环境)
- pnpm 8+ (开发环境)

### 系统要求

- **内存**: 最少 4GB RAM，推荐 8GB+
- **存储**: 最少 10GB 可用空间
- **CPU**: 2 核心以上
- **网络**: 稳定的互联网连接

## 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   Nginx Proxy   │────│ Flutter Web     │    │   NestJS BFF    │
│   (Port 80/443) │    │   (Port 3200)   │────│   (Port 3102)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                               ┌────────────────────────┼────────────────────────┐
                               │                        │                        │
                    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
                    │                 │    │                 │    │                 │
                    │   PostgreSQL    │    │      Redis      │    │     MinIO       │
                    │   (Port 5432)   │    │   (Port 6379)   │    │  (Port 9000/1)  │
                    │                 │    │                 │    │                 │
                    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 部署方式

### 1. 开发环境部署

开发环境使用混合部署模式：数据库服务容器化，应用服务本地运行。

```bash
# 方式一：使用快速启动脚本
./scripts/quick-start.sh start dev

# 方式二：手动启动
# 1. 启动数据库服务
docker-compose -f infrastructure/docker/docker-compose.database-only.yml up -d

# 2. 启动 Admin BFF
cd apps/admin-bff
pnpm install
pnpm run start:dev

# 3. 启动 Admin Web（新终端）
cd apps/admin-web
flutter pub get
flutter pub run build_runner build --delete-conflicting-outputs
flutter run -d web-server --web-hostname=0.0.0.0 --web-port=3200
```

**开发环境访问地址：**
- Admin Web: http://localhost:3200
- Admin BFF API: http://localhost:3102
- API 文档: http://localhost:3102/api/docs
- PostgreSQL: localhost:5432
- Redis: localhost:6379
- MinIO: http://localhost:9000 (控制台: http://localhost:9001)

### 2. 生产环境部署

生产环境完全容器化部署，包含 Nginx 反向代理和 SSL 支持。

#### 2.1 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（重要配置项）
vim .env
```

**关键环境变量：**
```bash
# 数据库密码（必须修改）
DB_PASSWORD=your-secure-password

# JWT 密钥（必须修改）
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# MinIO 访问密钥（建议修改）
MINIO_ACCESS_KEY=your-access-key
MINIO_SECRET_KEY=your-secret-key

# 生产环境 URL
PRODUCTION_URL=https://your-domain.com
```

#### 2.2 SSL 证书配置

**方式一：自动生成自签名证书（开发/测试）**
```bash
./scripts/deploy.sh  # 自动生成
```

**方式二：使用真实 SSL 证书（生产环境推荐）**
```bash
# 将证书文件放置到指定位置
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem
```

#### 2.3 部署执行

```bash
# 方式一：使用快速启动脚本
./scripts/quick-start.sh start prod

# 方式二：使用专用部署脚本（推荐）
./scripts/deploy.sh

# 方式三：手动部署
docker-compose -f docker-compose.prod.yml up -d
```

**生产环境访问地址：**
- 主应用: http://localhost 或 https://localhost (SSL)
- API 文档: http://localhost/api/docs
- MinIO 控制台: http://localhost:9001

## 服务管理

### 常用命令

```bash
# 启动服务
./scripts/quick-start.sh start [dev|prod]

# 停止服务
./scripts/quick-start.sh stop [dev|prod]

# 重启服务
./scripts/quick-start.sh restart [dev|prod]

# 查看状态
./scripts/quick-start.sh status [dev|prod]

# 查看日志
./scripts/quick-start.sh logs [dev|prod] [service]

# 清理数据（危险操作）
./scripts/quick-start.sh clean [dev|prod]
```

### 服务状态检查

```bash
# 检查所有服务状态
docker-compose -f docker-compose.prod.yml ps

# 检查健康状态
curl http://localhost/health
curl http://localhost:3102/health

# 查看服务日志
docker-compose -f docker-compose.prod.yml logs -f [service-name]
```

## 数据管理

### 数据库初始化

系统首次启动时会自动执行数据库初始化脚本 `scripts/init.sql`。

**默认账户：**
- 管理员: `admin` / `admin123`
- 经理: `manager` / `manager123`
- 普通用户: `user` / `user123`

### 数据备份

```bash
# 备份数据库
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U masteryos masteryos > backup.sql

# 备份 MinIO 数据
docker-compose -f docker-compose.prod.yml exec minio mc mirror /data /backup

# 恢复数据库
docker-compose -f docker-compose.prod.yml exec -T postgres psql -U masteryos masteryos < backup.sql
```

### 数据迁移

```bash
# 运行数据库迁移（如果有）
docker-compose -f docker-compose.prod.yml exec admin-bff npm run migration:run

# 回滚迁移
docker-compose -f docker-compose.prod.yml exec admin-bff npm run migration:revert
```

## 监控和日志

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f admin-bff
docker-compose -f docker-compose.prod.yml logs -f admin-web
docker-compose -f docker-compose.prod.yml logs -f postgres

# 查看最近的日志
docker-compose -f docker-compose.prod.yml logs --tail=100 -f
```

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
df -h
free -h
```

### 健康检查

所有服务都配置了健康检查：
- **PostgreSQL**: `pg_isready`
- **Redis**: `redis-cli ping`
- **MinIO**: HTTP 健康检查
- **Admin BFF**: `/health` 端点
- **Admin Web**: HTTP 响应检查
- **Nginx**: HTTP 响应检查

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3102
   lsof -i :3200
   lsof -i :5432
   
   # 修改端口配置
   vim docker-compose.prod.yml
   ```

2. **内存不足**
   ```bash
   # 查看内存使用
   free -h
   docker stats
   
   # 调整容器内存限制
   vim docker-compose.prod.yml
   ```

3. **磁盘空间不足**
   ```bash
   # 清理 Docker 缓存
   docker system prune -a
   
   # 清理未使用的卷
   docker volume prune
   ```

4. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U masteryos
   
   # 查看数据库日志
   docker-compose -f docker-compose.prod.yml logs postgres
   ```

### 日志分析

```bash
# 错误日志筛选
docker-compose -f docker-compose.prod.yml logs | grep -i error

# API 访问日志
docker-compose -f docker-compose.prod.yml logs nginx | grep -i api

# 数据库连接日志
docker-compose -f docker-compose.prod.yml logs admin-bff | grep -i database
```

## 性能优化

### 数据库优化

1. **索引优化**
   - 所有查询字段都已建立索引
   - 定期分析查询性能

2. **连接池配置**
   ```bash
   # 调整数据库连接池大小
   # 在 admin-bff 的环境变量中设置
   DB_POOL_SIZE=20
   ```

### 缓存优化

1. **Redis 缓存**
   - API 响应缓存
   - 会话数据缓存
   - 查询结果缓存

2. **Nginx 静态资源缓存**
   - 1年缓存期
   - Gzip 压缩
   - 浏览器缓存优化

### 资源限制

生产环境容器资源限制：
- **Admin BFF**: 512MB 内存限制
- **Admin Web**: 128MB 内存限制
- **数据库**: 根据需要调整
- **Redis**: 256MB 内存限制

## 安全配置

### 网络安全

1. **防火墙配置**
   ```bash
   # 只开放必要端口
   ufw allow 80/tcp
   ufw allow 443/tcp
   ufw enable
   ```

2. **SSL/TLS 配置**
   - 使用 TLS 1.2+ 协议
   - 强加密套件
   - HSTS 头部

### 应用安全

1. **环境变量安全**
   - 所有敏感信息通过环境变量配置
   - 不在代码中硬编码密钥

2. **API 安全**
   - JWT 认证
   - 请求频率限制
   - CORS 策略

## 扩展性

### 水平扩展

1. **负载均衡**
   ```bash
   # 可以部署多个 admin-bff 实例
   # Nginx 自动负载均衡
   ```

2. **数据库读写分离**
   ```bash
   # 可配置主从数据库
   # 读写分离提升性能
   ```

### 服务拆分

系统设计支持逐步拆分为微服务：
- 用户服务
- 文档服务
- 分析服务
- 认证服务

## 联系支持

如遇到部署问题，请：

1. 查看相关日志文件
2. 检查系统资源使用情况
3. 参考故障排除章节
4. 提交 Issue 并附上详细日志

---

**注意**: 在生产环境中，请务必：
- 修改所有默认密码
- 配置真实的 SSL 证书
- 设置适当的备份策略
- 配置监控和告警系统