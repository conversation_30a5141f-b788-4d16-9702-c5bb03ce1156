# MasteryOS 环境变量配置模板
# 复制此文件为 .env 并根据实际情况修改

# ========================================
# 基础配置
# ========================================
NODE_ENV=development
PORT=3000
API_VERSION=v1

# ========================================
# 数据库配置
# ========================================
# PostgreSQL 主数据库
DATABASE_URL=postgresql://masteryos:masteryos123@localhost:8182/masteryos
DB_HOST=localhost
DB_PORT=8182
DB_NAME=masteryos
DB_USER=masteryos
DB_PASSWORD=masteryos123
DB_SSL=false

# Redis 缓存
REDIS_URL=redis://localhost:8183
REDIS_HOST=localhost
REDIS_PORT=8183
REDIS_PASSWORD=
REDIS_DB=0

# ========================================
# API 服务配置
# ========================================
# Mobile BFF
MOBILE_BFF_HOST=localhost
MOBILE_BFF_PORT=3101
MOBILE_BFF_API_PREFIX=/api/mobile

# Admin BFF
ADMIN_BFF_HOST=localhost
ADMIN_BFF_PORT=3102
ADMIN_BFF_API_PREFIX=/api/admin

# Admin SPA
ADMIN_SPA_HOST=localhost
ADMIN_SPA_PORT=3100

# ========================================
# JWT 认证配置
# ========================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-secret-key-change-in-production
JWT_REFRESH_EXPIRES_IN=30d

# ========================================
# 文件存储配置（MinIO - 可选）
# ========================================
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=masteryos
MINIO_SECRET_KEY=masteryos123
MINIO_USE_SSL=false
MINIO_BUCKET_NAME=masteryos-files

# ========================================
# Elasticsearch 配置（可选）
# ========================================
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX=masteryos

# ========================================
# 邮件服务配置
# ========================================
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>

# ========================================
# 监控和日志
# ========================================
LOG_LEVEL=debug
LOG_FORMAT=json
ENABLE_SWAGGER=true
SWAGGER_PATH=/api-docs

# ========================================
# 跨域配置
# ========================================
CORS_ORIGIN=http://localhost:3100,http://localhost:8080
CORS_CREDENTIALS=true

# ========================================
# 速率限制
# ========================================
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# ========================================
# Flutter Web 开发
# ========================================
FLUTTER_WEB_PORT=8080
FLUTTER_WEB_HOST=0.0.0.0