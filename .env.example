# =================================
# MasteryOS 环境变量配置模板
# =================================

# 应用环境
NODE_ENV=production
PORT=3102

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=masteryos
DB_PASSWORD=masteryos_prod_password
DB_NAME=masteryos
DATABASE_URL=************************************************************/masteryos

# Redis配置  
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_URL=redis://redis:6379

# MinIO对象存储配置
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=masteryos-documents
MINIO_USE_SSL=false

# JWT认证配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# 文件上传配置
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 性能监控配置（可选）
ENABLE_METRICS=true
METRICS_PORT=9090

# 安全配置
CORS_ORIGIN=http://localhost:3200,https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Docker相关
COMPOSE_PROJECT_NAME=masteryos

# 生产环境特定配置
PRODUCTION_HOST=your-server.com
PRODUCTION_USER=deploy
PRODUCTION_URL=https://your-domain.com

# SSL证书路径（HTTPS）
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 备份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=masteryos-backups

# 监控和告警
SENTRY_DSN=https://your-sentry-dsn
PROMETHEUS_ENABLED=true
GRAFANA_ADMIN_PASSWORD=admin123