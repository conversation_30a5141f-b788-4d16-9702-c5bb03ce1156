# MasteryOS 混合架构设计：移动端学习 + Web端管理

**日期**: 2025年7月14日  
**版本**: 3.0 (Mobile+Web Hybrid)  
**作者**: 技术架构团队

## 🎯 业务场景重新定位

### B2C + B2B 混合模式
- **C端学习者**: 主要通过**Flutter移动端**进行学习（PDF阅读、AI交互、进度追踪）
- **B端管理员**: 通过**Web管理端**进行内容管理、组织管理、数据分析

### 核心业务需求

| 用户角色 | 使用场景 | 技术需求 |
|----------|----------|----------|
| **学习者** | 移动端学习PDF、AI对话、进度追踪 | 高性能PDF、离线支持、流畅体验 |
| **培训管理员** | 上传培训资料、员工管理、课程设置 | 复杂表单、批量操作、权限控制 |
| **企业管理者** | 学习数据分析、效果评估、ROI统计 | 数据可视化、报表生成、dashboard |

## 🏗️ 混合架构设计

### 核心架构原则
- **关注点分离**: 移动端专注学习体验，Web端专注管理功能
- **独立部署**: 移动API和管理API可独立开发、部署、扩展
- **多租户安全**: 严格的企业数据隔离
- **性能隔离**: 管理端重查询不影响移动端性能

### 整体架构图
```
┌─────────────────┐    ┌─────────────────────────┐
│   Flutter App   │    │    Admin Web SPA        │
│  (iOS/Android)  │    │  (React + React-admin)  │
└─────────┬───────┘    └─────────┬───────────────┘
          │                      │
          │ HTTPS/REST           │ HTTPS/REST
          │                      │
┌─────────▼───────┐    ┌─────────▼───────────────┐
│   Mobile BFF    │    │     Admin BFF           │
│   (NestJS)      │    │    (NestJS)             │
│ 专注学习功能     │    │  专注管理功能            │
└─────────┬───────┘    └─────────┬───────────────┘
          │                      │
          └──────────┬───────────┘
                     │
        ┌────────────▼─────────────┐
        │   共享数据层              │
        │ PostgreSQL + pgvector    │
        │ Redis + BullMQ           │
        │ S3 File Storage          │
        └──────────────────────────┘
```

## 📱 Mobile BFF (移动端后端)

### 设计目标
- **轻量高效**: 专注C端学习功能，API响应<100ms
- **离线友好**: 支持数据同步和离线缓存
- **AI优化**: pgvector语义搜索，实时AI对话

### 核心API
```typescript
// 学习相关API
GET  /api/v1/documents/:id/content    // 获取PDF内容
POST /api/v1/documents/:id/annotations // 保存标注
GET  /api/v1/ai/chat                  // AI对话
POST /api/v1/learning/sessions        // 记录学习时长

// 用户相关API
GET  /api/v1/profile                  // 用户信息
PUT  /api/v1/profile/progress         // 更新学习进度
```

## 🌐 Admin BFF (管理端后端)

### 设计目标
- **功能丰富**: 支持复杂CRUD、批量操作、数据分析
- **多租户**: 强制的organization_id隔离
- **异步处理**: 大文件上传、批量导入通过队列处理

### 核心API
```typescript
// 内容管理API
POST /api/admin/organizations/:orgId/documents/upload  // 批量上传PDF
GET  /api/admin/organizations/:orgId/documents         // 文档列表
POST /api/admin/organizations/:orgId/learning-paths   // 创建学习路径

// 组织管理API
POST /api/admin/organizations/:orgId/users/batch      // 批量创建用户
GET  /api/admin/organizations/:orgId/users            // 用户列表
PUT  /api/admin/organizations/:orgId/users/:id/role   // 角色分配

// 数据分析API
GET  /api/admin/organizations/:orgId/analytics/dashboard    // 仪表盘数据
GET  /api/admin/organizations/:orgId/analytics/completion   // 完成率统计
GET  /api/admin/organizations/:orgId/analytics/engagement   // 参与度分析
```

## 🔐 多租户安全架构

### 数据隔离策略
```sql
-- 核心表结构示例
CREATE TABLE organizations (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE users (
    id UUID PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id),
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) DEFAULT 'learner', -- learner, admin, manager
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE documents (
    id UUID PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id),
    title VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 所有查询必须包含organization_id过滤
-- 例：SELECT * FROM documents WHERE organization_id = ? AND id = ?
```

### JWT令牌设计
```typescript
interface JWTPayload {
  userId: string;
  organizationId: string;  // 强制租户隔离
  role: 'learner' | 'admin' | 'manager';
  permissions: string[];   // 细粒度权限
  exp: number;
}
```

### 自动租户过滤中间件
```typescript
// Admin BFF 中的强制租户过滤
@Injectable()
export class TenancyMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const { organizationId } = req.user; // 从JWT解析
    
    // 将organizationId注入到所有数据库查询
    req.organizationId = organizationId;
    
    // 所有ORM查询自动添加WHERE organization_id = ?
    prisma.$use(async (params, next) => {
      if (params.model && TENANT_MODELS.includes(params.model)) {
        params.args.where = {
          ...params.args.where,
          organizationId: req.organizationId
        };
      }
      return next(params);
    });
    
    next();
  }
}
```

## 🖥️ Web管理端技术栈

### 框架选择：React-admin + Tremor
```typescript
// 主要依赖
{
  "react-admin": "^4.16.0",      // 核心CRUD框架
  "tremor": "^3.14.0",           // 数据可视化组件
  "react": "^18.2.0",
  "vite": "^4.4.0",
  "tailwindcss": "^3.3.0"
}
```

### 项目结构
```
admin-spa/
├── src/
│   ├── components/
│   │   ├── dashboard/         # Tremor仪表盘组件
│   │   ├── layout/           # 通用布局
│   │   └── shared/           # 共享组件
│   ├── resources/            # React-admin资源
│   │   ├── users/           # 用户管理
│   │   ├── documents/       # 文档管理
│   │   ├── learning-paths/  # 学习路径
│   │   └── analytics/       # 数据分析
│   ├── providers/
│   │   ├── authProvider.ts  # 认证提供者
│   │   └── dataProvider.ts  # 数据提供者
│   └── App.tsx
├── public/
└── package.json
```

### 核心组件示例
```typescript
// 用户管理资源
import { List, Datagrid, TextField, EmailField, Create, SimpleForm, TextInput } from 'react-admin';

export const UserList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="name" />
      <EmailField source="email" />
      <TextField source="role" />
      <TextField source="department" />
    </Datagrid>
  </List>
);

// 数据可视化仪表盘
import { Card, Metric, BarChart } from 'tremor';

export const LearningDashboard = () => {
  const { data: stats } = useGetOne('analytics/dashboard');
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <Metric title="总学习时长" value={`${stats.totalHours}小时`} />
      </Card>
      <Card>
        <Metric title="课程完成率" value={`${stats.completionRate}%`} />
      </Card>
      <Card>
        <BarChart
          title="部门学习进度"
          data={stats.departmentProgress}
          index="department"
          categories={["progress"]}
        />
      </Card>
    </div>
  );
};
```

## 🚀 开发环境配置

### Docker Compose 更新
```yaml
# docker-compose.hybrid-dev.yml
version: '3.8'

services:
  # Mobile BFF (移动端API)
  mobile-api:
    build:
      context: ./mobile-bff
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/masteryos
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=mobile-dev-secret

  # Admin BFF (管理端API)  
  admin-api:
    build:
      context: ./admin-bff
      dockerfile: Dockerfile
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/masteryos
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=admin-dev-secret

  # Admin SPA (管理端前端)
  admin-spa:
    build:
      context: ./admin-spa
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VITE_ADMIN_API_URL=http://admin-api:3000
    depends_on:
      - admin-api

  # 共享数据层
  db:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=masteryos
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-hybrid-db.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"

volumes:
  postgres_data:
```

## 📊 性能优化策略

### 1. 数据库读写分离
```typescript
// Admin BFF 使用读副本进行分析查询
@Injectable()
export class AnalyticsService {
  constructor(
    @Inject('PRIMARY_DB') private primaryDb: PrismaService,
    @Inject('READONLY_DB') private readonlyDb: PrismaService, // 读副本
  ) {}

  async getDashboardStats(organizationId: string) {
    // 重查询使用读副本，不影响主库性能
    return this.readonlyDb.learningSession.groupBy({
      by: ['userId'],
      where: { organizationId },
      _sum: { durationMinutes: true },
    });
  }
}
```

### 2. 缓存策略
```typescript
// Redis缓存热点数据
@Injectable()
export class CacheService {
  // 移动端API：缓存用户学习数据
  @Cacheable('user-progress', 300) // 5分钟缓存
  async getUserProgress(userId: string) {
    return this.db.user.findUnique({
      where: { id: userId },
      include: { learningProgress: true }
    });
  }

  // 管理端API：缓存组织统计数据
  @Cacheable('org-stats', 1800) // 30分钟缓存
  async getOrganizationStats(organizationId: string) {
    // 复杂聚合查询结果缓存
  }
}
```

### 3. 异步任务处理
```typescript
// 大文件上传异步处理
@Processor('document-processing')
export class DocumentProcessor {
  @Process('pdf-upload')
  async processPdfUpload(job: Job<{ filePath: string; organizationId: string }>) {
    const { filePath, organizationId } = job.data;
    
    // 1. PDF解析（Docling）
    const markdown = await this.doclingService.convertToMarkdown(filePath);
    
    // 2. 生成向量嵌入
    const embeddings = await this.aiService.generateEmbeddings(markdown);
    
    // 3. 存储到数据库
    await this.db.document.create({
      data: {
        organizationId,
        content: markdown,
        embeddings,
        status: 'processed'
      }
    });
    
    // 4. 通知前端处理完成
    await this.notificationService.notifyProcessingComplete(organizationId);
  }
}
```

## 📈 实施路线图

### 阶段0：基础架构（2-3周）
- [x] ~~移动端优先架构~~ (已完成)
- [ ] 创建Admin BFF和Admin SPA项目
- [ ] 实施多租户数据模型和安全中间件
- [ ] 配置开发环境和CI/CD

### 阶段1：核心管理功能（3-4周）
- [ ] 用户和组织管理CRUD
- [ ] 文档上传和管理功能
- [ ] 基础权限和角色系统
- [ ] React-admin基础界面

### 阶段2：高级功能（4-5周）
- [ ] 学习路径和课程管理
- [ ] 批量操作和异步任务
- [ ] 数据导入导出功能
- [ ] 基础数据分析

### 阶段3：数据可视化（3-4周）
- [ ] Tremor仪表盘集成
- [ ] 学习进度和完成率统计
- [ ] 部门和个人效果评估
- [ ] 导出报表功能

### 阶段4：优化和扩展（持续）
- [ ] 性能优化和读写分离
- [ ] 高级分析和机器学习集成
- [ ] 移动端新功能需求
- [ ] 企业级功能扩展

## 💰 成本效益分析

### 基础设施成本（月度）
- **Mobile BFF**: $30-50 (轻量级容器)
- **Admin BFF**: $50-80 (复杂查询处理)
- **Admin SPA**: $15-25 (静态站点)
- **数据库主从**: $60-100 (读写分离)
- **缓存和队列**: $20-30
- **文件存储**: $15-25
- **总计**: $190-310/月

### 开发成本
- **新增技术栈学习**: React-admin 1周, Tremor 0.5周
- **架构重构时间**: 2-3周并行开发
- **团队技能匹配**: 充分利用现有React/TypeScript技能

## 🎯 总结

通过**移动端+Web端混合架构**，MasteryOS可以同时满足：

✅ **C端学习者**: Flutter高性能移动学习体验  
✅ **B端管理员**: React-admin强大的Web管理功能  
✅ **企业客户**: 数据安全隔离和深度分析能力  
✅ **技术团队**: 关注点分离，独立开发部署  

这个架构为MasteryOS的B2B转型提供了完整的技术基础，同时保持了移动端学习体验的领先优势。

---

**关键决策记录**：
- 采用BFF模式实现移动端和管理端API分离
- 选择React-admin + Tremor的组合提供管理功能
- 实施organization_id强制租户隔离
- 通过读写分离保证性能隔离