# MasteryOS 项目文档

**基于"一万小时定律"的智能技能发展追踪系统**

---

## 📚 核心文档

### 🚀 [快速开始指南](./getting-started/)
- [环境配置](./getting-started/environment-setup.md) - 开发环境搭建
- [项目安装](./getting-started/installation.md) - 项目安装步骤  
- [快速入门](./getting-started/quick-start.md) - 5分钟上手指南

### 📐 [系统架构](./architecture/)
- [架构概览](./architecture/overview.md) - 系统整体设计
- [技术栈](./architecture/technology-stack.md) - 技术选型说明

### 💻 [开发指南](./development/)
- [开发环境](./development/README.md) - 开发环境说明
- [开发指南](./development/development-guide.md) - 开发最佳实践
- [文档规范](./development/documentation-standards.md) - 文档编写标准

### 🔌 [API 文档](./api/)
- [移动端 API](./api/mobile-bff/) - Flutter 应用接口
- [管理端 API](./api/admin-bff/) - 管理后台接口  
- [数据模型](./api/data-models/) - 核心数据结构

### 🗄️ [数据库设计](./database/)
- [数据库架构](./database/README.md) - PostgreSQL + Redis 架构

### 📋 [业务规划](./business/plan/)
- [产品需求](./business/plan/PRD.md) - 产品功能需求
- [项目路线图](./business/plan/Roadmap.md) - 开发计划
- [用户故事](./business/plan/User_Story_Map.md) - 用户场景映射

### 📁 [历史归档](./archive/)
历史版本文档和分析报告存档

---

## 🎯 项目特色

- **一万小时追踪** - 基于科学理论的技能精通度量
- **AI 智能辅助** - 个性化学习建议和进度分析  
- **多端支持** - Flutter 移动端 + React 管理端
- **现代架构** - NestJS + PostgreSQL + Redis 技术栈

## 💡 核心功能

1. **技能追踪** - 精确记录学习时间和进度
2. **文档管理** - 支持 PDF 解析和知识提取
3. **进度分析** - 可视化学习曲线和成就系统
4. **社交学习** - 学习小组和经验分享

---

*最后更新: 2025-08-04*