---
title: "文档系统开发指南"
version: "1.0.0"
lastUpdated: "2025-01-04"
author: "开发团队"
reviewers: []
status: "approved"
tags:
  - "development"
  - "documentation"
  - "vuepress"
dependencies: []
---

# 文档系统开发指南

## 概述

本指南介绍如何开发和维护 MasteryOS 项目的文档系统。文档系统基于 VuePress 构建，支持 Markdown 格式，具有搜索、导航等功能。

## 技术栈

- **VuePress 2.x**: 静态网站生成器
- **Markdown**: 文档编写格式
- **Node.js 18+**: 运行环境
- **GitHub Actions**: 自动化构建和部署

## 快速开始

### 环境要求

- Node.js 18.x 或更高版本
- npm 或 yarn 包管理器
- Git 版本控制

### 本地开发

1. **克隆项目**
   ```bash
   git clone https://github.com/masteryos/masteryos.git
   cd masteryos/docs
   ```

2. **安装依赖**
   ```bash
   pnpm install
   ```

3. **启动开发服务器**
   ```bash
   pnpm run dev
   # 或使用开发脚本
   ./scripts/dev.sh dev
   ```

4. **访问文档**
   
   打开浏览器访问 http://localhost:8080

### 使用开发脚本

项目提供了便捷的开发脚本 `scripts/dev.sh`：

```bash
# 启动开发服务器
./scripts/dev.sh dev

# 构建生产版本
./scripts/dev.sh build

# 运行格式检查
./scripts/dev.sh lint

# 检查链接有效性
./scripts/dev.sh links

# 显示帮助
./scripts/dev.sh help
```

## 文档编写

### 文档结构

```
docs/
├── .vuepress/           # VuePress 配置
│   ├── config.js       # 主配置文件
│   └── public/         # 静态资源
├── .templates/         # 文档模板
├── architecture/       # 架构文档
├── api/               # API 文档
├── database/          # 数据库文档
├── development/       # 开发文档
├── deployment/        # 部署文档
├── user-guides/       # 用户指南
└── project-management/ # 项目管理
```

### 创建新文档

1. **选择合适的目录**
   
   根据文档类型选择对应的目录

2. **使用文档模板**
   
   从 `.templates/` 目录复制合适的模板：
   - `document-template.md` - 通用文档模板
   - `api-template.md` - API 文档模板
   - `database-template.md` - 数据库文档模板
   - `guide-template.md` - 操作指南模板

3. **填写文档内容**
   
   按照模板格式填写文档内容

4. **更新导航配置**
   
   在 `.vuepress/config.js` 中添加新文档的导航链接

### 文档规范

请遵循 [文档编写规范](./development/documentation-standards.md) 中的要求：

- 使用正确的 Front Matter 格式
- 遵循 Markdown 语法规范
- 保持文档结构清晰
- 提供充足的示例和说明

## 配置说明

### VuePress 配置

主配置文件位于 `.vuepress/config.js`，包含以下配置：

- **站点信息**: 标题、描述、基础路径
- **主题配置**: 导航栏、侧边栏、仓库链接
- **插件配置**: 搜索、图片缩放、进度条等
- **Markdown 配置**: 代码高亮、目录生成

### 导航配置

#### 顶部导航栏

在 `theme.navbar` 中配置：

```javascript
navbar: [
  {
    text: '架构文档',
    children: [
      '/architecture/overview.md',
      '/architecture/technology-stack.md'
    ]
  }
]
```

#### 侧边栏

在 `theme.sidebar` 中配置：

```javascript
sidebar: {
  '/architecture/': [
    {
      text: '架构文档',
      children: [
        '/architecture/overview.md',
        '/architecture/technology-stack.md'
      ]
    }
  ]
}
```

### 插件配置

#### 搜索插件

```javascript
searchPlugin({
  locales: {
    '/': {
      placeholder: '搜索文档',
    }
  },
  maxSuggestions: 10
})
```

#### 图片缩放插件

```javascript
mediumZoomPlugin({
  selector: '.theme-default-content :not(a) > img'
})
```

## 构建和部署

### 本地构建

```bash
pnpm run build
```

构建产物输出到 `dist/` 目录。

### 自动化部署

项目配置了 GitHub Actions 自动化流程：

1. **代码检查**: Markdown 格式检查、链接有效性检查
2. **构建文档**: 生成静态网站
3. **部署**: 自动部署到 GitHub Pages

### 部署流程

1. 推送代码到 `main` 分支
2. GitHub Actions 自动触发构建
3. 构建成功后自动部署到 GitHub Pages
4. 访问文档网站查看更新

## 质量保证

### 自动化检查

#### Markdown 格式检查

使用 markdownlint 进行格式检查：

```bash
pnpm run lint
pnpm run lint:fix  # 自动修复
```

#### 链接有效性检查

使用 markdown-link-check 检查链接：

```bash
pnpm run check-links
```

### 手动检查

- 内容准确性检查
- 逻辑结构审查
- 用户体验测试
- 跨浏览器兼容性测试

## 故障排除

### 常见问题

#### 1. 开发服务器启动失败

**症状**: `pnpm run dev` 命令失败

**解决方案**:
1. 检查 Node.js 版本是否符合要求
2. 删除 `node_modules` 和 `package-lock.json`，重新安装依赖
3. 检查端口 8080 是否被占用

#### 2. 构建失败

**症状**: `pnpm run build` 命令失败

**解决方案**:
1. 检查 Markdown 文件语法是否正确
2. 检查配置文件是否有语法错误
3. 查看构建日志定位具体错误

#### 3. 搜索功能不工作

**症状**: 搜索框无法搜索到内容

**解决方案**:
1. 检查搜索插件配置
2. 确认文档包含可搜索的内容
3. 重新构建文档

#### 4. 图片无法显示

**症状**: 文档中的图片无法正常显示

**解决方案**:
1. 检查图片路径是否正确
2. 确认图片文件存在于 `public` 目录
3. 检查图片文件格式是否支持

### 调试技巧

1. **查看构建日志**
   ```bash
   pnpm run build -- --debug
   ```

2. **检查配置**
   ```bash
   node -e "console.log(require('./.vuepress/config.js'))"
   ```

3. **清理缓存**
   ```bash
   rm -rf .temp .cache node_modules/.cache
   ```

## 贡献指南

### 提交流程

1. 创建功能分支
2. 编写或修改文档
3. 运行质量检查
4. 提交 Pull Request
5. 代码审查
6. 合并到主分支

### 提交规范

遵循 [Conventional Commits](https://conventionalcommits.org/) 规范：

```
docs(scope): subject

body

footer
```

示例：
```
docs(api): add user authentication endpoints

- Add login and logout API documentation
- Include JWT token handling examples
- Update error code definitions

Closes #123
```

## 更新历史

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0.0 | 2025-01-04 | 开发团队 | 初始版本 |