# 系统架构概览

## 1. 架构概述

MasteryOS（万时通）采用现代化的混合架构设计，支持B2C移动端学习和B2B Web端管理的双重需求。系统基于微服务架构思想，采用BFF（Backend for Frontend）模式，为不同的前端应用提供专门优化的API服务。

### 1.1 架构设计原则

- **用户体验优先**: 移动端优化的学习体验，Web端高效的管理界面
- **服务分离**: 移动端BFF专注学习功能，管理端BFF专注管理功能
- **数据统一**: 共享数据层确保数据一致性
- **可扩展性**: 支持水平扩展和功能模块化
- **安全性**: 多层安全防护和多租户隔离

### 1.2 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Flutter移动应用<br/>iOS/Android]
        B[React管理后台<br/>Web浏览器]
    end
    
    subgraph "BFF服务层"
        C[Mobile BFF<br/>NestJS:3101<br/>学习功能API]
        D[Admin BFF<br/>NestJS:3102<br/>管理功能API]
    end
    
    subgraph "数据服务层"
        E[PostgreSQL<br/>主数据库:5432]
        F[Redis<br/>缓存队列:6379]
        G[MinIO<br/>文件存储:9000]
    end
    
    subgraph "外部服务"
        H[AI服务<br/>OpenAI API]
        I[PDF处理<br/>Docling:8080]
        J[推送服务<br/>FCM]
    end
    
    A --> C
    B --> D
    C --> E
    C --> F
    C --> G
    D --> E
    D --> F
    D --> G
    C --> H
    C --> I
    D --> I
    C --> J
    
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style F fill:#ffebee
    style G fill:#e0f2f1
```

## 2. 系统组件说明

### 2.1 前端应用层

#### 2.1.1 Flutter移动应用
- **技术栈**: Flutter 3.32.1 + Dart
- **端口**: 8080 (开发环境Web服务)
- **主要功能**:
  - 技能学习和时间跟踪
  - AI助手对话和学习建议
  - 个人学习数据分析
  - 社交学习功能
- **特性**:
  - 跨平台一致性体验
  - 离线数据缓存
  - 实时数据同步
  - 响应式设计

#### 2.1.2 React管理后台
- **技术栈**: React 18 + TypeScript + React-admin
- **端口**: 3100
- **主要功能**:
  - 用户和组织管理
  - 学习数据分析和报表
  - 系统配置和监控
  - 企业级权限管理
- **特性**:
  - 现代化管理界面
  - 数据可视化图表
  - 批量操作支持
  - 多租户管理

### 2.2 BFF服务层

#### 2.2.1 Mobile BFF (移动端后端)
- **技术栈**: NestJS + TypeScript
- **端口**: 3101
- **服务职责**:
  - 用户认证和个人资料管理
  - 技能创建和学习记录
  - AI助手服务集成
  - 实时通知推送
- **性能优化**:
  - 轻量级API设计
  - 响应时间 < 100ms
  - 离线数据同步支持
  - 移动端网络优化

#### 2.2.2 Admin BFF (管理端后端)
- **技术栈**: NestJS + TypeScript
- **端口**: 3102
- **服务职责**:
  - 企业和组织管理
  - 用户权限和角色管理
  - 数据分析和报表生成
  - 系统监控和配置
- **功能特性**:
  - 复杂CRUD操作
  - 批量数据处理
  - 多租户安全隔离
  - 高级数据分析

### 2.3 数据服务层

#### 2.3.1 PostgreSQL主数据库
- **版本**: PostgreSQL 15 + pgvector扩展
- **端口**: 5432
- **数据存储**:
  - 用户和组织数据
  - 技能和学习记录
  - AI对话和向量数据
  - 系统配置信息
- **特性**:
  - ACID事务保证
  - 向量相似度搜索
  - 读写分离支持
  - 自动备份机制

#### 2.3.2 Redis缓存队列
- **版本**: Redis 7
- **端口**: 6379
- **用途**:
  - 会话缓存存储
  - 热点数据缓存
  - 任务队列管理
  - 实时数据同步
- **配置**:
  - 持久化存储
  - 主从复制
  - 集群模式支持

#### 2.3.3 MinIO文件存储
- **端口**: 9000 (API), 9101 (控制台)
- **存储内容**:
  - PDF学习文档
  - 用户头像和图片
  - 系统静态资源
  - 数据备份文件
- **特性**:
  - S3兼容API
  - 分布式存储
  - 版本控制
  - 访问权限控制

### 2.4 外部服务集成

#### 2.4.1 AI服务集成
- **OpenAI API**: GPT-4模型用于学习建议和对话
- **文本嵌入**: text-embedding-ada-002用于语义搜索
- **本地AI**: Ollama支持本地模型部署

#### 2.4.2 PDF处理服务
- **Docling服务**: PDF文档解析和内容提取
- **端口**: 8080
- **功能**: PDF转Markdown，内容结构化

#### 2.4.3 推送通知服务
- **Firebase Cloud Messaging**: 移动端推送通知
- **Web Push**: 浏览器推送通知支持

## 3. 数据流图

### 3.1 用户学习数据流

```mermaid
sequenceDiagram
    participant U as 用户(Flutter)
    participant M as Mobile BFF
    participant DB as PostgreSQL
    participant R as Redis
    participant AI as AI服务
    
    U->>M: 开始学习会话
    M->>DB: 创建学习记录
    M->>R: 缓存会话状态
    M-->>U: 返回会话ID
    
    U->>M: 结束学习并评估
    M->>AI: 请求学习建议
    AI-->>M: 返回AI建议
    M->>DB: 更新学习记录
    M->>R: 更新缓存数据
    M-->>U: 返回学习总结
```

### 3.2 PDF文档处理数据流

```mermaid
sequenceDiagram
    participant U as 用户
    participant BFF as BFF服务
    participant S3 as MinIO存储
    participant DOC as Docling服务
    participant DB as PostgreSQL
    participant AI as AI服务
    
    U->>BFF: 上传PDF文档
    BFF->>S3: 存储原始文件
    BFF->>DOC: 请求文档解析
    DOC-->>BFF: 返回结构化内容
    BFF->>AI: 生成学习计划
    AI-->>BFF: 返回学习建议
    BFF->>DB: 保存文档元数据
    BFF-->>U: 返回处理结果
```

## 4. 技术决策和原因

### 4.1 架构选择

#### BFF架构模式
**决策**: 采用BFF（Backend for Frontend）架构
**原因**:
- 移动端和Web端有不同的性能和功能需求
- 可以针对不同客户端优化API设计
- 降低前端复杂度，提高开发效率
- 支持独立部署和扩展

#### 微服务vs单体架构
**决策**: 采用模块化单体架构，为微服务演进做准备
**原因**:
- 项目初期，单体架构开发和部署更简单
- 模块化设计便于后续拆分为微服务
- 降低运维复杂度和成本
- 保持代码内聚性和数据一致性

### 4.2 技术栈选择

#### Flutter vs React Native
**决策**: 选择Flutter作为移动端开发框架
**原因**:
- 更好的性能和用户体验
- 统一的iOS/Android代码库
- Google长期支持和活跃社区
- 丰富的UI组件和动画支持

#### NestJS vs Express/Fastify
**决策**: 选择NestJS作为后端框架
**原因**:
- TypeScript原生支持
- 模块化架构和依赖注入
- 丰富的生态系统和中间件
- 企业级应用开发经验

#### PostgreSQL vs MongoDB
**决策**: 选择PostgreSQL作为主数据库
**原因**:
- ACID事务保证数据一致性
- pgvector扩展支持向量搜索
- 成熟的生态系统和工具
- 优秀的性能和可扩展性

### 4.3 部署架构选择

#### Docker容器化
**决策**: 全面采用Docker容器化部署
**原因**:
- 环境一致性保证
- 简化部署和扩展
- 资源隔离和安全性
- 支持多环境管理

#### 混合开发环境
**决策**: 提供Docker混合开发环境
**原因**:
- 快速环境搭建
- 开发环境一致性
- 支持多种开发模式
- 降低新开发者上手成本

## 5. 系统边界和约束

### 5.1 系统边界

**包含的功能**:
- 用户认证和权限管理
- 技能学习和时间跟踪
- AI助手和学习建议
- PDF文档处理和分析
- 数据分析和可视化
- 社交学习功能

**不包含的功能**:
- 在线课程内容制作
- 视频直播和会议
- 复杂的支付和电商功能
- 第三方学习平台集成

### 5.2 技术约束

**性能约束**:
- API响应时间 < 2秒
- 页面加载时间 < 3秒
- 支持1000并发用户
- 数据库查询优化

**安全约束**:
- HTTPS强制加密
- JWT Token认证
- 多租户数据隔离
- 定期安全审计

**可扩展性约束**:
- 水平扩展支持
- 数据库读写分离
- 缓存层优化
- CDN内容分发

## 6. 未来架构演进

### 6.1 微服务拆分计划

**第一阶段**: 核心服务拆分
- 用户服务 (User Service)
- 技能服务 (Skill Service)
- AI服务 (AI Service)

**第二阶段**: 业务服务拆分
- 通知服务 (Notification Service)
- 分析服务 (Analytics Service)
- 文档服务 (Document Service)

**第三阶段**: 平台服务拆分
- 网关服务 (API Gateway)
- 配置服务 (Config Service)
- 监控服务 (Monitoring Service)

### 6.2 技术栈升级计划

- **数据库**: 考虑引入时序数据库(InfluxDB)用于学习数据分析
- **搜索**: 集成Elasticsearch提供全文搜索能力
- **消息队列**: 引入RabbitMQ或Apache Kafka处理异步任务
- **监控**: 集成Prometheus + Grafana监控体系
- **日志**: 采用ELK Stack进行日志聚合和分析

---

*文档版本: v1.0*  
*最后更新: 2025-01-04*  
*维护者: 开发团队*