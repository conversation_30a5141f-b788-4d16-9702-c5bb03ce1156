# MasteryOS 统一BFF服务架构设计

## 概述

基于分析现有的 `admin-bff` 和 `mobile-bff` 服务，我们将创建一个统一的 BFF (Backend for Frontend) 服务，支持多客户端访问，提供统一的 API 接口。

## 现状分析

### Admin BFF (已完成)
- **端口**: 3102
- **功能模块**: 用户管理、文档管理、分析数据、健康检查
- **技术特征**: 完整的CRUD操作、TypeORM实体、Swagger文档、数据验证
- **成熟度**: 生产就绪

### Mobile BFF (待开发)
- **端口**: 3000  
- **功能模块**: 基础框架，业务功能待实现
- **技术特征**: 基本NestJS结构，TODO占位符
- **成熟度**: 框架阶段

## 统一架构设计

### 1. 服务结构
```
unified-bff/
├── src/
│   ├── core/                    # 核心模块
│   │   ├── auth/               # 认证授权
│   │   ├── database/           # 数据库配置  
│   │   ├── cache/              # 缓存服务
│   │   ├── storage/            # 文件存储
│   │   └── logger/             # 日志服务
│   ├── shared/                  # 共享模块
│   │   ├── dto/                # 通用DTO
│   │   ├── entities/           # 数据库实体
│   │   ├── filters/            # 异常过滤器
│   │   ├── guards/             # 路由守卫
│   │   ├── interceptors/       # 拦截器
│   │   ├── pipes/              # 管道
│   │   └── decorators/         # 装饰器
│   ├── modules/                 # 业务模块
│   │   ├── users/              # 用户管理
│   │   ├── auth/               # 认证服务
│   │   ├── documents/          # 文档管理
│   │   ├── analytics/          # 数据分析
│   │   ├── organizations/      # 组织管理
│   │   ├── learning/           # 学习模块
│   │   ├── profile/            # 个人资料
│   │   └── notifications/      # 消息通知
│   ├── api/                     # API版本控制
│   │   ├── v1/                 # API v1
│   │   │   ├── admin/          # 管理端API
│   │   │   └── mobile/         # 移动端API
│   │   └── common/             # 通用API
│   ├── config/                  # 配置文件
│   └── health/                  # 健康检查
```

### 2. 技术架构

#### 核心技术栈
- **框架**: NestJS 10.x
- **数据库**: PostgreSQL + TypeORM
- **缓存**: Redis
- **存储**: MinIO
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **验证**: class-validator + class-transformer
- **测试**: Jest + Supertest

#### 架构特性
- **多客户端支持**: 统一服务，不同API版本
- **模块化设计**: 清晰的业务模块分离
- **认证授权**: 基于角色的访问控制(RBAC)
- **API版本管理**: 支持多版本共存
- **性能优化**: Redis缓存、连接池优化
- **可观测性**: 结构化日志、健康检查、指标监控

### 3. API设计原则

#### 路由结构
```
/api/v1/admin/users          # 管理端用户API
/api/v1/admin/documents      # 管理端文档API  
/api/v1/admin/analytics      # 管理端分析API

/api/v1/mobile/profile       # 移动端个人资料
/api/v1/mobile/learning      # 移动端学习模块
/api/v1/mobile/documents     # 移动端文档API

/api/v1/common/auth          # 通用认证API
/api/v1/common/health        # 通用健康检查
```

#### 响应格式标准化
```typescript
// 成功响应
{
  "success": true,
  "data": any,
  "meta"?: {
    "pagination": {
      "page": number,
      "pageSize": number,
      "total": number,
      "totalPages": number
    }
  }
}

// 错误响应  
{
  "success": false,
  "error": {
    "code": string,
    "message": string,
    "details"?: any
  },
  "timestamp": string,
  "path": string
}
```

### 4. 模块设计

#### 用户管理模块 (Users)
- **Admin功能**: CRUD、批量操作、统计分析、角色管理
- **Mobile功能**: 个人资料查看、密码修改、头像上传
- **共享功能**: 用户验证、状态管理

#### 认证模块 (Auth)  
- **登录方式**: 邮箱/用户名密码、手机验证码、第三方登录
- **Token管理**: JWT生成、刷新、黑名单
- **权限控制**: 角色权限、资源权限、操作权限

#### 文档管理模块 (Documents)
- **Admin功能**: 文档CRUD、分类管理、权限设置、统计报表
- **Mobile功能**: 文档浏览、搜索、收藏、下载
- **共享功能**: 文件上传、格式转换、预览生成

#### 分析模块 (Analytics)
- **Admin功能**: 用户行为分析、系统使用统计、报表生成
- **Mobile功能**: 个人学习统计、进度跟踪
- **共享功能**: 事件埋点、数据聚合

### 5. 数据库设计

#### 表结构扩展
```sql
-- 用户表（已存在，需扩展）
ALTER TABLE users ADD COLUMN IF NOT EXISTS mobile_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS wechat_openid VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';

-- 组织表（新增）
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    logo_url TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户组织关系表（新增）
CREATE TABLE user_organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'MEMBER',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, organization_id)
);

-- 学习记录表（新增）
CREATE TABLE learning_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content_id VARCHAR(255) NOT NULL,
    content_type VARCHAR(50) NOT NULL,
    progress DECIMAL(5,2) DEFAULT 0.00,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 6. 认证授权设计

#### 角色权限模型
```typescript
enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',    // 超级管理员
  ADMIN = 'ADMIN',                // 管理员  
  MANAGER = 'MANAGER',            // 经理
  USER = 'USER',                  // 普通用户
  GUEST = 'GUEST'                 // 访客
}

enum Permission {
  // 用户管理
  USER_CREATE = 'user:create',
  USER_READ = 'user:read', 
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  
  // 文档管理
  DOCUMENT_CREATE = 'document:create',
  DOCUMENT_READ = 'document:read',
  DOCUMENT_UPDATE = 'document:update', 
  DOCUMENT_DELETE = 'document:delete',
  
  // 分析数据
  ANALYTICS_READ = 'analytics:read',
  ANALYTICS_EXPORT = 'analytics:export'
}
```

#### API访问控制
```typescript
@Controller('api/v1/admin/users')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.MANAGER)
export class AdminUsersController {
  
  @Get()
  @RequirePermissions(Permission.USER_READ)
  findAll() {}
  
  @Post()
  @RequirePermissions(Permission.USER_CREATE)
  create() {}
}
```

### 7. 缓存策略

#### Redis缓存层次
```typescript
// 用户信息缓存（30分钟）
const userCacheKey = `user:${userId}`;
const userCacheTTL = 30 * 60; // 30分钟

// API响应缓存（5分钟）
const apiCacheKey = `api:${method}:${path}:${queryHash}`;
const apiCacheTTL = 5 * 60; // 5分钟

// 统计数据缓存（1小时）
const statsCacheKey = `stats:${type}:${date}`;
const statsCacheTTL = 60 * 60; // 1小时
```

### 8. 部署配置

#### 环境变量
```bash
# 服务配置
UNIFIED_BFF_PORT=3100
NODE_ENV=production

# 数据库配置  
DATABASE_URL=*********************************************/masteryos

# Redis配置
REDIS_URL=redis://redis:6379

# JWT配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 文件存储
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123

# 外部服务
SMTP_HOST=smtp.gmail.com
SMS_API_KEY=your-sms-api-key
```

#### Docker配置
```yaml
unified-bff:
  build:
    context: ./apps/unified-bff
  ports:
    - "3100:3100"
  environment:
    - UNIFIED_BFF_PORT=3100
    - DATABASE_URL=postgresql://masteryos:${DB_PASSWORD}@postgres:5432/masteryos
    - REDIS_URL=redis://redis:6379
  depends_on:
    - postgres
    - redis
    - minio
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:3100/health"]
    interval: 30s
    timeout: 10s
    retries: 3
```

## 实施计划

### Phase 1: 基础架构搭建
1. 创建统一BFF项目结构
2. 配置数据库连接和Redis
3. 实现认证授权基础框架
4. 设置API版本控制

### Phase 2: 迁移Admin BFF功能  
1. 迁移用户管理模块
2. 迁移文档管理模块
3. 迁移分析数据模块
4. 迁移健康检查模块

### Phase 3: 扩展Mobile功能
1. 实现移动端认证
2. 开发个人资料管理
3. 实现学习模块
4. 开发通知系统

### Phase 4: 性能优化
1. 实现Redis缓存策略
2. API响应时间优化
3. 数据库查询优化
4. 添加监控指标

### Phase 5: 测试与部署
1. 单元测试覆盖
2. 集成测试验证
3. 性能压测
4. 生产环境部署

## 预期收益

1. **代码复用**: 统一的业务逻辑，减少重复开发
2. **维护效率**: 单一服务，降低运维复杂度
3. **API一致性**: 统一的接口规范和响应格式
4. **性能提升**: 共享连接池和缓存层
5. **扩展性**: 模块化设计，易于添加新功能
6. **安全性**: 统一的认证授权机制

## 风险评估

1. **服务可用性**: 单点故障风险 → 容器化部署 + 健康检查
2. **性能瓶颈**: 单服务压力 → 缓存策略 + 数据库优化  
3. **API兼容性**: 版本升级风险 → API版本控制策略
4. **开发复杂度**: 功能耦合风险 → 清晰的模块边界

---

**总结**: 统一BFF服务将显著提升开发效率和系统可维护性，为后续的功能扩展和性能优化奠定坚实基础。