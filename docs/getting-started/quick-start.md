# 快速启动指南

**版本**: 1.0  
**更新时间**: 2025-08-04  
**维护者**: MasteryOS Team  
**状态**: ✅ 稳定

## ⚡ 5分钟快速体验

### 前置条件

确保已完成 [安装指南](installation.md) 中的环境配置。

### 🚀 一键启动

```bash
# 1. 进入项目目录
cd 1w

# 2. 启动数据库环境 (首次启动)
./scripts/1w-db.sh start

# 3. 启动 Flutter Web 开发服务
cd apps/mobile
fvm flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0
```

### 🌐 访问应用

- **移动端预览**: http://localhost:8080
- **数据库管理**: http://localhost:8182 (pgAdmin)

## 📱 功能演示

### 当前可用功能

1. **导航系统**: 底部导航栏，支持页面切换
2. **响应式设计**: 适配不同屏幕尺寸
3. **基础页面**: 首页、文档、学习、个人资料

### 预览截图

```
首页 → 显示项目概览和统计信息
文档 → 学习资料管理 (待开发)
学习 → 技能追踪界面 (待开发)  
个人 → 用户信息和设置 (待开发)
```

## 🛠️ 开发模式

### 数据库管理

```bash
# 查看数据库状态
./scripts/1w-db.sh status

# 连接到 PostgreSQL
./scripts/1w-db.sh connect

# 查看服务日志
./scripts/1w-db.sh logs

# 停止数据库服务
./scripts/1w-db.sh stop
```

### 代码质量检查

```bash
# ESLint 检查
pnpm run lint

# TypeScript 类型检查
pnpm run typecheck

# 代码格式化
pnpm run format
```

### Flutter 开发

```bash
cd apps/mobile

# 获取依赖
fvm flutter pub get

# 代码分析
fvm flutter analyze

# 构建 Web 版本
fvm flutter build web --release

# 运行测试
fvm flutter test
```

## 🔧 常用命令

### 项目管理

```bash
# 安装新依赖
pnpm add <package-name>

# 添加开发依赖
pnpm add -D <package-name>

# 更新依赖
pnpm update

# 清理缓存
pnpm store prune
```

### Docker 管理

```bash
# 查看运行的容器
docker ps --filter "name=1w"

# 查看容器日志
docker logs 1w-postgres
docker logs 1w-redis

# 重启容器
docker restart 1w-postgres 1w-redis
```

## 🎯 下一步开发

### 立即可以开始的任务

1. **熟悉代码结构**: 浏览 `apps/` 目录下的各个应用
2. **数据库探索**: 连接到 PostgreSQL 查看表结构
3. **UI 定制**: 修改 Flutter 应用的界面样式
4. **API 测试**: 启动 NestJS 后端服务进行接口测试

### 开发建议

- **Flutter 开发**: 使用热重载提高开发效率
- **API 开发**: 先完成数据模型定义
- **数据库**: 熟悉现有的 18 个数据表结构
- **文档**: 及时更新相关开发文档

## 📖 学习资源

### 技术文档

- [Flutter 官方文档](https://flutter.dev/docs)
- [NestJS 官方文档](https://nestjs.com/)
- [React Admin 文档](https://marmelab.com/react-admin/)
- [PostgreSQL 文档](https://www.postgresql.org/docs/)

### 项目文档

- [系统架构](../architecture/system-overview.md)
- [开发指南](../development/development-guide.md)
- [API 文档](../api/README.md)
- [数据库设计](../architecture/database-design.md)

## ❓ 常见问题

### Q: 如何重置开发环境？

```bash
# 停止所有服务
./scripts/1w-db.sh stop

# 清理容器和数据
./scripts/1w-db.sh clean

# 重新启动
./scripts/1w-db.sh start
```

### Q: Flutter Web 页面无法访问？

1. 检查端口 8080 是否被占用
2. 确认 Flutter 服务正常启动
3. 尝试使用 `--web-hostname=0.0.0.0` 参数

### Q: 数据库连接失败？

1. 确认 Docker 服务正常运行
2. 检查端口 8182 和 8183 是否可用
3. 查看数据库容器日志排查问题

---

🎉 **恭喜！你已经成功启动了 MasteryOS 开发环境**

**下一步**: 查看 [开发指南](../development/development-guide.md) 开始功能开发