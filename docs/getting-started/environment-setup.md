# MasteryOS (1w) 项目启动指南

**版本**: 1.0.0  
**更新时间**: 2025年8月4日  
**项目状态**: ✅ 数据库架构完成，开发环境就绪

---

## 📋 快速启动检查清单

### ✅ 环境验证结果 (2025-08-04)

| 组件 | 状态 | 版本 | 说明 |
|------|------|------|------|
| **数据库环境** | ✅ 正常 | PostgreSQL 16 + Redis 7 | 1w 项目独立容器 |
| **Node.js** | ⚠️ 需要升级 | v22.17.1 | 要求 >=22.18.0 |
| **pnpm** | ✅ 正常 | 10.14.0 | 最新版本 |
| **Flutter** | ✅ 正常 | 3.32.1 | 稳定版，分析无问题 |
| **Docker** | ✅ 正常 | 28.3.2 | 容器运行健康 |
| **TypeScript** | ⚠️ 有警告 | 5.9.2 | 部分配置问题 |
| **ESLint** | ⚠️ 有警告 | 9.32.0 | 代码质量可优化 |

---

## 🚀 完整启动流程

### 第一步：环境准备

#### 1.1 检查并升级 Node.js (推荐)
```bash
# 检查当前版本
node --version  # 当前: v22.17.1

# 使用 NVM 升级到 LTS 版本
nvm install 22.18.0
nvm use 22.18.0
nvm alias default 22.18.0
```

#### 1.2 验证必要工具
```bash
# 验证 pnpm
pnpm --version  # 应显示: 10.14.0

# 验证 Docker
docker --version  # 应显示: Docker version 28.3.2

# 验证 Flutter
fvm flutter --version  # 应显示: Flutter 3.32.1
```

### 第二步：启动数据库环境

#### 2.1 启动 1w 项目数据库
```bash
# 启动 PostgreSQL + Redis
./scripts/1w-db.sh start

# 检查状态 (应显示两个健康的容器)
./scripts/1w-db.sh status
```

**预期输出**:
```
NAMES         STATUS                   PORTS
1w-postgres   Up X minutes (healthy)   0.0.0.0:8182->5432/tcp
1w-redis      Up X minutes (healthy)   0.0.0.0:8183->6379/tcp

✅ PostgreSQL: 健康
✅ Redis: 健康
```

#### 2.2 验证数据库连接
```bash
# 连接到 PostgreSQL (可选)
./scripts/1w-db.sh connect

# 在 PostgreSQL 中验证表结构
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'public';
# 应显示: 18

# 退出数据库
\q
```

### 第三步：安装项目依赖

#### 3.1 安装根目录依赖
```bash
# 安装所有工作区依赖
pnpm install

# 预期警告 (可忽略):
# - Node.js 版本警告 (如果未升级到 22.18.0)
# - 构建脚本警告 (安全限制)
```

#### 3.2 安装 Flutter 依赖
```bash
# 进入 mobile 目录
cd apps/mobile

# 获取 Flutter 依赖
fvm flutter pub get

# 返回根目录
cd ../..
```

### 第四步：代码质量检查 (可选)

#### 4.1 运行自动修复
```bash
# 自动修复格式问题
pnpm run lint:fix

# 预期结果: 大部分格式问题自动修复
# 剩余问题: 主要是类型安全警告，不影响运行
```

#### 4.2 Flutter 代码检查
```bash
cd apps/mobile

# Flutter 代码分析
fvm flutter analyze
# 预期结果: No issues found!

cd ../..
```

### 第五步：启动开发服务

#### 5.1 启动 Flutter Web 开发
```bash
cd apps/mobile

# 启动 Flutter Web 开发服务器
fvm flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0

# 或者用于生产构建测试
fvm flutter build web --release
```

**访问地址**: http://localhost:8080

#### 5.2 启动后端服务 (待开发)
```bash
# Mobile BFF (移动端 API) - 端口 3101
cd apps/mobile-bff
pnpm run start:dev

# Admin BFF (管理端 API) - 端口 3102  
cd ../admin-bff
pnpm run start:dev

# Admin SPA (管理后台) - 端口 3100
cd ../admin-spa
pnpm run dev
```

---

## 🔧 开发环境管理

### 数据库管理
```bash
# 查看数据库状态
./scripts/1w-db.sh status

# 连接到 PostgreSQL
./scripts/1w-db.sh connect

# 连接到 Redis  
./scripts/1w-db.sh redis

# 查看服务日志
./scripts/1w-db.sh logs

# 重启数据库服务
./scripts/1w-db.sh restart

# 停止数据库服务
./scripts/1w-db.sh stop
```

### 代码开发工作流
```bash
# 1. 启动数据库
./scripts/1w-db.sh start

# 2. 检查代码质量
pnpm run lint && pnpm run typecheck

# 3. 启动 Flutter 开发
cd apps/mobile && fvm flutter run -d web-server --web-port=8080

# 4. 开发完成后停止服务
./scripts/1w-db.sh stop
```

---

## 🌐 服务端口分配

| 服务 | 端口 | 状态 | 用途 | 访问地址 |
|------|------|------|------|----------|
| **PostgreSQL** | 8182 | ✅ 运行中 | 主数据库 | localhost:8182 |
| **Redis** | 8183 | ✅ 运行中 | 缓存/队列 | localhost:8183 |
| **Flutter Web** | 8080 | ⏸️ 按需启动 | 移动端开发 | http://localhost:8080 |
| **Mobile BFF** | 3101 | 🔄 待开发 | 移动端 API | http://localhost:3101 |
| **Admin BFF** | 3102 | 🔄 待开发 | 管理端 API | http://localhost:3102 |
| **Admin SPA** | 3100 | 🔄 待开发 | 管理后台 | http://localhost:3100 |

---

## 📊 数据库连接信息

### PostgreSQL (1w-postgres)
```bash
主机: localhost
端口: 8182
用户: masteryos
密码: masteryos123
数据库: masteryos
```

### Redis (1w-redis)
```bash
主机: localhost  
端口: 8183
密码: masteryos123
```

### 数据库架构
- **18个核心数据表** 基于"一万小时定律"设计
- **PostgreSQL 16** + **pgvector** 扩展支持 AI 向量搜索
- **Redis 7** 用于缓存和任务队列
- **完整的成就系统**和游戏化机制

---

## ⚠️ 已知问题与解决方案

### 1. Node.js 版本警告
**问题**: `Unsupported engine: wanted: {"node":">=22.18.0"} (current: {"node":"v22.17.1"})`  
**解决**: 升级到 Node.js 22.18.0 LTS
```bash
nvm install 22.18.0 && nvm use 22.18.0
```

### 2. TypeScript 类型警告
**问题**: 环境变量访问和类型定义警告  
**影响**: 不影响功能运行，仅影响开发体验  
**状态**: 计划在后续开发中优化

### 3. ESLint 配置警告
**问题**: ESLint 9 新配置格式的迁移  
**解决**: 已部分修复，剩余问题不影响开发  
**建议**: 添加 `"type": "module"` 到 package.json

### 4. Flutter 依赖版本
**状态**: 50 个包有更新版本，但当前版本稳定可用  
**建议**: 开发稳定后再考虑升级依赖

---

## 🎯 开发里程碑

### ✅ 已完成 (2025-08-04)
- [x] 完整的数据库架构设计 (18个核心表)
- [x] 独立的 Docker 项目环境 (1w 容器组)
- [x] Flutter 移动应用基础框架
- [x] 现代化工具链升级 (pnpm 10.14.0, ESLint 9.32.0)
- [x] 完整的开发环境配置和管理脚本

### 🔄 进行中
- [ ] 后端 API 实现 (NestJS)
- [ ] 用户认证系统集成
- [ ] 技能管理功能开发

### 📅 计划中
- [ ] PDF 文档处理和向量化
- [ ] AI 学习助手功能
- [ ] 社交和游戏化功能
- [ ] 生产环境部署配置

---

## 📞 故障排除

### 数据库连接问题
```bash
# 检查容器状态
docker ps --filter "name=1w"

# 查看容器日志
./scripts/1w-db.sh logs

# 重启数据库服务
./scripts/1w-db.sh restart
```

### Flutter 开发问题
```bash
# 清理缓存
cd apps/mobile
fvm flutter clean
fvm flutter pub get

# 重新构建
fvm flutter build web --release
```

### 依赖问题
```bash
# 清理并重新安装
rm -rf node_modules apps/*/node_modules
pnpm install

# 清理 pnpm 缓存
pnpm store prune
```

---

## 📚 相关文档

- **[CLAUDE.md](./CLAUDE.md)** - 项目指导文档
- **[README.md](./README.md)** - 项目说明
- **[docs/plan/PRD.md](./docs/plan/PRD.md)** - 产品需求文档
- **[scripts/1w-db.sh](./scripts/1w-db.sh)** - 数据库管理脚本

---

**🎉 开发环境启动完成！开始构建 MasteryOS 智能技能学习平台吧！**