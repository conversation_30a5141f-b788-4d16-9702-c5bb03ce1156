# 安装指南

**版本**: 1.0  
**更新时间**: 2025-08-04  
**维护者**: MasteryOS Team  
**状态**: ✅ 稳定

## 📋 环境要求

### 必需软件

| 软件 | 版本要求 | 用途 | 安装方式 |
|------|----------|------|----------|
| **Node.js** | ≥ 22.18.0 LTS | JavaScript 运行时 | [官方下载](https://nodejs.org/) |
| **pnpm** | ≥ 8.0.0 | 包管理器 | `npm install -g pnpm` |
| **Docker** | ≥ 20.10.0 | 容器化环境 | [官方文档](https://docs.docker.com/get-docker/) |
| **Git** | ≥ 2.30.0 | 版本控制 | [官方下载](https://git-scm.com/) |

### 可选软件

| 软件 | 版本要求 | 用途 | 安装方式 |
|------|----------|------|----------|
| **Flutter** | 3.32.1 | 移动应用开发 | 通过 FVM 管理 |
| **VS Code** | 最新版 | 推荐编辑器 | [官方下载](https://code.visualstudio.com/) |

## 🚀 快速安装

### 1. 克隆项目

```bash
git clone https://github.com/changxiaoyangbrain/1w.git
cd 1w
```

### 2. 安装依赖

```bash
# 使用 pnpm 安装所有依赖
pnpm install

# 验证安装结果
pnpm run --version
```

### 3. 启动开发环境

```bash
# 启动数据库服务
./scripts/1w-db.sh start

# 验证数据库状态
./scripts/1w-db.sh status
```

### 4. 验证安装

```bash
# 检查代码质量
pnpm run lint
pnpm run typecheck

# 启动 Flutter Web 开发
cd apps/mobile
fvm flutter run -d web-server --web-port=8080
```

## 🔧 详细安装步骤

### Node.js 安装

推荐使用 NVM 管理 Node.js 版本：

```bash
# 安装 NVM (macOS/Linux)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 安装指定版本 Node.js
nvm install 22.18.0
nvm use 22.18.0
nvm alias default 22.18.0

# 验证安装
node --version  # 应显示 v22.18.0
```

### Flutter 开发环境

项目使用 FVM 管理 Flutter 版本：

```bash
# 安装 FVM
dart pub global activate fvm

# 在项目目录中安装 Flutter
fvm install

# 验证 Flutter 环境
fvm flutter doctor
```

### Docker 环境

确保 Docker 服务正常运行：

```bash
# 检查 Docker 状态
docker --version
docker compose --version

# 验证 Docker 运行
docker run hello-world
```

## 📁 项目结构

安装完成后，项目目录结构如下：

```
1w/
├── apps/                  # 应用程序
│   ├── mobile/           # Flutter 移动应用
│   ├── admin-spa/        # React 管理后台
│   ├── admin-bff/        # 管理端 API
│   └── mobile-bff/       # 移动端 API
├── infrastructure/       # 基础设施配置
├── scripts/              # 开发脚本
├── docs/                 # 项目文档
└── package.json          # 项目配置
```

## 🌐 服务端口

| 服务 | 端口 | 访问地址 | 状态 |
|------|------|----------|------|
| Flutter Web | 8080 | http://localhost:8080 | 开发环境 |
| PostgreSQL | 8182 | localhost:8182 | 数据库 |
| Redis | 8183 | localhost:8183 | 缓存 |

## ⚠️ 故障排除

### 常见问题

#### 1. Node.js 版本问题
```bash
# 错误：Unsupported engine
# 解决：升级到指定版本
nvm install 22.18.0
nvm use 22.18.0
```

#### 2. pnpm 安装失败
```bash
# 清理缓存重新安装
pnpm store prune
rm -rf node_modules
pnpm install
```

#### 3. Docker 容器启动失败
```bash
# 检查端口占用
lsof -i :8182
lsof -i :8183

# 重启 Docker 服务
docker system prune -f
./scripts/1w-db.sh restart
```

#### 4. Flutter 环境问题
```bash
# 重新配置 Flutter
fvm flutter clean
fvm flutter pub get
fvm flutter doctor
```

### 获取帮助

- **文档**: 查看 [docs/](../README.md) 目录
- **Issue**: 提交到 [GitHub Issues](https://github.com/changxiaoyangbrain/1w/issues)
- **讨论**: 参与 [GitHub Discussions](https://github.com/changxiaoyangbrain/1w/discussions)

---

**下一步**: 完成安装后，查看 [快速启动指南](quick-start.md)