---
title: "文档编写规范"
version: "1.0.0"
lastUpdated: "2025-01-04"
author: "开发团队"
reviewers: []
status: "approved"
tags:
  - "documentation"
  - "standards"
  - "guidelines"
dependencies: []
---

# 文档编写规范

## 概述

本文档定义了 MasteryOS 项目的文档编写规范，确保所有文档的一致性、可读性和维护性。

## 文档结构规范

### 文件命名规范

- 使用小写字母和连字符分隔单词
- 英文文档使用英文命名，中文文档可使用中文或拼音
- 示例：`user-guide.md`、`api-reference.md`

### 目录结构规范

```
docs/
├── .templates/          # 文档模板
├── architecture/        # 架构文档
├── api/                # API文档
├── database/           # 数据库文档
├── development/        # 开发文档
├── deployment/         # 部署文档
├── user-guides/        # 用户指南
└── project-management/ # 项目管理
```

## 文档元数据规范

### Front Matter 格式

所有文档必须包含 YAML Front Matter：

```yaml
---
title: "文档标题"
version: "版本号(语义化版本)"
lastUpdated: "YYYY-MM-DD"
author: "作者姓名"
reviewers: 
  - "审查者1"
  - "审查者2"
status: "draft|review|approved|deprecated"
tags:
  - "标签1"
  - "标签2"
dependencies:
  - "依赖文档1.md"
  - "依赖文档2.md"
---
```

### 字段说明

- **title**: 文档标题，简洁明确
- **version**: 遵循语义化版本规范 (SemVer)
- **lastUpdated**: 最后更新日期，格式为 YYYY-MM-DD
- **author**: 文档作者或主要维护者
- **reviewers**: 文档审查者列表
- **status**: 文档状态
  - `draft`: 草稿状态
  - `review`: 审查中
  - `approved`: 已批准
  - `deprecated`: 已废弃
- **tags**: 文档标签，便于分类和搜索
- **dependencies**: 依赖的其他文档

## 内容编写规范

### 标题层级

- 使用 `#` 到 `######` 表示标题层级
- 一级标题仅用于文档标题
- 避免跳级使用标题（如从 `##` 直接到 `####`）

### 代码块规范

#### 行内代码

使用反引号包围：`code`

#### 代码块

使用三个反引号并指定语言：

```javascript
const example = "Hello World";
console.log(example);
```

#### 支持的语言标识

- `javascript` / `js`
- `typescript` / `ts`
- `bash` / `shell`
- `sql`
- `json`
- `yaml`
- `markdown` / `md`
- `dart`

### 链接规范

#### 内部链接

- 使用相对路径：`[链接文本](./relative-path.md)`
- 链接到章节：`[章节标题](#章节标题)`

#### 外部链接

- 使用完整URL：`[链接文本](https://example.com)`
- 重要外部链接应在文档末尾列出

### 图片规范

- 图片存放在 `docs/assets/images/` 目录
- 使用描述性文件名
- 提供 alt 文本：`![描述文本](./assets/images/example.png)`

### 表格规范

使用 Markdown 表格语法：

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

### 列表规范

#### 无序列表

```markdown
- 项目1
- 项目2
  - 子项目1
  - 子项目2
```

#### 有序列表

```markdown
1. 步骤1
2. 步骤2
   1. 子步骤1
   2. 子步骤2
```

#### 任务列表

```markdown
- [ ] 未完成任务
- [x] 已完成任务
```

## 文档类型规范

### API 文档

- 使用统一的 API 文档模板
- 包含完整的请求/响应示例
- 提供错误码说明
- 包含使用示例

### 数据库文档

- 详细的表结构说明
- 索引和约束信息
- 常用查询示例
- 性能优化建议

### 操作指南

- 清晰的步骤说明
- 必要的截图或示例
- 常见问题解答
- 验证方法

## 版本控制规范

### 版本号规范

遵循语义化版本规范 (SemVer)：

- **主版本号**: 不兼容的重大变更
- **次版本号**: 向后兼容的功能性新增
- **修订号**: 向后兼容的问题修正

示例：`1.2.3`

### 变更记录

每个文档应维护变更历史表格：

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0.0 | 2025-01-04 | 张三 | 初始版本 |
| 1.1.0 | 2025-01-05 | 李四 | 新增章节 |
| 1.1.1 | 2025-01-06 | 王五 | 修正错误 |

## 审查流程

### 文档审查标准

1. **内容准确性**: 信息是否准确、最新
2. **结构清晰性**: 逻辑结构是否清晰
3. **语言规范性**: 语言表达是否规范
4. **格式一致性**: 是否符合格式规范
5. **完整性**: 是否包含必要信息

### 审查流程

1. 作者创建文档并设置状态为 `draft`
2. 作者完成初稿后设置状态为 `review`
3. 指定审查者进行审查
4. 审查者提供反馈和建议
5. 作者根据反馈修改文档
6. 审查通过后设置状态为 `approved`

## 工具和插件

### 推荐编辑器

- Visual Studio Code
- Typora
- Mark Text

### 推荐插件

#### VS Code 插件

- Markdown All in One
- Markdown Preview Enhanced
- markdownlint
- Prettier

### Lint 规则

使用 markdownlint 进行格式检查：

```json
{
  "MD013": false,
  "MD033": false,
  "MD041": false
}
```

## 最佳实践

### 写作建议

1. **简洁明了**: 使用简单直接的语言
2. **结构清晰**: 合理使用标题和列表
3. **示例丰富**: 提供充足的代码示例
4. **及时更新**: 保持文档与代码同步

### 维护建议

1. **定期审查**: 定期检查文档的准确性
2. **用户反馈**: 收集和处理用户反馈
3. **版本管理**: 严格遵循版本控制规范
4. **备份保护**: 确保文档的安全性

## 常见问题

### Q: 如何选择合适的文档模板？

A: 根据文档类型选择：
- 一般文档使用 `document-template.md`
- API 文档使用 `api-template.md`
- 数据库文档使用 `database-template.md`
- 操作指南使用 `guide-template.md`

### Q: 如何处理文档中的敏感信息？

A: 
- 使用占位符替代真实数据
- 在示例中使用虚构数据
- 标记敏感信息并限制访问权限

## 更新历史

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0.0 | 2025-01-04 | 开发团队 | 初始版本 |