# CLAUDE.md

**重要说明**: 
- 请用中文与我交流
- 所有生成的文档和报告都使用当前系统时间 (2025-08-04)
- 本文件为 Claude Code (claude.ai/code) 提供项目工作指南

**最后更新**: 2025年8月4日

## 项目概述

MasteryOS (万时通) 是基于"一万小时定律"的智能技能发展和追踪系统。这是一个综合学习平台，结合AI辅助、社交学习和游戏化机制，帮助用户在任何技能领域实现精通。

## 当前项目状态 🚀

### ✅ 已完成的核心功能 (2025-08-04)

**1. 完整的目录结构重组**
- 统一 monorepo 架构，清晰的 `apps/` 目录组织
- 移除重复和混乱的目录结构
- 标准化的 `infrastructure/docker/` 配置

**2. Flutter 移动应用基础**
- 完整的导航系统 (底部导航栏 + go_router)
- 四个主要页面: 首页、文档、学习、个人资料
- 已解决 TODO 注释，实现个人资料页面导航
- 响应式 UI 设计，支持 Web 开发

**3. 开发环境配置**
- Docker 混合开发环境 (PostgreSQL + Redis)
- 多种开发脚本支持 (Flutter、混合模式、独立模式)
- 完整的开发工具链配置

**4. 项目清理和优化**
- 移除不必要的 `.devcontainer/` 配置
- 清理过时文档和配置文件
- 简化项目结构，降低维护复杂度

**5. 🆕 完整的数据库架构设计与实现 (2025-08-04)**
- 基于"一万小时定律"的完整数据库设计 (18个核心表)
- PostgreSQL + pgvector 扩展，支持 AI 向量搜索
- Redis 缓存和任务队列系统
- 完整的成就系统和游戏化机制数据结构
- 支持 PDF 文档处理和 AI 学习计划生成

**6. 🆕 独立的 Docker 项目环境 (2025-08-04)**
- 创建 `1w` 项目容器组，与其他项目完全隔离
- 独立的端口分配 (PostgreSQL: 8182, Redis: 8183)
- 便捷的数据库管理脚本 `./scripts/1w-db.sh`
- 完整的开发环境工具链

**7. 🆕 现代化的工具链升级 (2025-08-04)**
- pnpm 升级到 10.14.0 (从 8.10.5)
- ESLint 升级到 9.32.0 (从 8.57.1)，采用新的平面配置
- Node.js 版本统一为 22.18.0 LTS
- TypeScript 5.9.2 + 严格类型检查

### 📁 当前目录结构

```
masteryos/
├── apps/                           # ✅ 应用程序目录
│   ├── mobile/                     # ✅ Flutter 移动应用 (已实现导航)
│   ├── admin-spa/                  # ✅ React 管理后台 (基础结构)
│   ├── admin-bff/                  # ✅ 管理端 API (NestJS)
│   └── mobile-bff/                 # ✅ 移动端 API (NestJS)
├── infrastructure/docker/          # ✅ Docker 配置
├── scripts/                        # ✅ 开发脚本
├── docs/plan/                      # ✅ 规划文档
├── CLAUDE.md                       # ✅ 项目指导文档
├── HYBRID-DEV-SETUP.md            # ✅ 开发环境指南
├── PROJECT-STRUCTURE-2025-07-14.md # ✅ 目录结构文档
└── README.md                       # ✅ 项目说明
```

## 开发环境指南

### 🐳 推荐开发方式 - 1w 项目独立环境

```bash
# 启动 1w 项目数据库环境
./scripts/1w-db.sh start

# 查看服务状态
./scripts/1w-db.sh status

# 连接到数据库
./scripts/1w-db.sh connect

# 查看服务日志
./scripts/1w-db.sh logs

# 停止服务
./scripts/1w-db.sh stop
```

### 📱 Flutter 移动端开发

```bash
# 快速启动 Flutter Web 开发
./scripts/flutter-dev-start.sh

# 或手动启动
cd apps/mobile
fvm flutter run -d web-server --web-port=8080
```

### 🔧 必要的开发工具

**前置要求**:
- Node.js 22.18.0 LTS (通过 NVM 管理)
- pnpm 10.14.0 (最新版本)
- Docker & Docker Compose (项目容器化)
- FVM (Flutter Version Manager)
- Git (版本控制)

## 开发命令

### 基础项目管理
```bash
# 项目依赖管理
pnpm install                    # 安装依赖
pnpm run lint                   # 代码检查
pnpm run lint:fix              # 修复代码问题
pnpm run format                 # 格式化代码
pnpm run typecheck             # TypeScript 类型检查

# 1w 项目数据库管理
./scripts/1w-db.sh start        # 启动数据库服务
./scripts/1w-db.sh stop         # 停止数据库服务  
./scripts/1w-db.sh status       # 查看服务状态
./scripts/1w-db.sh connect      # 连接到 PostgreSQL
./scripts/1w-db.sh redis        # 连接到 Redis
```

### Flutter 开发命令
```bash
cd apps/mobile

# 开发相关
fvm flutter pub get            # 获取依赖
fvm flutter analyze            # 代码分析
fvm flutter build web          # 构建 Web 版本
fvm flutter test               # 运行测试

# 开发服务器
fvm flutter run -d web-server --web-port=8080  # 启动 Web 开发
```

## 架构和技术栈

### 当前技术架构
- **数据库**: PostgreSQL 16 + pgvector (主数据库) + Redis 7 (缓存/队列)
- **移动端**: Flutter 3.32.1 + go_router + 响应式设计
- **后端 API**: NestJS + TypeScript 5.9.2
- **管理后台**: React + Vite + React Admin
- **容器化**: Docker + Docker Compose (1w 项目组)
- **工具链**: pnpm 10.14.0 + ESLint 9.32.0 + Node.js 22.18.0 LTS

### 服务端口分配 (1w 项目独立环境)
| 服务 | 端口 | 用途 | 访问地址 | 容器名 |
|------|------|------|----------|---------|
| **PostgreSQL** | 8182 | 主数据库 | localhost:8182 | 1w-postgres |
| **Redis** | 8183 | 缓存/队列 | localhost:8183 | 1w-redis |
| **Flutter Web** | 8080 | 移动端开发 | http://localhost:8080 | - |
| **Admin SPA** | 3100 | 管理后台 | http://localhost:3100 | - |
| **Mobile BFF** | 3101 | 移动端 API | http://localhost:3101 | - |
| **Admin BFF** | 3102 | 管理端 API | http://localhost:3102 | - |

### 数据库连接信息
```bash
# PostgreSQL (1w-postgres)
主机: localhost
端口: 8182
用户: masteryos
密码: masteryos123
数据库: masteryos

# Redis (1w-redis)  
主机: localhost
端口: 8183
密码: masteryos123
```

### 数据库架构概览
- **18个核心数据表** 基于"一万小时定律"设计
- **用户管理**: users, user_settings, user_achievements
- **技能系统**: skills, skill_categories, skill_goals
- **学习跟踪**: practice_sessions, quality_assessments
- **AI功能**: documents, document_chunks, learning_plans, ai_conversations
- **社交游戏化**: achievements, social_connections, learning_activities
- **系统管理**: notifications, system_configs

## 下一步开发规划

### 🎯 核心功能开发 (高优先级)
1. **后端 API 实现** - 基于现有数据库设计的 NestJS API
2. **用户认证系统** - JWT + OAuth 2.0 集成
3. **技能管理功能** - 创建、编辑、跟踪技能进度
4. **PDF 文档处理** - 上传、解析、向量化存储
5. **AI 学习助手** - 基于文档生成学习计划和问题

### 🔧 技术增强
1. **状态管理** - Flutter BLoC 集成
2. **API 文档** - Swagger/OpenAPI 文档
3. **测试框架** - 单元测试 + 集成测试
4. **缓存策略** - Redis 缓存实现

### 🎨 用户体验优化
1. **页面动画** - Flutter 转场动画
2. **错误处理** - 统一错误处理和用户反馈
3. **响应式设计** - 适配各种屏幕尺寸
4. **离线支持** - 本地数据缓存

## 重要注意事项

### 开发原则
- **优先使用中文** - 所有交流和文档都使用中文
- **时间标准** - 使用当前系统时间生成文档 (2025-08-04)
- **渐进开发** - 从核心功能开始，逐步增加复杂性
- **代码质量** - 严格的 TypeScript 类型检查 + ESLint 9.32.0
- **环境隔离** - 使用 1w 项目独立容器环境

### 文件管理
- **CLAUDE.md** - 项目指导文档，定期更新
- **PROJECT-STRUCTURE-2025-07-14.md** - 目录结构规范
- **scripts/init-masteryos-db.sql** - 数据库初始化脚本
- **scripts/1w-db.sh** - 数据库管理脚本
- **infrastructure/docker/** - Docker 配置文件

### 开发流程
1. 使用 TodoWrite 工具规划任务
2. 启动数据库环境: `./scripts/1w-db.sh start`
3. 优先编辑现有文件，避免创建新文件
4. 完成功能后运行 lint 和 typecheck
5. 只有明确要求时才进行 git commit

### 快速启动指南
```bash
# 1. 启动数据库环境
./scripts/1w-db.sh start

# 2. 安装项目依赖
pnpm install

# 3. 检查代码质量
pnpm run lint && pnpm run typecheck

# 4. 启动 Flutter 开发
cd apps/mobile && fvm flutter run -d web-server --web-port=8080
```

---

**项目状态**: 🎯 数据库架构完成，开始后端 API 开发  
**数据库状态**: ✅ 18个核心表 + pgvector 向量搜索 + Redis 缓存  
**最后更新**: 2025年8月4日  
**下次里程碑**: 实现用户认证和技能管理 API