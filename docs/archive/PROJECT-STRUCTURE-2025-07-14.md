# MasteryOS 项目目录结构规范

**文档日期**: 2025年7月14日  
**版本**: 1.0  
**架构模式**: Mobile+Web 混合架构

## 🚨 当前问题分析

### 发现的问题
1. **Flutter项目文件分散**: Flutter的`android/`、`ios/`、`lib/`等目录错误地出现在项目根目录
2. **重复的服务目录**: `admin-bff`、`admin-spa`、`mobile-bff`在多个位置重复
3. **结构不一致**: 缺乏统一的项目组织规范

### 影响
- 开发环境混乱
- Docker路径映射错误
- IDE识别困难
- 团队协作问题

## 📁 正确的目录结构

```
masteryos/                          # 项目根目录
├── .fvm/                           # Flutter版本管理
│   └── fvm_config.json
├── .vscode/                        # VS Code配置
│   ├── settings.json
│   ├── extensions.json
│   └── cspell.json
├── .env.example                    # 环境变量模板
├── .gitignore                      # Git忽略文件
├── package.json                    # 项目级依赖(monorepo)
├── pnpm-workspace.yaml             # pnpm工作空间配置
│
├── apps/                           # 应用程序目录
│   ├── mobile/                     # Flutter移动应用
│   │   ├── android/                # Android构建文件
│   │   ├── ios/                    # iOS构建文件
│   │   ├── lib/                    # Dart源代码
│   │   │   ├── core/               # 核心功能
│   │   │   ├── features/           # 功能模块
│   │   │   └── main.dart          # 应用入口
│   │   ├── web/                    # Web构建资源
│   │   ├── test/                   # 测试文件
│   │   ├── pubspec.yaml           # Flutter依赖
│   │   ├── analysis_options.yaml  # 代码分析配置
│   │   └── README.md              # Flutter应用文档
│   │
│   ├── admin-spa/                  # React管理后台
│   │   ├── src/                    # 源代码
│   │   │   ├── components/         # 组件
│   │   │   ├── resources/          # React-admin资源
│   │   │   ├── providers/          # 数据提供者
│   │   │   ├── hooks/              # 自定义Hooks
│   │   │   ├── utils/              # 工具函数
│   │   │   ├── App.tsx            # 应用入口
│   │   │   ├── main.tsx           # React入口
│   │   │   └── index.css          # 全局样式
│   │   ├── public/                 # 静态资源
│   │   ├── package.json           # 依赖配置
│   │   ├── vite.config.ts         # Vite配置
│   │   ├── tsconfig.json          # TypeScript配置
│   │   ├── Dockerfile.dev         # 开发环境Docker
│   │   └── README.md              # React应用文档
│   │
│   ├── mobile-bff/                 # 移动端API服务
│   │   ├── src/                    # 源代码
│   │   │   ├── auth/               # 认证模块
│   │   │   ├── users/              # 用户管理
│   │   │   ├── documents/          # 文档处理
│   │   │   ├── ai/                 # AI服务
│   │   │   ├── learning/           # 学习功能
│   │   │   ├── common/             # 公共模块
│   │   │   ├── app.module.ts      # 应用模块
│   │   │   └── main.ts            # 应用入口
│   │   ├── test/                   # 测试文件
│   │   ├── package.json           # 依赖配置
│   │   ├── nest-cli.json          # NestJS配置
│   │   ├── tsconfig.json          # TypeScript配置
│   │   ├── Dockerfile.dev         # 开发环境Docker
│   │   └── README.md              # Mobile BFF文档
│   │
│   └── admin-bff/                  # 管理端API服务
│       ├── src/                    # 源代码
│       │   ├── auth/               # 认证模块
│       │   ├── users/              # 用户管理
│       │   ├── organizations/      # 组织管理
│       │   ├── documents/          # 文档管理
│       │   ├── analytics/          # 数据分析
│       │   ├── common/             # 公共模块
│       │   ├── app.module.ts      # 应用模块
│       │   └── main.ts            # 应用入口
│       ├── test/                   # 测试文件
│       ├── package.json           # 依赖配置
│       ├── nest-cli.json          # NestJS配置
│       ├── tsconfig.json          # TypeScript配置
│       ├── Dockerfile.dev         # 开发环境Docker
│       └── README.md              # Admin BFF文档
│
├── packages/                       # 共享包目录
│   ├── shared-types/               # 共享TypeScript类型
│   │   ├── src/
│   │   ├── package.json
│   │   └── tsconfig.json
│   │
│   ├── shared-utils/               # 共享工具函数
│   │   ├── src/
│   │   ├── package.json
│   │   └── tsconfig.json
│   │
│   └── shared-constants/           # 共享常量
│       ├── src/
│       ├── package.json
│       └── tsconfig.json
│
├── infrastructure/                 # 基础设施配置
│   ├── docker/                     # Docker配置
│   │   ├── docker-compose.yml     # 生产环境
│   │   ├── docker-compose.dev.yml # 开发环境
│   │   └── nginx/                 # Nginx配置
│   │
│   ├── k8s/                       # Kubernetes配置(可选)
│   │   ├── deployments/
│   │   ├── services/
│   │   └── ingress/
│   │
│   └── terraform/                  # 云资源配置(可选)
│       ├── main.tf
│       └── variables.tf
│
├── scripts/                        # 开发脚本
│   ├── dev-start.sh               # 启动开发环境
│   ├── dev-stop.sh                # 停止开发环境
│   ├── dev-status.sh              # 检查环境状态
│   ├── dev-clean.sh               # 清理环境
│   ├── build-all.sh               # 构建所有应用
│   ├── test-all.sh                # 运行所有测试
│   └── README.md                  # 脚本使用说明
│
├── docs/                           # 项目文档
│   ├── architecture/               # 架构文档
│   │   ├── mobile-architecture.md
│   │   ├── web-architecture.md
│   │   └── api-design.md
│   │
│   ├── development/                # 开发文档
│   │   ├── setup-guide.md
│   │   ├── coding-standards.md
│   │   └── deployment-guide.md
│   │
│   ├── api/                       # API文档
│   │   ├── mobile-api.md
│   │   └── admin-api.md
│   │
│   └── plan/                      # 规划文档
│       ├── PRD.md
│       ├── Roadmap.md
│       └── User_Story_Map.md
│
├── config/                         # 配置文件
│   ├── redis.conf                 # Redis配置
│   ├── nginx.conf                 # Nginx配置
│   └── database/                  # 数据库配置
│       ├── init.sql
│       └── seed-data.sql
│
├── tests/                          # 集成测试
│   ├── e2e/                       # 端到端测试
│   ├── integration/               # 集成测试
│   └── performance/               # 性能测试
│
├── tools/                          # 开发工具
│   ├── generators/                # 代码生成器
│   ├── linters/                   # 代码检查配置
│   └── monitors/                  # 监控工具
│
├── PROJECT-STRUCTURE-2025-07-14.md # 本文档
├── README.md                       # 项目说明
├── CHANGELOG.md                    # 变更日志
└── LICENSE                         # 许可证
```

## 🔧 修复步骤

### 1. 立即修复 (紧急)

```bash
# 停止所有运行的服务
./scripts/hybrid-dev-stop.sh

# 备份当前状态
cp -r . ../masteryos-backup-$(date +%Y%m%d)

# 移动Flutter文件到正确位置
cd /Volumes/acasis/augment-projects/1w
mv android flutter-app/
mv ios flutter-app/
mv lib flutter-app/
mv web flutter-app/
mv test flutter-app/
mv pubspec.* flutter-app/
mv analysis_options.yaml flutter-app/
mv masteryos_mobile.iml flutter-app/

# 清理重复目录
rm -rf admin-bff/src/analytics admin-bff/src/auth admin-bff/src/documents admin-bff/src/organizations admin-bff/src/users
rm -rf admin-spa/src/components admin-spa/src/hooks admin-spa/src/providers admin-spa/src/resources admin-spa/src/utils
rm -rf mobile-bff/src/ai mobile-bff/src/auth mobile-bff/src/documents mobile-bff/src/learning mobile-bff/src/users
```

### 2. 重新组织目录结构

```bash
# 创建apps目录
mkdir -p apps

# 移动应用到apps目录
mv flutter-app apps/mobile
mv admin-spa apps/
mv admin-bff apps/
mv mobile-bff apps/

# 创建packages目录(共享代码)
mkdir -p packages/{shared-types,shared-utils,shared-constants}/src

# 重新组织基础设施
mkdir -p infrastructure/docker
mv docker-compose*.yml infrastructure/docker/
mv nginx infrastructure/docker/

# 重新组织配置
mkdir -p config/database
mv scripts/init-db.sql config/database/init.sql
mv scripts/seed-demo-data.sql config/database/seed-data.sql
```

### 3. 更新配置文件

```bash
# 更新Docker Compose文件中的路径
sed -i 's|flutter-app|apps/mobile|g' infrastructure/docker/docker-compose*.yml
sed -i 's|admin-spa|apps/admin-spa|g' infrastructure/docker/docker-compose*.yml
sed -i 's|admin-bff|apps/admin-bff|g' infrastructure/docker/docker-compose*.yml
sed -i 's|mobile-bff|apps/mobile-bff|g' infrastructure/docker/docker-compose*.yml

# 更新脚本中的路径
find scripts/ -name "*.sh" -exec sed -i 's|flutter-app|apps/mobile|g' {} \;
find scripts/ -name "*.sh" -exec sed -i 's|admin-spa|apps/admin-spa|g' {} \;
```

## 📋 迁移检查清单

### Flutter应用 (apps/mobile/)
- [ ] android/ 目录已移动
- [ ] ios/ 目录已移动  
- [ ] lib/ 目录已移动
- [ ] web/ 目录已移动
- [ ] pubspec.yaml 已移动
- [ ] 构建和运行正常

### React应用 (apps/admin-spa/)
- [ ] src/ 目录完整
- [ ] package.json 配置正确
- [ ] vite.config.ts 路径正确
- [ ] 构建和运行正常

### API服务 (apps/*-bff/)
- [ ] src/ 目录结构正确
- [ ] package.json 配置正确
- [ ] NestJS配置文件完整
- [ ] 构建和运行正常

### Docker环境
- [ ] 所有路径映射已更新
- [ ] 容器能正常启动
- [ ] 服务间通信正常
- [ ] 数据持久化正常

## 🚀 验证步骤

```bash
# 1. 验证Flutter应用
cd apps/mobile
fvm flutter doctor
fvm flutter pub get
fvm flutter run -d web-server --web-port=8080

# 2. 验证React应用
cd ../admin-spa
pnpm install
pnpm run dev

# 3. 验证API服务
cd ../admin-bff
pnpm install
pnpm run start:dev

# 4. 验证Docker环境
cd ../../
./scripts/hybrid-dev-start.sh
./scripts/hybrid-dev-status.sh
```

## 📝 后续优化建议

### 1. 实现Monorepo管理

```yaml
# pnpm-workspace.yaml
packages:
  - 'apps/*'
  - 'packages/*'
```

### 2. 统一构建脚本

```json
# 根目录 package.json
{
  "scripts": {
    "dev": "concurrently \"pnpm --filter admin-spa dev\" \"pnpm --filter admin-bff start:dev\"",
    "build": "pnpm --filter * build",
    "test": "pnpm --filter * test",
    "lint": "pnpm --filter * lint"
  }
}
```

### 3. 共享类型定义

```typescript
// packages/shared-types/src/index.ts
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'trainer' | 'learner';
}
```

## ⚠️ 注意事项

1. **备份重要**: 在执行重构前必须备份现有代码
2. **渐进迁移**: 建议分步骤进行，避免一次性大规模改动
3. **团队同步**: 确保所有团队成员了解新的目录结构
4. **文档更新**: 所有相关文档需要同步更新路径
5. **CI/CD更新**: 构建和部署脚本需要更新路径

---

**文档维护**: MasteryOS Team  
**最后更新**: 2025年7月14日  
**下次检查**: 2025年8月14日