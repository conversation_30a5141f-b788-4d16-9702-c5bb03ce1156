# Web端开发环境指南

## 🎯 项目结构

```
masteryos/
├── flutter-app/           # Flutter移动应用 (已配置)
├── admin-spa/             # React管理后台 (新建)
├── admin-bff/             # 管理端API (新建)
├── mobile-bff/            # 移动端API (新建)
├── docker-compose.hybrid-dev.yml  # Docker开发环境
└── scripts/               # 开发脚本
```

## 🚀 快速开始

### 1. 启动完整开发环境

```bash
# 启动所有服务 (包括数据库、API、前端)
./scripts/hybrid-dev-start.sh

# 选择启动模式:
# 1) 完整环境 (推荐) - 启动所有服务
# 2) 调试模式 - 包含 Redis Commander + pgAdmin
# 3) 仅移动端开发 - Flutter + Mobile BFF
# 4) 仅Web端开发 - React + Admin BFF
```

### 2. 服务访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **Flutter Web** | http://localhost:8080 | 移动应用Web版 |
| **React管理后台** | http://localhost:3100 | Web管理界面 |
| **Mobile BFF API** | http://localhost:3101 | 移动端API |
| **Admin BFF API** | http://localhost:3102 | 管理端API |
| **API文档** | http://localhost:3101/docs | Mobile API文档 |
| **管理API文档** | http://localhost:3102/docs | Admin API文档 |

### 3. 数据库和工具

| 工具 | 地址 | 账号密码 |
|------|------|----------|
| **PostgreSQL** | localhost:5432 | postgres/password |
| **Redis** | localhost:6379 | - |
| **pgAdmin** | http://localhost:8182 | <EMAIL>/admin123 |
| **Redis Commander** | http://localhost:8081 | - |
| **MinIO Console** | http://localhost:9101 | minioadmin/minioadmin123 |

## 🛠️ 本地开发模式

### React 管理后台开发

```bash
# 进入admin-spa目录
cd admin-spa

# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev

# 访问: http://localhost:3100
```

### NestJS API开发

```bash
# Admin BFF (管理端API)
cd admin-bff
pnpm install
pnpm run start:dev  # 端口: 3102

# Mobile BFF (移动端API)  
cd mobile-bff
pnpm install
pnpm run start:dev  # 端口: 3101
```

### Flutter开发

```bash
# Flutter移动应用
cd flutter-app
fvm flutter run -d web-server --web-port=8080
```

## 🔧 开发工作流

### 1. 前端开发流程

```bash
# 1. 启动后端服务
./scripts/hybrid-dev-start.sh
# 选择模式4: 仅Web端开发

# 2. 本地运行React前端
cd admin-spa
pnpm run dev

# 3. 开发时的热重载
# React: 自动热重载
# NestJS: 文件变化时自动重启
```

### 2. API开发流程

```bash
# 1. 启动数据库服务
./scripts/hybrid-dev-start.sh
# 选择基础模式

# 2. 本地运行API服务
cd admin-bff  # 或 mobile-bff
pnpm run start:dev

# 3. 访问API文档
# http://localhost:3102/docs
```

### 3. 全栈开发流程

```bash
# 1. 启动完整环境
./scripts/hybrid-dev-start.sh
# 选择模式1: 完整环境

# 2. 所有服务同时运行在Docker中
# 3. 代码修改时自动重载
```

## 📋 开发规范

### React前端

- **状态管理**: React Admin内置状态管理
- **UI组件**: Material-UI + React Admin
- **路由**: React Admin内置路由
- **API调用**: React Admin Data Provider
- **国际化**: 中文语言包

### NestJS后端

- **架构模式**: 模块化 + 依赖注入
- **数据库**: TypeORM + PostgreSQL
- **认证**: JWT + Passport
- **文档**: Swagger自动生成
- **验证**: class-validator + class-transformer

### 代码风格

```bash
# React前端
cd admin-spa
pnpm run lint        # ESLint检查
pnpm run lint:fix    # 自动修复

# NestJS后端
cd admin-bff  # 或 mobile-bff
pnpm run lint        # ESLint检查
pnpm run format      # Prettier格式化
```

## 🔍 调试指南

### 1. 前端调试

```bash
# React DevTools (浏览器扩展)
# 在Chrome/Firefox中安装React Developer Tools

# API请求调试
# 打开浏览器开发者工具 -> Network标签
# 查看API请求和响应
```

### 2. 后端调试

```bash
# 查看API日志
docker-compose -f docker-compose.hybrid-dev.yml logs -f admin-bff
docker-compose -f docker-compose.hybrid-dev.yml logs -f mobile-bff

# 数据库调试
# 访问 pgAdmin: http://localhost:8182
# 或使用命令行: psql -h localhost -p 5432 -U postgres -d masteryos
```

### 3. 网络调试

```bash
# 检查服务状态
./scripts/hybrid-dev-status.sh

# 检查端口占用
lsof -i :3100  # React前端
lsof -i :3101  # Mobile BFF
lsof -i :3102  # Admin BFF
```

## 🚢 部署准备

### 1. 生产构建

```bash
# React前端
cd admin-spa
pnpm run build

# NestJS后端
cd admin-bff
pnpm run build
```

### 2. Docker生产镜像

```bash
# 构建生产镜像 (待实现)
docker build -t masteryos-admin-spa:latest ./admin-spa
docker build -t masteryos-admin-bff:latest ./admin-bff
docker build -t masteryos-mobile-bff:latest ./mobile-bff
```

## 📝 开发笔记

### 技术栈对比

| 层级 | 移动端 | Web端 |
|------|--------|-------|
| **前端** | Flutter | React + React Admin |
| **API** | NestJS (Mobile BFF) | NestJS (Admin BFF) |
| **数据库** | PostgreSQL (共享) | PostgreSQL (共享) |
| **认证** | JWT | JWT |

### API设计约定

- **RESTful**: 遵循REST API设计规范
- **版本控制**: /api/v1/路径前缀
- **错误处理**: 统一错误响应格式
- **分页**: 标准分页参数
- **过滤**: 查询参数过滤

---

**最后更新**: 2025年7月14日  
**开发团队**: MasteryOS Team