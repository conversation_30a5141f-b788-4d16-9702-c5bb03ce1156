version: '3.8'

# 1w 项目数据库服务
# MasteryOS 技能学习平台的数据库环境

services:
  # PostgreSQL 数据库 (with pgvector extension)
  postgres:
    image: pgvector/pgvector:pg16
    container_name: 1w-postgres
    environment:
      POSTGRES_USER: masteryos
      POSTGRES_PASSWORD: masteryos123
      POSTGRES_DB: masteryos
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "8182:5432"
    volumes:
      - 1w_postgres_data:/var/lib/postgresql/data
      - ../../scripts/init-masteryos-db.sql:/docker-entrypoint-initdb.d/01-init.sql
    command: >
      postgres -c max_connections=200
               -c shared_buffers=256MB
               -c effective_cache_size=1GB
               -c maintenance_work_mem=64MB
               -c checkpoint_completion_target=0.9
               -c wal_buffers=16MB
               -c default_statistics_target=100
               -c random_page_cost=1.1
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U masteryos -d masteryos"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - 1w-network

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: 1w-redis
    ports:
      - "8183:6379"
    volumes:
      - 1w_redis_data:/data
      - ../../infrastructure/docker/config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass masteryos123
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "masteryos123", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - 1w-network

  # pgAdmin 数据库管理工具 (可选)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: 1w-pgadmin
    ports:
      - "8184:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: "False"
    volumes:
      - 1w_pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - 1w-network
    profiles:
      - admin-tools

  # Redis Commander 管理工具 (可选)
  redis-commander:
    image: ghcr.io/joeferner/redis-commander:latest
    container_name: 1w-redis-commander
    ports:
      - "8185:8081"
    environment:
      REDIS_HOSTS: 1w:redis:6379:0:masteryos123
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - 1w-network
    profiles:
      - admin-tools

volumes:
  1w_postgres_data:
    name: 1w-postgres-data
    driver: local
  1w_redis_data:
    name: 1w-redis-data
    driver: local
  1w_pgadmin_data:
    name: 1w-pgadmin-data
    driver: local

networks:
  1w-network:
    name: 1w-network
    driver: bridge

# 使用说明:
# 1. 启动数据库服务:
#    docker-compose -f docker-compose.database-only.yml up -d
#
# 2. 启动包含管理工具:
#    docker-compose -f docker-compose.database-only.yml --profile admin-tools up -d
#
# 3. 连接信息:
#    PostgreSQL: localhost:8182, 用户: masteryos, 密码: masteryos123, 数据库: masteryos
#    Redis: localhost:8183, 密码: masteryos123
#    pgAdmin: http://localhost:8184 (<EMAIL> / admin123)
#    Redis Commander: http://localhost:8185
#
# 4. 停止服务:
#    docker-compose -f docker-compose.database-only.yml down