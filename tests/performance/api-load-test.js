import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// 自定义指标
const errorRate = new Rate('errors');

// 测试配置
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // 缓慢增加到10个用户
    { duration: '5m', target: 10 }, // 保持10个用户5分钟
    { duration: '2m', target: 20 }, // 增加到20个用户
    { duration: '5m', target: 20 }, // 保持20个用户5分钟
    { duration: '2m', target: 0 },  // 缓慢降到0个用户
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%的请求在500ms内完成
    http_req_failed: ['rate<0.1'],    // 错误率低于10%
    errors: ['rate<0.1'],             // 自定义错误率低于10%
  },
};

const BASE_URL = __ENV.API_BASE_URL || 'http://localhost:3102';

export default function () {
  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 健康检查测试
  let response = http.get(`${BASE_URL}/health`, params);
  check(response, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);

  sleep(1);

  // 用户列表API测试
  response = http.get(`${BASE_URL}/users?page=1&pageSize=20`, params);
  check(response, {
    'users list status is 200': (r) => r.status === 200,
    'users list has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.data && Array.isArray(body.data);
      } catch (e) {
        return false;
      }
    },
    'users list response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);

  sleep(1);

  // 用户统计API测试
  response = http.get(`${BASE_URL}/users/stats`, params);
  check(response, {
    'users stats status is 200': (r) => r.status === 200,
    'users stats has required fields': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.totalUsers !== undefined && body.activeUsers !== undefined;
      } catch (e) {
        return false;
      }
    },
    'users stats response time < 500ms': (r) => r.timings.duration < 500,
  }) || errorRate.add(1);

  sleep(1);

  // 文档列表API测试
  response = http.get(`${BASE_URL}/documents?page=1&pageSize=20`, params);
  check(response, {
    'documents list status is 200': (r) => r.status === 200,
    'documents list response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);

  sleep(1);

  // 分析数据API测试
  response = http.get(`${BASE_URL}/analytics/dashboard?days=30`, params);
  check(response, {
    'analytics dashboard status is 200': (r) => r.status === 200,
    'analytics dashboard response time < 2s': (r) => r.timings.duration < 2000,
  }) || errorRate.add(1);

  sleep(2);
}

export function handleSummary(data) {
  return {
    'performance-test-results.json': JSON.stringify(data, null, 2),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

function textSummary(data, options) {
  const indent = options.indent || '';
  const enableColors = options.enableColors || false;
  
  let summary = `${indent}✓ Performance Test Results\n`;
  summary += `${indent}  Scenarios: ${data.metrics.scenarios ? Object.keys(data.metrics.scenarios).length : 0}\n`;
  summary += `${indent}  Duration: ${data.state.testRunDurationMs}ms\n`;
  summary += `${indent}  Requests: ${data.metrics.http_reqs ? data.metrics.http_reqs.count : 0}\n`;
  summary += `${indent}  Request rate: ${data.metrics.http_reqs ? (data.metrics.http_reqs.count / (data.state.testRunDurationMs / 1000)).toFixed(2) : 0}/s\n`;
  
  if (data.metrics.http_req_duration) {
    summary += `${indent}  Response time:\n`;
    summary += `${indent}    avg: ${data.metrics.http_req_duration.avg.toFixed(2)}ms\n`;
    summary += `${indent}    p95: ${data.metrics.http_req_duration.p95.toFixed(2)}ms\n`;
    summary += `${indent}    max: ${data.metrics.http_req_duration.max.toFixed(2)}ms\n`;
  }
  
  if (data.metrics.http_req_failed) {
    summary += `${indent}  Failed requests: ${(data.metrics.http_req_failed.rate * 100).toFixed(2)}%\n`;
  }
  
  return summary;
}