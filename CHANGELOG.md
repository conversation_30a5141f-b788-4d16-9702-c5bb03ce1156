# Changelog

本文档记录了 MasteryOS (1w) 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中
- 用户认证系统 (JWT + OAuth 2.0)
- 技能管理功能
- 文档处理和 AI 知识提取
- 学习记录和进度分析

## [1.0.0] - 2025-08-04

### 新增
- 🎉 **项目初始化**: 完整的 Monorepo 架构
- 🏗️ **基础架构**: 标准化的项目目录结构
- 🐳 **Docker 环境**: 独立的开发和数据库环境
- 📱 **Flutter 移动端**: 基础应用框架和导航系统
- 🔧 **后端 API**: NestJS 服务框架 (Admin BFF + Mobile BFF)
- 🖥️ **管理后台**: React Admin 基础界面
- 🗄️ **数据库设计**: 18个核心数据表，支持技能追踪体系
- 📚 **文档体系**: 完整的项目文档和开发指南
- ✅ **代码质量**: ESLint + TypeScript + Prettier 配置
- 🚀 **开发工具**: 自动化脚本和开发环境管理

### 技术栈
- **前端移动**: Flutter 3.32.1
- **前端管理**: React 18.3.1 + Vite
- **后端 API**: NestJS 10.4.9 + TypeScript 5.8.3
- **数据库**: PostgreSQL 16 + pgvector + Redis 7
- **包管理**: pnpm 10.14.0 + Workspaces
- **容器化**: Docker Compose
- **代码质量**: ESLint 9.32.0 + Prettier

### 基础设施
- **服务端口配置**: 
  - Flutter Web (8080)
  - PostgreSQL (8182) 
  - Redis (8183)
  - Admin BFF (3102)
  - Mobile BFF (3101)
- **Docker 容器**: 1w 项目独立容器组
- **开发脚本**: 数据库管理、环境启动等自动化工具

### 文档
- 📖 完整的项目文档结构
- 🚀 安装和快速启动指南  
- 🏗️ 系统架构和技术栈文档
- 👨‍💻 开发规范和贡献指南
- 📋 项目状态和里程碑规划

### 数据库架构
- **用户管理**: 用户、组织、角色权限
- **技能系统**: 技能、子技能、技能关系
- **学习记录**: 学习会话、时长追踪、进度统计
- **文档管理**: 文档存储、标签分类、AI 处理
- **社交功能**: 用户关注、动态、评论
- **游戏化**: 成就系统、等级、经验值
- **系统管理**: 配置、日志、通知

### 已修复
- ✅ TypeScript 版本兼容性问题 (26+ 错误)
- ✅ ESLint 配置和代码质量问题
- ✅ process.env 索引签名访问问题
- ✅ Admin SPA 模块导入和类型定义
- ✅ DataProvider 类型兼容性
- ✅ 浮动 Promise 和未使用参数警告

## [0.1.0] - 2025-07-14

### 新增
- 🎯 项目概念和需求分析
- 📋 产品需求文档 (PRD)
- 🗺️ 用户故事地图
- 📅 产品路线图规划
- 🏗️ 技术架构设计

---

## 版本说明

### 版本号格式
遵循 [语义化版本](https://semver.org/lang/zh-CN/) 格式：`主版本号.次版本号.修订号`

- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 变更类型
- `新增` - 新功能
- `变更` - 对现有功能的变更
- `弃用` - 即将移除的功能
- `移除` - 已移除的功能
- `修复` - 问题修复
- `安全` - 安全相关修复

### 发布节奏
- **主版本**: 重大架构变更或破坏性更新
- **次版本**: 每月发布，包含新功能和改进
- **修订版**: 按需发布，主要是 Bug 修复

---

**注**: 此项目目前处于积极开发阶段，API 可能会有较大变动。建议关注版本更新，及时调整代码。